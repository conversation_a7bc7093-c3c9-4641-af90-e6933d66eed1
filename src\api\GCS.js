import request from '@/utils/request'

export function createWorkerStory(params) {//添加故事
  return request({
    url: 'oe_createWorkerStory_.csp',
    method: 'get',
    params
  })
}

export function workerStorys(params) {//获取公司列表
  return request({
    url: 'oe_workerStorys_.csp',
    method: 'get',
    params
  })
}

export function delStorys(params) {//删除故事
  return request({
    url: 'oe_delStorys_.csp ',
    method: 'get',
    params
  })
}
