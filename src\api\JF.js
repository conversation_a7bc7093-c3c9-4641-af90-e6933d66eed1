import request from '@/utils/request'

export function createIntergralCate(params) {//添加礼品分类
  return request({
    url: 'oe_createIntergralCate_.csp',
    method: 'get',
    params
  })
}

export function delIntergralCate(params) {//删除分类
  return request({
    url: 'oe_delIntergralCate_.csp',
    method: 'get',
    params
  })
}
export function getIntergralCate(params) {//查询分类
  return request({
    url: 'oe_getIntergralCate_.csp',
    method: 'get',
    params
  })
}

export function allCates(params) {//获取所有分类
  return request({
    url: 'oe_allCates_.csp',
    method: 'get',
    params
  })
}

export function intergralGoods(params) {//获取商品管理
  return request({
    url: 'oe_intergralGoods_.csp',
    method: 'get',
    params
  })
}

export function createIntergralGoods(params) {//添加商品管理
  return request({
    url: 'oe_createIntergralGoods_.csp',
    method: 'get',
    params
  })
}

export function delIntergralGoods(params) {//删除商品管理
  return request({
    url: 'oe_delIntergralGoods_.csp',
    method: 'get',
    params
  })
}

export function userOrders(params) {//积分订单管理
  return request({
    url: 'oe_userOrders_.csp',
    method: 'get',
    params
  })
}

export function orderToSend(params) {//发货
  return request({
    url: 'oe_orderToSend_.csp',
    method: 'get',
    params
  })
}

export function rechargeRecords(params) {//充值记录
  return request({
    url: 'oe_rechargeRecords_.csp',
    method: 'get',
    params
  })
}