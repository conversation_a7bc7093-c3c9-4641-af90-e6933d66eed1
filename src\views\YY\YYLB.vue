<template>
  <div class="app-container">
    <el-tabs type="card" v-model="activeName" @tab-click="handleClick">
      <el-tab-pane v-for="(tab, index) in DDSTUIS" :key="tab.id" :label="tab.label" :name="tab.name">
        <!-- 添加按钮 -->
        <el-form :inline="true" class="user-search">
          <el-form-item>
            <!-- <el-button size="mini" @click="handleEdit1" type="primary">添加</el-button> -->

          </el-form-item>

          <el-form-item>
            <el-input size="mini" placeholder="请输入内容" v-model="input4" class="el-input_ss1">
              <el-button size="mini" slot="append" icon="el-icon-search" @click="change3"></el-button>
            </el-input>
          </el-form-item>
          <el-form-item>
            <!-- <el-button size="mini" type="primary" icon="el-icon-plus" @click="increase()">添加</el-button> -->

            <el-button size="mini" type="primary" @click="Reload" icon="el-icon-refresh-left">重新加载</el-button>
            <!-- <el-button @click="leading">批量导出</el-button> -->




          </el-form-item>
        </el-form>
        <!-- 表格 -->
        <el-table :data="data" border fit highlight-current-row>
          <el-table-column align="center" label="序号">
        <template slot-scope="scope">{{ scope.$index + 1 }}</template>
      </el-table-column>
      <el-table-column align="center" label="微信名称">
        <template slot-scope="scope">{{ scope.row.wxName }}</template>
      </el-table-column>
      <el-table-column align="center" label="客户名称" width="200">
        <template slot-scope="scope">{{ scope.row.name }}</template>

        <!-- <template slot-scope="scope">
                  <div v-if="scope.row.cateId == '1'">首页</div>
                  <div v-if="scope.row.cateId == '2'">个人中心</div>
                  <div v-if="scope.row.cateId == '3'">其他</div>
              </template> -->
      </el-table-column>
     
      <el-table-column align="center" label="手机号" width="200">
        <template slot-scope="scope">{{ scope.row.phone }}</template>


      </el-table-column> <el-table-column align="center" label="城市" width="200">
        <template slot-scope="scope">{{ scope.row.city }}</template>


      </el-table-column>
      <el-table-column align="center" label="商家名称" width="200">
        <template slot-scope="scope">{{ scope.row.storeName }}</template>
      </el-table-column>
      <el-table-column align="center" label="预约时间" width="200">
        <template slot-scope="scope">{{ scope.row.date }}</template>
      </el-table-column>
   
      <el-table-column align="center" label="创建时间" width="300">
        <template slot-scope="scope">{{ scope.row.createTime }}</template>
      </el-table-column> 
      <el-table-column align="center" label="预约进度" fixed="right">
      

           <template slot-scope="scope">
            <el-button size="mini"  v-if="scope.row.status == '1'" type="success">已处理</el-button>
            <el-button size="mini"  v-if="scope.row.status == '0'" type="danger">未处理</el-button>
                  
              </template>
      </el-table-column>
     
        </el-table>
        <el-row>
          <el-pagination :current-page.sync="currentPage" background style="margin-top:30px" :page-size="pageSize"
            layout="total,prev,pager,next,jumper" :total="total" @current-change="handleCurrentChange1" />

        </el-row>

        <!-- 在这里插入您想要循环显示的内容 -->
      </el-tab-pane>

    </el-tabs>
    <!-- 查看 -->
    <el-dialog :title="title" :visible.sync="editFormVisible" @close="closeDialog" width="50%">
      <el-form label-position="rigth" label-width="100px">
        <el-form-item label="订单编号:">
          <div>{{ special.orderNum }}</div>

        </el-form-item>
        <el-form-item label="订单状态:">
          <el-tag v-if="special.status == '1'">已下单</el-tag>
          <el-tag v-if="special.status == '2'" type="danger">已发货</el-tag>
          <el-tag v-if="special.status == '0'" type="warning">待付款</el-tag>
        </el-form-item>
        <el-form-item label="礼品名称:">

          <div>{{ special.proInfo }}</div>

        </el-form-item>
        <el-form-item label="所需积分:">

          <div>{{ special.money }}</div>

        </el-form-item>

        <el-form-item label="购买数量:">

          <div>{{ special.number }}</div>

        </el-form-item>

        <el-form-item label="封面图:">
          <div style="max-height: 60px;max-width:60px; padding: 5px">
            <img width="100%" :src="special.productImage" alt>


          </div>

        </el-form-item>


        <el-form-item label="收件人:">


          <div>{{ special.name }}</div>

        </el-form-item>


        <el-form-item label="收件人电话:">


          <div>{{ special.phone }}</div>

        </el-form-item>

        <el-form-item label="收件人地址:">


          <div>{{ special.address }}</div>

        </el-form-item>


        <el-form-item label="下单时间:">


          <div>{{ special.createTime }}</div>

        </el-form-item>
        <el-form-item label="所属门店:">


          <div>{{ special.storeName }}</div>

        </el-form-item>


      </el-form>
      <div slot="footer" class="dialog-footer">
        <!-- <el-button size="mini" @click="closeDialog">取消</el-button> -->
        <!-- <el-button size="mini" type="primary" class="title" @click="submitForm()">保存</el-button> -->
      </div>
    </el-dialog>

    <!-- 发货 -->
    <el-dialog title="发货" :visible.sync="editFormVisible1" @close="closeDialog1" width="50%">
      <el-form label-position="rigth" label-width="100px">


        <el-form-item label="快递公司:">
          <el-input style="width: 100%;" size="mini" v-model="ShipmentData.eprCp" auto-complete="off"
            placeholder="请输入快递公司"></el-input>
        </el-form-item>



        <el-form-item label="物流单号:">
          <el-input style="width: 100%;" size="mini" v-model="ShipmentData.lgtNum" auto-complete="off"
            placeholder="请输入物流单号"></el-input>
        </el-form-item>




      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" class="title" @click="Confirmshipment()">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import { yuyues, orderToSend } from '@/api/JF'
import { yuyues } from '@/api/YY'
import { upload } from '@/api/upload'
import { Loading } from 'element-ui';

export default {
  data() {
    return {
      activeName: 'DFH',
      editFormVisible: false,
      editFormVisible1: false,
      dialogVisibleImg: false,
      currentPage: 1,
      pageSize: 10,
      total: 1,
      data: [],
      title: '订单详情',
      special: {
        id: '',
        productImage: "",
        status: 1,
        sortId: '',
        remark: '',
        integralNum: '',
        productStock: '',
        proInfo: '',
        peoNum: "",
        quota: '',
        productCate: '',
        cateId: '',
        detail: '',
        sort: '',
      },
      modulelist: [

      ],
      SEDJJXCX_LHFW_UserInformation_HT: {},
      bookUrllist: [],
      bookUrllist1: [],
      input3: '',
      input4: '',
      type: 1,
      ShipmentData: {
        eprCp: "",
        lgtNum: "",
        status: 2,
        id: ''
      },
      DDSTUIS: [
        { label: '预约到店', name: 'DFH', id: 1 },
        { label: '预约工地', name: 'YFH', id: 2 },

      ],
    }
  },
  created() {
    const SEDJJXCX_LHFW_UserInformation_HT = JSON.parse(decodeURIComponent(window.localStorage.getItem('SEDJJXCX_LHFW_UserInformation_HT')))
    this.SEDJJXCX_LHFW_UserInformation_HT = SEDJJXCX_LHFW_UserInformation_HT;
    // this.fetchData1()
    this.fetchData()
  },
  methods: {
    Viewlogistics(e) {
      // window.location.href='https://www.baidu.com/s?wd=' + e
      // window.open='https://www.baidu.com/s?wd=' + e
      window.open(`https://www.baidu.com/s?wd=${e}`, '_blank')
    },
    closeDialog1() {
      this.ShipmentData.eprCp = ''
      this.ShipmentData.lgtNum = ''
    },
    Confirmshipment() {
      if (this.ShipmentData.eprCp == '') {
        this.$message({
          showClose: true,
          message: '请输入快递公司！',
          type: 'warning'
        });
        return
      }
      if (this.ShipmentData.lgtNum == '') {
        this.$message({
          showClose: true,
          message: '请输入物流单号！',
          type: 'warning'
        });
        return
      }


      const params = {
        eprCp: this.ShipmentData.eprCp,
        lgtNum: this.ShipmentData.lgtNum,
        id: this.ShipmentData.id,
        status: this.ShipmentData.status,
        dbName: window.localStorage.getItem('JJHTDBnmame')
      };
      orderToSend(params).then(response => {
        if (response.data.code == '1') {
          this.editFormVisible1 = false
          this.ShipmentData.eprCp = '';
          this.ShipmentData.lgtNum = '';
          this.fetchData()
          this.$message({
            type: 'success',
            message: '发货成功！'
          })
        } else {
          this.$message({
            type: 'warning',
            message: '发货失败！'
          })
        }
      })

    },
    sendoutgoods(index, row) {
      console.log(row);
      this.editFormVisible1 = true;
      this.ShipmentData.id = row.id;

    },
    handleClick(tab, event) {
      if (tab.label == '预约到店') {
        this.type = 1;
        const params = {
                              dbName: window.localStorage.getItem('JJHTDBnmame'),

          curPage: 1,
          type: this.type

        }
        yuyues(params).then(response => {
          this.total = response.data.totalNum
          if (response.data.code == '1') {
            this.data = response.data.list
          } else {
            this.data = response.data.list

          }
        })
      } else {
        this.type = 2;
        const params = {
                              dbName: window.localStorage.getItem('JJHTDBnmame'),

          curPage: 1,
          type: this.type

        }
        yuyues(params).then(response => {
          this.total = response.data.totalNum
          if (response.data.code == '1') {
            this.data = response.data.list
          } else {
            this.data = response.data.list

          }
        })
      }
    },
    Reload() {
      this.input3 = ''
      this.input4 = '';
this.activeName='DFH'
      const params = {
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: 1,
        type:1
      }
      yuyues(params).then(response => {
        this.total = response.data.totalNum

        if (response.data.code == '1') {
          this.data = response.data.list
        } else {
          this.data = response.data.list

        }
      }).catch((err) => {
        console.log();
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    change3() {
      const params = {
        keyword: this.input4,
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: 1,
        type:this.type
      }
      if (this.input4) {
        params.cateId = this.input3;
      }

      yuyues(params).then(response => {
        this.total = response.data.totalNum

        if (response.data.code == '1') {
          this.data = response.data.list
        } else {
          this.data = response.data.list
        }
      }).catch((err) => {
        console.log();
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    change2() {
      const params = {
        role: this.input3,
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: 1
      }
      if (this.input4) {
        params.keyword = this.input4;
      }

      yuyues(params).then(response => {
        if (response.data.code == '1') {
          this.modulelist = response.data.records
        } else {
          this.modulelist = response.data.records
          this.input4 = ''
        }
      }).catch((err) => {
        console.log();
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    fetchData1(val = 1) {
      const params = {
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: val,
        type: 1
      }
      yuyues(params).then(response => {
        if (response.data.code == '1') {
          this.modulelist = response.data.records
        } else {
          this.modulelist = response.data.records

        }
      }).catch((err) => {
        console.log();
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    handleCurrentChange1(val) {
      this.fetchData(val)
    },
    fetchData(val = 1) {
      const params = {
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: val,
        type: 1

      }
      yuyues(params).then(response => {
        this.total = response.data.totalNum
        if (response.data.code == '1') {
          this.data = response.data.list
        } else {
          this.data = response.data.list

        }
      })
    },
    handleEdit1() {
      // this.title = '添加';
      this.editFormVisible = true;
      this.special.id = '';
    },
    handleEdit(index, row) {
      this.editFormVisible = true;
      this.special = row;
      if (row.productImage) {
        this.bookUrllist = [
          {
            url: row.productImage
          }
        ]
      }


      return
      if (row != undefined && row != 'undefined') {
        this.title = '修改'
        this.special.id = row.id
        this.special.name = row.name
      } else {
        this.title = '添加'
        this.special.id = ''
      }
    },
    // 关闭编辑、增加弹出框
    closeDialog() {

      this.editFormVisible = false;
      this.deptName = '';
      this.special = {};
    },
    // 编辑、增加页面保存方法
    submitForm() {
      if (this.title == '添加') {
        this.special.productCate = this.modulelist.find(item => item.id == this.special.cateId).cateName;
      }

      if (this.special.detail == '') {
        this.$message({
          showClose: true,
          message: '请输入名称！',
          type: 'warning'
        });
        return
      }

      if (this.special.integralNum == '') {
        this.$message({
          showClose: true,
          message: '请输入所需积分！',
          type: 'warning'
        });
        return
      }
      if (this.special.sort == '') {
        this.$message({
          showClose: true,
          message: '请输入排序！',
          type: 'warning'
        });
        return
      }
      if (this.special.cateId == '') {
        this.$message({
          showClose: true,
          message: '请选择模块！',
          type: 'warning'
        });
        return
      }

      if (this.special.quota == '') {
        this.$message({
          showClose: true,
          message: '请输入限购数量！',
          type: 'warning'
        });
        return
      }
      if (this.special.detail == '') {
        this.$message({
          showClose: true,
          message: '请输入详情！',
          type: 'warning'
        });
        return
      }
      if (this.special.productStock == '') {
        this.$message({
          showClose: true,
          message: '请输入库存量！',
          type: 'warning'
        });
        return
      }
      if (this.special.productImage == '') {
        this.$message({
          showClose: true,
          message: '请上传产品封面图！',
          type: 'warning'
        });
        return
      }



      const params = {
        integralNum: this.special.integralNum,
        id: this.special.id,
        sort: this.special.sort,
        cateId: this.special.cateId,
        status: this.special.status,
        peoNum: this.special.peoNum,
        quota: this.special.quota,
        detail: this.special.detail,
        productStock: this.special.productStock,
        productImage: this.special.productImage,
        productCate: this.special.productCate,
        proInfo: this.special.proInfo,
        dbName: window.localStorage.getItem('JJHTDBnmame')
      };
      createIntergralGoods(params).then(response => {
        if (response.data.code == '1') {
          this.editFormVisible = false
          this.special.sortId = '';
          this.special.remark = '';
          this.bookUrllist = []
          this.fetchData()
          this.$message({
            type: 'success',
            message: this.title == '修改' ? '修改成功！' : '创建成功！'
          })
        } else {
          this.$message({
            type: 'warning',
            message: '创建失败！'
          })
        }
      })


    },

    deleteUser(index, row) {
      this.$confirm('此操作将永久删除, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const params = {
          id: row.id,
                              dbName: window.localStorage.getItem('JJHTDBnmame'),

        }; delIntergralGoods(params).then(response => {
          if (response.data.code == '1') {
            this.fetchData()
          }
        })
        this.$message({
          type: 'success',
          message: '删除成功!'
        });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });

    },
    handlePreview(file) {
      // 放大
      this.special.productImage = file.productImage
      this.dialogVisibleImg = true
    },
    handleRemove1(file, fileList) {
      // 移除
      const { uid } = file
      const { powUrl } = this.form
      const newPowUrl = powUrl.filter(v => {
        return uid !== powUrl.uid
      })
    },
    handleSmallPicSuccess(res, file, fileList) {
      if (file && file.length == 2) {
        file.shift()
      }
      this.uploadImg(res, 1);

    },
    handleSmallPicSuccess1(res, file, fileList) {
      if (file && file.length == 2) {
        file.shift()
      }
      this.uploadImg(res, 2);

    }
    , uploadImg(file, type) {
      this.loading = Loading.service({
        lock: true,
        text: '图片上传中........',
        // background: 'rgba(0, 0, 0, 0.7)',
        target: document.querySelector('#loadingDiv')
      })
      let formData = new FormData()
      formData.append('file', file.raw)
      upload(formData).then(response => {
        setTimeout(() => {
          this.loading.close()

        }, 2000);
        if (type == 1) {
          this.special.productImage = response.data.yunUrl;

        } else {
          this.special.detail = response.data.yunUrl;

        }

      }).catch((err) => {
        console.log(err);

      });


    },
  }
}
</script>

<style lang="scss" scoped>
.el-row {
  margin-bottom: 20px;
}

.el-row:last-child {
  margin-bottom: 0;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.el-button--medium {
  margin-top: 10px;
  margin-left: 600px;
  height: 45px;
  width: 100px;
}

.el-input {
  width: 300px;
}

// .el-input.inp {
//   width: auto !important;
// }
.el-input_ss1 {

  margin-top: 6px;
}
</style>
