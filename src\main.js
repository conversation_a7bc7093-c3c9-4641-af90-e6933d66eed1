import Vue from 'vue'

import 'normalize.css/normalize.css' // A modern alternative to CSS resets

import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import locale from 'element-ui/lib/locale/lang/zh-CN' // lang i18n

import '@/styles/index.scss' // global css

import App from './App'
import store from './store'
import router from './router'

import '@/icons' // icon
import '@/permission' // permission control
// 引入echarts
import echarts from 'echarts'
// md5
import md5 from 'js-md5';

// 引入axios
import './utils/axios'
Vue.prototype.$echarts = echarts 
Vue.prototype.$md5 = md5;

import VueParticles from 'vue-particles'

Vue.prototype.$dbList = 'sedysgd_cn,sedysgd_en';

Vue.use(VueParticles)


//高德地图配置安全密钥
window._AMapSecurityConfig = {
	securityJsCode: '0837603d5f5ea57673273626dc2be5d8' //*  安全密钥
}
/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online ! ! !
 */

// set ElementUI lang to EN
Vue.use(ElementUI, { locale })
// 如果想要中文版 element-ui，按如下方式声明
// Vue.use(ElementUI)

Vue.config.productionTip = false

new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})
