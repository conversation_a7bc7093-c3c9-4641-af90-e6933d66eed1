<template>
    <div class="app-container">

        <!-- 添加按钮 -->
        <el-form :inline="true" class="user-search">
            <el-form-item>
                <el-button size="mini" @click="handleEdit" type="primary">添加</el-button>

            </el-form-item>
        </el-form>
        <!-- 表格 -->
        <el-table :data="data" border fit highlight-current-row>
            <el-table-column align="center" label="序号">
                <template slot-scope="scope">{{scope.$index + 1}}</template>
            </el-table-column>
            <el-table-column align="center" prop="standard" label="步骤">
                <template slot-scope="scope">{{ scope.row.step }}</template>

            </el-table-column>
            <el-table-column align="center" prop="standard" label="标准">
                <template slot-scope="scope">{{ scope.row.standard }}</template>

            </el-table-column>
        
            <el-table-column align="center" prop="standard" label="排序">
                <template slot-scope="scope">{{ scope.row.sort }}</template>

            </el-table-column>
          
           
            <el-table-column  label="操作" align="center" width="200">
                <template slot-scope="scope">
                    <el-button size="mini" @click="handleEdit1(scope.$index, scope.row)">编辑</el-button>

                    <el-button size="mini" @click="deleteUser(scope.$index, scope.row)" type="danger">删除</el-button>

                </template>
            </el-table-column>
            </el-table-column>
        </el-table>
        <el-pagination :current-page.sync="currentPage" background style="margin-top:30px" :page-size="pageSize"
        layout="total,prev,pager,next,jumper" :total="total" @current-change="handleCurrentChange1" />

        <!-- 编辑界面 -->
        <el-dialog  :title="title" :visible.sync="editFormVisible" width="50%" @close="closeDialog">
            <el-form label-width="120px">
               
                <el-form-item label="步骤:" prop="authority.step">
                    <el-radio-group v-model="authority.step">
                        <el-radio :label="'铺贴前'">铺贴前</el-radio>
                        <el-radio :label="'铺贴中'">铺贴中</el-radio>
                        <el-radio :label="'铺贴后'">铺贴后</el-radio>

                    </el-radio-group>
                </el-form-item>
                <el-form-item label="标准:" >
                    <el-input type="textarea"  @input="changeMessage" size="mini" v-model="authority.standard" auto-complete="off"
                        placeholder="请输入标准"></el-input>
                </el-form-item>
                <el-form-item label="排序:" >
                    <el-input @input="changeMessage" size="mini" v-model="authority.sort" auto-complete="off"
                        placeholder="请输入排序"></el-input>
                </el-form-item>
                <el-form-item label="标准内容:" >
                    <wangEditor v-model="wangEditorDetail" @change="wangEditorChange"></wangEditor>

                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button size="mini" @click="closeDialog">取消</el-button>
                <el-button size="mini" type="primary" class="title" @click="submitForm()">保存</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import {createStandard ,workStandards ,delStandard} from '@/api/XM'
import wangEditor from "@/components/wangEditor/wangEditor.vue";

import { log } from 'console'

export default {
    components: {
    wangEditor
  },
    data() {
        return {
            isClear: false,//设置为true的时候，这个可以用this.wangEditorDetail=''来替代
      wangEditorDetail: "",
            editFormVisible: false,
            data: [],
            currentPage:1,
            total:1,
            pageSize:10,
            title: '添加',
            authority: {
                standard:'',
                id: '',
                step:'铺贴前',
                content:'',
                sort:''
            },
            SEDJJXCX_LHFW_UserInformation_HT:{}
        }
    },
    created() {
        const SEDJJXCX_LHFW_UserInformation_HT = JSON.parse(decodeURIComponent(window.localStorage.getItem('SEDJJXCX_LHFW_UserInformation_HT')))
    this.SEDJJXCX_LHFW_UserInformation_HT = SEDJJXCX_LHFW_UserInformation_HT
    this.fetchData()
    },
    methods: {
        wangEditorChange(val) {
      this.authority.content=val;
    },
        changeMessage(){
	//强制刷新渲染
	this.$forceUpdate()
},
        fetchData(val = 1) {
            const params = {
                                    dbName: window.localStorage.getItem('JJHTDBnmame'),

                curPage: val
            }
            workStandards(params).then(response => {
                this.total=response.data.totalNum
                if (response.data.code == '1') {
                    this.data = response.data.list
                }else{
                    this.data = response.data.list

                }
            })
        },
        handleCurrentChange1(val){
this.fetchData(val)
        },
        handleEdit: function (index, row) {

            this.editFormVisible = true;
          
                this.title = '添加'
                this.authority.id = ''
                this.authority.sort = ''
            
        },
        handleEdit1: function (index, row) {

this.editFormVisible = true;

    this.title = '修改';
 
    this.authority=row;
    this.wangEditorDetail=row.content;

},
        
        // 关闭编辑、增加弹出框
        closeDialog() {
            this.wangEditorDetail=''
            this.editFormVisible = false;
            this.deptName = '';
            this.authority = {
                content:'',
                standard:'',
                step:'铺贴前',
                sort:''

            };
        },
        // 编辑、增加页面保存方法
        submitForm() {
          
                const params = {
                    step: this.authority.step,
                    standard: this.authority.standard,
                    sort: this.authority.sort,
                    content: this.authority.content,
                                        dbName: window.localStorage.getItem('JJHTDBnmame'),

                    id:this.title == '修改' ?  this.authority.id : ''
                }
               
                createStandard(params).then(response => {
                    if (response.data.code == '1') {
                        this.editFormVisible = false
                        this.authority.standard = '';
                        this.authority.step = '';
                        this.fetchData()
                        this.$message({
                            type: 'success',
                            message:  this.title == '修改' ?'修改成功！' : '创建成功！'

                        })
                    } else {
                        this.$message({
                            type: 'warning',
                            message: '创建失败！'
                        })
                    }
                })
           

        },
        deleteUser(index, row) {
            this.$confirm('此操作将永久删除, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                const params = {
                    id: row.id,
                                        dbName: window.localStorage.getItem('JJHTDBnmame'),

                }; delStandard(params).then(response => {
                    if (response.data.code == '1') {
                        this.fetchData()
                    }
                })
                this.$message({
                    type: 'success',
                    message: '删除成功!'
                });
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });

        }
    }
}
</script>

<style lang="scss" scoped>
.el-row {
    margin-bottom: 20px;
}

.el-row:last-child {
    margin-bottom: 0;
}

.avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.avatar-uploader .el-upload:hover {
    border-color: #409eff;
}

.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}

.avatar {
    width: 178px;
    height: 178px;
    display: block;
}

.el-button--medium {
    margin-top: 10px;
    margin-left: 600px;
    height: 45px;
    width: 100px;
}

.el-input {
    width: 200px;
}

// .el-input.inp {
//   width: auto !important;
// }
</style>
