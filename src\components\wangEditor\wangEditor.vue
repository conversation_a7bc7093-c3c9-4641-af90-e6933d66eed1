<template lang="html">
  <div class="wangeditor" v-loading.fullscreen.lock="fullscreenLoading" element-loading-text="上传中">
    <div ref="toolbar" class="toolbar"></div>
    <div ref="wangeditor" class="text"></div>
  </div>
</template>
   
<script>
import E from "wangeditor";
import { log } from 'console';
export default {
  data() {
    return {
      wangEditor: null,
      wangEditorInfo: null,
      fullscreenLoading: false,
      baseUrl : process.env.VUE_APP_BASE_API + "/uploadCloud?filename=11111",
    };
  },
  model: {
    prop: "value",
    event: "change"
  },
  props: {
    value: {
      type: String,
      default: ""
    },
    isClear: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: String,
      default: ""
    }
  },
  watch: {
    isClear(val) {
      // 触发清除文本域内容
      if (val) {
        this.wangEditor.txt.clear();
        this.wangEditorInfo = null;
      }
    },
    disabled: function (val) {
      console.log(val);
      if (val == 'notEditBasicInfo') {
        this.editor.enable()

      } else {
        this.editor.disable()

      }
    },
    value: function (value) {
      if (value !== this.wangEditor.txt.html()) {
        this.isClear = false;
        this.wangEditor.txt.html(this.value); //value为编辑框输入的内容，这里我监听了一下值，当父组件调用得时候，如果给value赋值了，子组件将会显示父组件赋给的值
      }
    }
  },
  mounted() {
    this.initEditor();
    this.wangEditor.txt.html(this.value);
  },
  methods: {
    initEditor() {
      this.wangEditor = new E(this.$refs.toolbar, this.$refs.wangeditor);
      this.wangEditor.customConfig = this.wangEditor.customConfig ? this.wangEditor.customConfig : this.wangEditor.config
      this.wangEditor.customConfig.uploadImgShowBase64 = false; // base64存储图片（推荐）
      this.wangEditor.customConfig.uploadImgServer = this.baseUrl; // 配置服务器端地址（不推荐）
      this.wangEditor.customConfig.uploadImgHeaders = {}; // 自定义header
      this.wangEditor.customConfig.uploadFileName = "file"; // 后端接受上传文件的参数名
      this.wangEditor.customConfig.uploadImgMaxSize = 10 * 1024 * 1024; // 将图片大小限制为（默认最大支持2M）
      this.wangEditor.customConfig.uploadImgMaxLength = 9; // 限制一次最多上传6张图片
      this.wangEditor.customConfig.uploadImgTimeout = 1 * 60 * 1000; // 设置超时时间（默认1分钟）

      // 配置菜单
      this.wangEditor.customConfig.menus = [
        "head", // 标题
        "bold", // 粗体
        "fontSize", // 字号
        "fontName", // 字体
        "italic", // 斜体
        "underline", // 下划线
        "strikeThrough", // 删除线
        "foreColor", // 文字颜色
        "backColor", // 背景颜色
        "link", // 插入链接
        "list", // 列表
        "justify", // 对齐方式
        "quote", // 引用
        "emoticon", // 表情
        "image", // 插入图片
        "table", // 表格
        "video", // 插入视频
        "code", // 插入代码
        "undo", // 撤销
        "redo", // 重复
        "fullscreen" // 全屏
      ];
      this.wangEditor.customConfig.uploadImgHooks = {
        fail: (xhr, editor, result) => {
          // 插入图片失败回调
          console.log(xhr)
          console.log(editor)
          console.log(result)

        },
        success: (xhr, editor, result) => {
          console.log(xhr)
          console.log(editor)
          // 图片上传成功回调
        },
        timeout: (xhr, editor) => {
          // 网络超时的回调
        },
        error: (xhr, editor) => {
          console.log(xhr)
          console.log(editor)
          // 图片上传错误的回调
        },
        customInsert: (insertImg, result, editor) => {
          // insertImg(url);
        }
      };
      this.wangEditor.customConfig.customUploadImg = (files, insert) => {
        this.fullscreenLoading = true;
        var attr = []
        for (var i = 0; i < files.length; i++) {
          var formData = new FormData();
          formData.append("file", files[i], files[i].name);
          this.axios.post(this.baseUrl, formData, {
          // this.axios.post('http://*************:9528/api//uploadCloud?filename=11111', formData, {
            // 上传图片接口
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          }).then(da => {
            attr.push(da.data.yunUrl);
            if(files.length == attr.length){
            // attr.reverse();

            }
            // window.localStorage.setItem('images', JSON.stringify(attr));
            // let images = JSON.parse(localStorage.getItem('images'));
            // console.log(images);
            for (var j = 0; j < attr.length; j++) {
              if(files.length == attr.length){
                insert(attr[j]);
            this.fullscreenLoading = false;
              }
            }

            // insert(da.data.yunUrl);

          })
        }

      }
      this.wangEditor.customConfig.onchange = html => {
        this.wangEditorInfo = html;
        this.$emit("change", this.wangEditorInfo); // 将内容同步到父组件中
      };
      // 创建富文本编辑器
      this.wangEditor.create();
      if (this.disabled == 'notEditBasicInfo') {
        this.wangEditor.disable()
      } else {
        this.wangEditor.enable()

      }
    }
  }
};
</script>
   
<style lang="scss">
.wangeditor {
  border: 1px solid #e6e6e6;
  box-sizing: border-box;
  width: 100%;

  .toolbar {
    border-bottom: 1px solid #e6e6e6;
    box-sizing: border-box;
  }


}

.text {
  min-height: 300px;
  min-width: 500px;
}

.text img {
  width: 100%;
  height: 100%;
}
</style>
  