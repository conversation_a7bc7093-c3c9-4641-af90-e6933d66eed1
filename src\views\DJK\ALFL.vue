<template>
  <div class="dashboard-container">
    <el-row>
      <el-col :span="4" style="max-height: 90vh;
  overflow-y: auto;">
        <el-tree @node-click="handleNodeClick" class="filter-tree" :data="data" :props="defaultProps" default-expand-all
          :filter-node-method="filterNode" node-key="key" :highlight-current='true' :expand-on-click-node="false"
          ref="tree">
        </el-tree>
      </el-col>
      <el-col :span="18" v-if="Subitemshow" style="height: 700px;overflow-y: auto; margin-left: 80px;">
        <el-form label-position="left" label-width="80px">
          <el-form-item label="名称">
            <el-input v-model="AL.name" style="width: 500px;"></el-input>
          </el-form-item>
          <el-form-item label="序列号">
            <el-input v-model="AL.displayOrder" style="width: 500px;"></el-input>
          </el-form-item>
          <el-form-item label="是否上线" >
            <el-switch v-model="AL.status" :active-value="1" :inactive-value="0"></el-switch>
          </el-form-item>
          <el-form-item>
            <el-button  type="primary" @click="submitForm('ruleForm')">保存</el-button>
            <el-button type="danger"  @click="deleteclassification('ruleForm')">删除</el-button>
            <!-- <el-button v-if="this.AL.parentName == '系列'" type="info"  @click="property">编辑系列属性</el-button> -->

            <el-button v-if="AL.level == 0 || AL.level == 1" type="warning" 
              @click="resetForm(2)">添加子项</el-button>
          </el-form-item>
        </el-form>

      </el-col>

    </el-row>


      <!-- 界面 -->
      <el-dialog @close="closeDialog1" :title="title" :visible.sync="editFormVisible" width="40%" top="6vh">
          <div class="import-dialog">
      
        <el-row>
          <el-col :span="12">
            <el-form label-width="120px" ref="editForm">
              <el-form-item label="系列名称:" prop="form.name">
                <el-input  size="small" v-model="form.name" auto-complete="off"
                  placeholder="请输入系列名称"></el-input>
              </el-form-item>

            </el-form>
          </el-col>

        </el-row>
        <el-row>
          <el-col :span="24"><el-form label-width="120px" ref="editForm">
              <el-form-item label="系列简介:" prop="form.seriesSynopsis">

                <el-input type="textarea" v-model="form.seriesSynopsis"></el-input>

              </el-form-item>

            </el-form></el-col>

        </el-row>
     
      


        

        <el-row>
          <el-col :span="24">
            <el-form label-width="120px" ref="editForm">

              <el-form-item label="系列主图:" prop="form.seriesPhoto">
                <el-upload action="#" list-type="picture-card" :on-preview="handlePreview" :on-remove="handleRemove1"
                  :auto-upload="false" :file-list="bookUrllist1" multiple :on-change="handleSmallPicSuccess">
                  <i class="el-icon-plus" />
                </el-upload>
                <el-dialog top="6vh" width="50%" :visible.sync="dialogVisibleImg" center :modal="false">
            <img width="100%" :src="form.seriesPhoto" alt>

          </el-dialog>
              </el-form-item>
            </el-form>
          </el-col>

        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form label-width="120px" ref="editForm">

              <el-form-item label="视频" prop="uploadFile">
          <!-- <div v-if="Product.fileType"><a target="_blank" :href="form.seriesVideo">{{ Product.fileType }}视频                                {{
            Product.fileName }}
                 <el-button style="margin-left: 8px;" size="mini" type="primary">打开</el-button>
            </a></div> -->
            <video v-if="form.seriesVideo" controls="controls" :src="form.seriesVideo" style="width: 300px; height:150px;" @play="onPaly" @pause="onPauser"></video>
          <!-- <el-input v-model="form.seriesVideo" size="small" auto-complete="off" placeholder="视频URL"></el-input> -->
          <!-- <div>{{ form.seriesVideo }}</div> -->

          <el-upload class="upload-demo" action :http-request="uploadFile" ref="upload" :limit="fileLimit"
            :on-remove="handleRemove" :file-list="fileList" :on-exceed="handleExceed" :before-upload="beforeUpload"
            :show-file-list="false" :headers="headers">
            <!-- action="/api/file/fileUpload" -->
            <el-button class="btn"><i class="el-icon-paperclip"></i>上传视频</el-button>
          </el-upload>


        </el-form-item>
                
            </el-form>
          </el-col>

        </el-row>
       
        <el-row>
          <el-col :span="12">
            <el-form label-width="120px" ref="editForm">
              <el-form-item label="系列排序:" prop="form.paixu">
                <el-input  size="small" v-model="form.paixu" auto-complete="off"
                  placeholder="请输入系列排序"></el-input>
              </el-form-item>

            </el-form>
          </el-col>

        </el-row>
        <el-row>
          <el-col :span="12"><el-form label-width="120px" ref="editForm">
              <el-form-item label="是否上线:" prop="form.status">
                <el-radio v-model="form.status" :label="1">上线</el-radio>
                <el-radio v-model="form.status" :label="0">不上线</el-radio>
              </el-form-item>

            </el-form>
          </el-col>

        </el-row>
        <el-row>
          <el-col :span="12"><el-form label-width="120px" ref="editForm">
              <el-form-item label="是否推荐:" prop="form.seriesRecomment">
                <el-radio v-model="form.seriesRecomment" :label="1">推荐</el-radio>
                <el-radio v-model="form.seriesRecomment" :label="0">不推荐</el-radio>
              </el-form-item>

            </el-form>
          </el-col>

        </el-row>

      
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="RechargeRole('form')">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { Loading } from 'element-ui';
// import { upload } from '@/api/upload'

export default {
  data() {
    return {
      title:'',
      editFormVisible:false,
      data: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      SEDJJXCX_LHFW_UserInformation_HT: {},
      gg: {
        name: '',
        displayOrder: '',
        status: 1

      },
      AL: {
        name: "",
        displayOrder: '',
        level: '',
        pId: '',
        status: 1
      },
      form: {
        name: '',
        yuliutwo:'',
        slogan:'',
        seriesSynopsis: '',
        seriesPhoto: '',
        seriesVideo:'',
        seriesRecomment: 1,
        status: 1,
        paixu: 0,
        typeId: []
      },
      bookUrllist2:[],
      bookUrllist1:[],
      Subitemshow: false,
       //上传后的视频列表
       fileList: [],
      // 允许的视频类型
      fileType: ["pdf", "doc", "docx", "xls", "xlsx", "txt", "png", "jpg", "bmp", "jpeg",'mp4','ogg','flv','avi','wmv','rmvb','mov'],
      // 运行上传视频大小，单位 M
      fileSize: 50,
      // 附件数量限制
      fileLimit: 1,
      //请求头
      headers: { "Content-Type": "multipart/form-data" },
      dialogVisibleImg:false,
      

    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    }
  },
  methods: {
    
    RechargeRole() {
      // if (!this.form.name) {
      //   this.$message({
      //     showClose: true,
      //     message: '请输入系列名称！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (!this.form.seriesPhoto) {
      //   this.$message({
      //     showClose: true,
      //     message: '请上传系列主图！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (!this.form.yuliutwo) {
      //   this.$message({
      //     showClose: true,
      //     message: '请上传封面图！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (!this.form.seriesVideo) {
      //   this.$message({
      //     showClose: true,
      //     message: '请上传视频链接！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (!this.form.seriesSynopsis) {
      //   this.$message({
      //     showClose: true,
      //     message: '请输入系列简介！',
      //     type: 'warning'
      //   });
      //   return
      // }
      var params = new URLSearchParams();
            params.append('dbName', window.localStorage.getItem('JJHTDBnmame'));


      params.append('name', this.form.name ? this.form.name: '');
      params.append('seriesPhoto', this.form.seriesPhoto ? this.form.seriesPhoto: '');
      params.append('seriesVideo', this.form.seriesVideo ? this.form.seriesVideo: '');
      params.append('seriesSynopsis', this.form.seriesSynopsis ? this.form.seriesSynopsis: '') ;
      params.append('paixu', this.form.paixu ? this.form.paixu: 0);
      params.append('seriesRecomment', this.form.seriesRecomment ? this.form.seriesRecomment: '');
      params.append('status', '1');
      params.append('rootId', this.AL.id ? this.AL.id : '');
      params.append('id', this.sxid ? this.sxid : '');

      this.$store.dispatch('CP/AddcreateProductSeries', params).then((res) => {

        if (res.code == '1') {
          this.$message({
            type: 'success',
            message: this.title == '编辑属性' ? '修改成功' : '添加成功'

          })
           this.getqueryCaseType();
          this.editFormVisible = false;
          this.Subitemshow = false;
          this.form = {};
          this.form.seriesRecomment = 1;
          this.form.status = 1;
          this.form.paixu = 0;
this.dbName1='schende_cn'
        } else {
        }

      }).catch(() => {
        this.loading = false
      })
    },
    onPaly(){

this.playing=true
},
onPauser(){
this.playing=false

},
       //上传视频之前
       beforeUpload(file) {
      if (file.type != "" || file.type != null || file.type != undefined) {
        //截取视频的后缀，判断视频类型
        const FileExt = file.name.replace(/.+\./, "").toLowerCase();
        //计算视频的大小
        const isLt5M = file.size / 1024 / 1024 < 500; //这里做视频大小限制
        //如果大于50M
        if (!isLt5M) {
          this.$message.error('上传视频大小不能超过 500MB!')
          return false;
        }
        //如果视频类型不在允许上传的范围内
        if (this.fileType.includes(FileExt)) {
          return true;
        }
        else {
          this.$message.error("上传视频格式不正确!");
          return false;
        }
      }
    },
    //上传了的视频给移除的事件，由于我没有用到默认的展示，所以没有用到
    handleRemove() {
    },
    //这是我自定义的移除事件
    handleClose(i) {
      this.fileList.splice(i, 1);//删除上传的视频
      if (this.fileList.length == 0) {//如果删完了
        this.fileflag = true;//显示url必填的标识
        this.$set(this.rules.url, 0, { required: true, validator: this.validatorUrl, trigger: 'blur' })//然后动态的添加本地方法的校验规则
      }
    },
    //超出视频个数的回调
    handleExceed() {
      this.$message({
        type: 'warning',
        message: '超出最大上传视频数量的限制！'
      }); return
    },
    //上传视频的事件
    uploadFile(item) {
      this.loading = Loading.service({
        lock: true,
        text: '视频上传中........',
        // background: 'rgba(0, 0, 0, 0.7)',
        target: document.querySelector('#loadingDiv')
      })
      //上传视频的需要formdata类型;所以要转
      let FormDatas = new FormData()
      FormDatas.append('file', item.file);
      upload(FormDatas).then(res => {
        if (res.data.code == '0') {
          setTimeout(() => {
            this.loading.close()

          }, 2000);
          this.form.seriesVideo = res.data.yunUrl;
          // this.Product.filename=res.data.fileNameOld;
        }
      })

      // 	})
    },
    //上传成功后的回调
    handleSuccess() {

    },
    handlePreview(file) {
      this.AL.image = file.url
      this.dialogVisibleImg = true
    },
    closeDialog1(){},
    handleRemove1(file, fileList) {
      // 移除
    },
    handleSmallPicSuccess(res, file, fileList) {
      if (file && file.length == 2) {
        file.shift()
      }
      this.uploadImg(res, 1);

    },
    handleSmallPicSuccess1(res, file, fileList) {
      if (file && file.length == 2) {
        file.shift()
      }
      this.uploadImg(res, 2);

    },
    uploadImg(file, type) {
      console.log(type);
      let formData = new FormData()
      formData.append('file', file.raw)
      this.loading = Loading.service({
        lock: true,
        text: '上传中...',
        // background: 'rgba(0, 0, 0, 0.7)',
        target: document.querySelector('#loadingDiv')
      })
      upload(formData).then((res) => {
        if (res.data.code == 0) {
          this.loading.close()
          this.form.seriesPhoto = res.data.yunUrl;

         

        }

      }).catch(() => {
      })


    },
//查询属性
property() {
      var params = new URLSearchParams();
      params.append('rootId', this.AL.id);
            params.append('dbName', window.localStorage.getItem('JJHTDBnmame'));


      this.$store.dispatch('CP/getSeries', params).then((res) => {

        if (res.code == '1') {
          this.editFormVisible = true;
          this.form = res.record;
          this.sxid = res.record.id;
          this.title = '编辑属性'
          this.bookUrllist1 = [
            {
              url: res.record.seriesPhoto
            }
          ]
         
        } else {
          this.editFormVisible = true;
              this.title = '添加属性'
        }

      }).catch(() => {
      })
    },
// 删除分类
deleteclassification() {
      this.$confirm('是否删除产品分类！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        var params = new URLSearchParams();
      params.append('id', this.AL.id);
            params.append('dbName', window.localStorage.getItem('JJHTDBnmame'));


      this.$store.dispatch('CP/delCaseType', params).then((res) => {

        if (res.code >= 0) {

          this.$message({
            type: 'success',
            message: '删除成功！'
          })
          this.getqueryCaseType();
          this.Subitemshow = false;

        } else {
          this.$message({
            showClose: true,
            message: '该项有子项不可删除！',
            type: 'warning'
          });
        }

      }).catch(() => {
      })
    
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    
    },

    getqueryCaseType() {

      var params = new URLSearchParams();
            params.append('dbName', window.localStorage.getItem('JJHTDBnmame'));


      this.$store.dispatch('CP/getqueryCaseTypes', params).then((res) => {
        this.total1 = res.maxCount;

        if (res.code >= 0) {

          this.data = res.data;
          this.tableData1.forEach((item) => {
            this.tableData.forEach((att) => {
              if (item.matCode == att.matCode) {
                this.$refs.dataTable.toggleRowSelection(att, true)
              }
            })
          })
        } else {
          this.tableData = res.records;
        }

      }).catch(() => {
        this.loading = false
      })
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    handleNodeClick(row) {
      console.log(row);
      this.AL = JSON.parse(JSON.stringify(row));
      this.gg = JSON.parse(JSON.stringify(row));

      this.Subitemshow = true;
      if (row.level == 0 || row.level == 1) {
        this.Subitem = row;
      }
    },
    resetForm() {
      this.AL = {
        name: '',
        displayOrder: '',
        status: 1
        
      }
    },
    submitForm() {
      console.log(this.AL);
      console.log(this.gg);
      var level
      if (this.gg.level == 0) {
        level = 1
      } else if (this.gg.level == 1) {
        level = 2
      } else if (this.gg.level == 2) {
        level = 3
      } else {
        level = this.gg.level
      }
      var params = new URLSearchParams();
            params.append('dbName', window.localStorage.getItem('JJHTDBnmame'));

      params.append('name', this.AL.name);
      params.append('displayOrder', this.AL.displayOrder);
      params.append('level', level);
      params.append('status', this.AL.status);
      params.append('topId', this.gg.topId);
      params.append('id',  this.AL.id ? this.AL.id : '');
      params.append('parentId', this.AL.id ? this.gg.parentId :  this.gg.id);

      this.$store.dispatch('CP/createCaseType', params).then((res) => {
        if (res.code >= 0) {
          this.$message({
            type: 'success',
            message: res.msg
          })
          this.getqueryCaseType();
          this.Subitemshow = false;
          this.Largecategory = false;
          this.SFXZZX = false;
          this.AL = {};
        } else {
          this.SFXZZX = false;

        }

      }).catch(() => {
        this.loading = false
      })
    },
  },
  created() {
    const SEDJJXCX_LHFW_UserInformation_HT = JSON.parse(decodeURIComponent(window.localStorage.getItem('SEDJJXCX_LHFW_UserInformation_HT')))
    this.SEDJJXCX_LHFW_UserInformation_HT = SEDJJXCX_LHFW_UserInformation_HT;

    this.getqueryCaseType();

  },
  mounted() {
    this.$nextTick(function () {
      this.$nextTick(() => {
        // myTree 数组件的ref  默认让第一项高亮 
        // data是树组件对应的数据
        // 通过data中的第一项的id找到对应的节点，然后把这个节点设置为高亮
        // this.$refs.myTree.setCurrentKey(this.data[0].id);
      });
    })
  }
}
</script>

<style lang="scss">
.el-tree-node__label {
  font-weight: 900 !important;
}

.el-tree-node__content {
  font-weight: 900 !important;

}

.dashboard {
  &-container {
    margin: 30px;
  }

  &-text {
    font-size: 30px;
    line-height: 46px;
  }
}
</style>
