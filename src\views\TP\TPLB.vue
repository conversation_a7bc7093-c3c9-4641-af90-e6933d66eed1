<template>
  <div class="app-container">

    <!-- 添加按钮 -->
    <el-form :inline="true" class="user-search">
      <el-form-item>
        <el-button size="mini" @click="handleEdit1" type="primary">添加</el-button>

      </el-form-item>
    </el-form>
    <!-- 表格 -->
    <el-table :data="data" border fit highlight-current-row>
      <el-table-column align="center" label="序号" width="80">
        <template slot-scope="scope">{{ scope.$index + 1 }}</template>
      </el-table-column>


      <el-table-column align="center" label="模块">
        <template slot-scope="scope">{{ scope.row.moduleName }}</template>

        <!-- <template slot-scope="scope">
                  <div v-if="scope.row.module == '1'">首页</div>
                  <div v-if="scope.row.module == '2'">个人中心</div>
                  <div v-if="scope.row.module == '3'">其他</div>
              </template> -->
      </el-table-column>



      <el-table-column align="center" prop="name" label="是否上线">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status == '1'">是</el-tag>
          <el-tag v-if="scope.row.status == '0'" type="danger">否</el-tag>
        </template>

      </el-table-column>

      <el-table-column align="center" label="排序" width="80">
        <template slot-scope="scope">{{ scope.row.sortId }}</template>
      </el-table-column>
      <el-table-column align="center" prop="name" label="描述">
        <template slot-scope="scope">{{ scope.row.remark }}</template>

      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center">
        <template slot-scope="scope">
          <el-button size="mini" @click="handleEdit( scope.row)">编辑</el-button>

          <el-button size="mini" @click="deleteUser(scope.row)" type="danger">删除</el-button>

        </template>
      </el-table-column>
      </el-table-column>
    </el-table>
    <el-row>
      <el-pagination :current-page.sync="currentPage" background style="margin-top:30px" :page-size="pageSize"
        layout="total,prev,pager,next,jumper" :total="total" @current-change="handleCurrentChange1" />

    </el-row>
    <!-- 编辑界面 -->
    <el-dialog :title="title" :visible.sync="editFormVisible" @close="closeDialog">

      <el-tabs v-model="activeName" @tab-click="tabClick">
        <el-tab-pane v-for="(tab, index) in tabs" :key="index" :label="tab.name" :name="(index + 1) + ''" :lazy="true"
          :value="JSON.stringify(tab)">
        </el-tab-pane>
      </el-tabs>
      <el-form label-width="120px" ref="editForm">
        <el-row>
          <el-col :span="12">
            <el-form-item label="模块">
              <el-select size="mini" v-model="special.module" placeholder="模块">
                <el-option size="mini" v-for="item in modulelist" :key="item.id" :label="item.module"
                  :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否上线:">
              <el-radio v-model="special.status" :label="1">是</el-radio>
              <el-radio v-model="special.status" :label="0">否</el-radio>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="排序:">
              <el-input size="mini" v-model="special.sortId" auto-complete="off" placeholder="请输入序号"></el-input>
            </el-form-item>
          </el-col>


        </el-row>
        <el-row>

          <el-col :span="24">
            <el-form-item label="描述:">
              <el-input type="textarea" :rows="2" size="mini" v-model="special.remark" auto-complete="off"
                placeholder="请输入描述"></el-input>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
                      <el-form-item label="类型:" >
                          <el-radio v-model="special.remark" :label="'1'">图片</el-radio>
                          <el-radio v-model="special.remark" :label="'2'">视频</el-radio>
                      </el-form-item>
                  </el-col> -->
        </el-row>
        <el-row>


          <el-col :span="12">
            <el-form-item label="图片:" v-if="special.module != 3">
              <el-upload action="#" list-type="picture-card" :on-preview="handlePreview" :on-remove="handleRemove1"
                :auto-upload="false" :file-list="bookUrllist" multiple :on-change="handleSmallPicSuccess">
                <i class="el-icon-plus" />
              </el-upload>
              <el-dialog :visible.sync="dialogVisibleImg" top="0" center :modal="false">
                <img width="100%" :src="special.url" alt>
              </el-dialog>
            </el-form-item>
            <el-form-item label="视频:" v-if="special.module == 3">
              <video v-show="special.url" controls="controls" :src="special.url" style="width: 300px; height:150px;"
                @play="onPaly" @pause="onPauser"></video>

              <el-upload class="upload-demo" action :http-request="uploadFile" ref="upload" :limit="fileLimit"
                :on-remove="handleRemove" :file-list="fileList" :on-exceed="handleExceed" :before-upload="beforeUpload1"
                :show-file-list="false" :headers="headers">
                <el-button class="btn"><i class="el-icon-paperclip"></i>上传视频</el-button>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="closeDialog">取消</el-button>
        <el-button size="mini" type="primary" class="title" @click="submitForm()">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { Loading } from 'element-ui';

import { createImage, delImage, bannerImages, bannerModules } from '@/api/TP'
import { upload } from '@/api/upload'
export default {
  data() {
    return {
      cpid: '',
      tabs: [{
        name: '中文简体',
        dbName: 'sedysgd_cn',

      }, {
        name: '英语',
        dbName: 'sedysgd_en',


      }
      ],
      dbName: 'sedysgd_cn',
      activeName: '1',
      editFormVisible: false,
      dialogVisibleImg: false,
      data: [],
      total:1,
      pageSize:10,
      currentPage:1,
      title: '添加',
      special: {
        name: '',
        id: '',
        url: "",
        status: 1,
        sortId: '',
        remark: '',
        module: '',
      },
      modulelist: [
        {
          id: '1', name: '首页'
        },
        {
          id: '2', name: '个人中心'
        },
        {
          id: '3', name: '其他'
        }
      ],
      SEDJJXCX_LHFW_UserInformation_HT: {},
      bookUrllist: [],
      //上传后的视频列表
      fileList: [],
      // 允许的视频类型
      fileType: ["pdf", "doc", "docx", "xls", "xlsx", "txt", "png", "jpg", "bmp", "jpeg", 'mp4', 'ogg', 'flv', 'avi', 'wmv', 'rmvb', 'mov'],
      // 运行上传视频大小，单位 M
      fileSize: 50,
      // 附件数量限制
      fileLimit: 1,
      //请求头
      headers: { "Content-Type": "multipart/form-data" },
    }
  },
  created() {
    const SEDJJXCX_LHFW_UserInformation_HT = JSON.parse(decodeURIComponent(window.localStorage.getItem('SEDJJXCX_LHFW_UserInformation_HT')))
    this.SEDJJXCX_LHFW_UserInformation_HT = SEDJJXCX_LHFW_UserInformation_HT
    this.fetchData()
    this.fetchData1()
  },
  methods: {
    onPauser() {
      this.playing = false

    }, onPaly() {

      this.playing = true
    },
    fetchData1(val = 1) {
      const params = {
        dbName: this.dbName,

        curPage: val
      }
      bannerModules(params).then(response => {
        if (response.data.code == '1') {
          this.modulelist = response.data.list
        }
      })
    },
    handleCurrentChange1(val) {
      this.fetchData(val)
    },
    fetchData(val = 1) {
      const params = {
        dbName: this.dbName,

        curPage: val,
        flag: 'query',
        number: '10'
      }
      bannerImages(params).then(response => {
        this.total = response.data.sumcount
        if (response.data.code == '1') {
          this.data = response.data.list
        }
      })
    },
    handleEdit1() {
      this.title = '添加';
      this.editFormVisible = true;
      this.special = {
        name: '',
        id: '',
        url: "",
        status: 1,
        sortId: '',
        remark: '',
        module: '',
      };
    },
    // 编辑
    tabClick(el) {
      const currentTab = JSON.parse(el.$attrs.value)
      let e = {
        id: this.cpid
      }
      this.dbName = currentTab.dbName
      this.handleEdit(e)
    },
    handleEdit( e) {
      this.cpid = e.id
      const params = {
        id: e.id,
        flag: 'queryById',
        dbName: this.dbName
      };
      bannerImages(params).then(response => {
        if (response.data.code == '1') {
          this.editFormVisible = true;
          this.title = '修改'
          this.special = response.data.record;
          this.bookUrllist = [
            {
              url: response.data.record.url
            }
          ]
          return
          if (row != undefined && row != 'undefined') {
           
          } else {
            this.title = '添加'
            this.special={
              name: '',
        id: '',
        url: "",
        status: 1,
        sortId: '',
        remark: '',
        module: '',
            }
          }
        } else {

        }
      })

    },
    // 关闭编辑、增加弹出框
    closeDialog() {

      this.editFormVisible = false;
      this.special = {
        name: '',
        id: '',
        url: "",
        status: 1,
        sortId: '',
        remark: '',
        module: '',

      };
        this.activeName='1'
          this.dbName='sedysgd_cn'
    },
    // 编辑、增加页面保存方法
    submitForm() {
      console.log(this.special.module);

      if (this.special.module == '') {
        this.$message({
          showClose: true,
          message: '请选择模块！',
          type: 'warning'
        });
        return
      }
      if (this.special.sortId == '') {
        this.$message({
          showClose: true,
          message: '请输入排序！',
          type: 'warning'
        });
        return
      }
      if (this.special.url == '') {
        this.$message({
          showClose: true,
          message: '请上传图片！',
          type: 'warning'
        });
        return
      }
      const params = {
        module: this.special.module,
        id: this.special.id,
        url: this.special.url,
        status: this.special.status,
        sortId: this.special.sortId,
        remark: this.special.remark,
        dbList: this.$dbList,
        flag: 'save',
        dbName: this.dbName
      };
      bannerImages(params).then(response => {
        console.log(response);
        if (response.data.code == '1') {
          this.editFormVisible = false
          this.special.sortId = '';
          this.special.remark = '';
          this.bookUrllist = []
          this.fetchData()
          this.activeName='1'
          this.dbName='sedysgd_cn'
          this.$message({
            type: 'success',
            message: this.title == '修改' ? '修改成功！' : '创建成功！'
          })
        } else {
          this.$message({
            type: 'warning',
            message: '创建失败！'
          })
        }
      })


    },

   
       // 删除品牌
       deleteUser(row) {
      this.$confirm('此操作将永久删除, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        var params = new URLSearchParams();
        params.append('id', row.id);
        params.append('entity', 'banner');
        params.append('dbList', this.$dbList);

        this.$store.dispatch('CP/delProduct', params).then((res) => {

          if (res.code >= 0) {

            this.$message({
              type: 'success',
              message: '删除成功！'
            })
            this.fetchData()

          } else {

          }

        }).catch(() => {
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });

    },
    handlePreview(file) {
      // 放大
      this.special.url = file.url
      this.dialogVisibleImg = true
    },
    handleRemove1(file, fileList) {
      // 移除
      const { uid } = file
      const { powUrl } = this.form
      const newPowUrl = powUrl.filter(v => {
        return uid !== powUrl.uid
      })
    },
    handleSmallPicSuccess(res, file, fileList) {
      if (file && file.length == 2) {
        file.shift()
      }
      console.log(111111);
      this.uploadImg(res);

    }, uploadImg(file, type) {
      let formData = new FormData()
      formData.append('file', file.raw)
      this.loading = Loading.service({
        lock: true,
        text: '图片上传中........',
        // background: 'rgba(0, 0, 0, 0.7)',
        target: document.querySelector('#loadingDiv')
      })
      upload(formData).then(response => {
        this.special.url = response.data.yunUrl;
        this.loading.close()

      }).catch((err) => {
        console.log(err);

      });


    },
    //上传视频之前
    beforeUpload1(file) {
      if (file.type != "" || file.type != null || file.type != undefined) {
        //截取视频的后缀，判断视频类型
        const FileExt = file.name.replace(/.+\./, "").toLowerCase();
        //计算视频的大小
        const isLt5M = file.size / 1024 / 1024 < 500; //这里做视频大小限制
        //如果大于50M
        if (!isLt5M) {
          this.$message.error('上传视频大小不能超过 500MB!')
          return false;
        }
        //如果视频类型不在允许上传的范围内
        if (this.fileType.includes(FileExt)) {
          return true;
        }
        else {
          this.$message.error("上传视频格式不正确!");
          return false;
        }
      }
    },
    //上传了的视频给移除的事件，由于我没有用到默认的展示，所以没有用到
    handleRemove() {
    },
    //这是我自定义的移除事件
    handleClose(i) {
      this.fileList.splice(i, 1);//删除上传的视频
      if (this.fileList.length == 0) {//如果删完了
        this.fileflag = true;//显示url必填的标识
        this.$set(this.rules.url, 0, { required: true, validator: this.validatorUrl, trigger: 'blur' })//然后动态的添加本地方法的校验规则
      }
    },
    //超出视频个数的回调
    handleExceed() {
      this.$message({
        type: 'warning',
        message: '超出最大上传视频数量的限制！'
      }); return
    },
    //上传视频的事件
    uploadFile(item) {
      this.loading = Loading.service({
        lock: true,
        text: '视频上传中........',
        // background: 'rgba(0, 0, 0, 0.7)',
        target: document.querySelector('#loadingDiv')
      })
      //上传视频的需要formdata类型;所以要转
      let FormDatas = new FormData()
      FormDatas.append('file', item.file);
      upload(FormDatas).then(res => {
        if (res.data.code == '0') {
          setTimeout(() => {
            this.loading.close()

          }, 2000);
          this.special.url = res.data.yunUrl;
          this.$set(this.special, "videoUrl", this.special.url + '/' + 1)
          // this.Product.filename=res.data.fileNameOld;
        }
      })

      // 	})
    },
  }
}
</script>

<style lang="scss" scoped>
.el-row {
  margin-bottom: 20px;
}

.el-row:last-child {
  margin-bottom: 0;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.el-button--medium {
  margin-top: 10px;
  margin-left: 600px;
  height: 45px;
  width: 100px;
}




// .el-input.inp {
//   width: auto !important;
// }</style>
