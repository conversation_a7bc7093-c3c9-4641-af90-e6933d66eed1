import request from '@/utils/request'

export function createImage(params) {
  return request({
    url: 'oe_createImage_.csp',
    method: 'get',
    params
  })
}
export function delImage(params) {
  return request({
    url: 'oe_delImage_.csp',
    method: 'get',
    params
  })
}

export function bannerImages(params) {
  return request({
    url: 'oe_ImageCURDGD_.csp',
    method: 'get',
    params
  })
}

export function createModule(params) {
  return request({
    url: 'oe_createModule_.csp',
    method: 'get',
    params
  })
}
export function delModule(params) {
  return request({
    url: 'oe_delModule_.csp',
    method: 'get',
    params
  })
}
export function bannerModules(params) {
  return request({
    url: 'oe_bannerModules_.csp',
    method: 'get',
    params
  })
}

export function getServiceProcesList(params) {
  return request({
    url: 'oe_getServiceProcesList_.csp',
    method: 'get',
    params
  })
}

export function addServiceProces(params) {
  return request({
    url: 'oe_addServiceProces_.csp',
    method: 'post',
    params
  })
}

export function delServiceProces(params) {
  return request({
    url: 'oe_delServiceProces_.csp',
    method: 'get',
    params
  })
}

export function contactUs(params) {
  return request({
    url: 'oe_contactUs_.csp',
    method: 'get',
    params
  })
}

export function fileTypeCURD(params) {
  return request({
    url: 'oe_fileTypeCURD_.csp',
    method: 'get',
    params
  })
}



export function getFileYm(params) {
  return request({
    url: 'oe_getFileYm_.csp',
    method: 'get',
    params
  })
}
