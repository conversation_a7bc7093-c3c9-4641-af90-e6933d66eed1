import request from '@/utils/request'

export function createIngredientCat(params) {//添加
  return request({
    url: 'oe_createIngredientCat_.csp',
    method: 'get',
    params
  })
}

export function delIngredientsCat(params) {//删除
  return request({
    url: 'oe_delIngredientsCat_.csp',
    method: 'get',
    params
  })
}



export function ingredientsCats(params) {//获取
  return request({
    url: 'oe_ingredientsCats_.csp',
    method: 'get',
    params
  })
}


export function addIngredientsl(params) {//添加商品
  return request({
    url: 'oe_addIngredientsl_.csp',
    method: 'get',
    params
  })
}
export function ingredientslProducts(params) {//获取添加商品
  return request({
    url: 'oe_ingredientslProducts_.csp',
    method: 'get',
    params
  })
}


export function allMyOrder(params) {//获取订单商品
  return request({
    url: 'oe_allMyOrderGD_.csp',
    method: 'get',
    params
  })
}


export function alterOrderAddress(params) {//修改地址
  return request({
    url: 'oe_alterOrderAddress_.csp',
    method: 'get',
    params
  })
}
export function shipments(params) {//发货
  return request({
    url: 'oe_shipments_.csp',
    method: 'get',
    params
  })
}

export function goodsDetail(params) {
  return request({
    url: 'oe_getOrderDetailGD_.csp',
    method: 'get',
    params
  })
}