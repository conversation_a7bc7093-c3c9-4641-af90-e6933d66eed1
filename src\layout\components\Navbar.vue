<template>
  <div class="navbar">
    <hamburger :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" />

    <breadcrumb class="breadcrumb-container" />
    <div class="right-menu">
      <el-dropdown class="avatar-container" trigger="click">
        <div class="avatar-wrapper">
          <div>管理员</div>
          <!-- <img src="https://cdn.juesedao.cn/huiya/5ed199afa2524a80a44881e156211710" class="user-avatar"> -->
          <i class="el-icon-caret-bottom" />
        </div>
        <el-dropdown-menu slot="dropdown" class="user-dropdown">
          <!-- <el-dropdown-item @click.native="ChangePassword">
            修改密码
          </el-dropdown-item> -->
          <!-- <a target="_blank" href="https://github.com/PanJiaChen/vue-admin-template/">
            <el-dropdown-item>Github</el-dropdown-item>
          </a>
          <a target="_blank" href="https://panjiachen.github.io/vue-element-admin-site/#/">
            <el-dropdown-item>Docs</el-dropdown-item>
          </a> -->
          <el-dropdown-item  @click.native="logout">
            <span style="display:block;">退出登录</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <div class="right-menu">
      <el-dropdown class="avatar-container" trigger="click">
        <div class="avatar-wrapper">
          <!-- <div>当前语言:{{dbNAME}}-切换语言</div> -->
          <!-- <img src="https://cdn.juesedao.cn/huiya/5ed199afa2524a80a44881e156211710" class="user-avatar"> -->
          <i class="el-icon-caret-bottom" />
        </div>
        <el-dropdown-menu slot="dropdown" class="user-dropdown">
          <!-- <el-dropdown-item @click.native="ChangePassword">
            修改密码
          </el-dropdown-item> -->
          <!-- <a target="_blank" href="https://github.com/PanJiaChen/vue-admin-template/">
            <el-dropdown-item>Github</el-dropdown-item>
          </a>
          <a target="_blank" href="https://panjiachen.github.io/vue-element-admin-site/#/">
            <el-dropdown-item>Docs</el-dropdown-item>
          </a> -->
          <el-dropdown-item  @click.native="qhyy('sedysgd_cn')">
            <span style="display:block;">中文</span>
          </el-dropdown-item>
          <el-dropdown-item  @click.native="qhyy('sedysgd_en')">
            <span style="display:block;">英文</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <el-dialog @close='formCancal' width="30%" title="修改密码" :visible.sync="editFormVisible" :close-on-click-modal="false"
      :close-on-press-escape="false">
      <el-form ref="form">
        <el-form-item label="新密码:" label-width="20%">
          <template slot-scope="scope">
            　　　　 <el-input show-password v-model="newPassword" size="mini" auto-complete="off"
              placeholder="无"></el-input>
          </template>
        </el-form-item>

      </el-form>
      <el-form ref="form">
        <el-form-item label="确认密码:" label-width="20%">
          <template slot-scope="scope">
            　　　　 <el-input show-password v-model="affirmPassword" size="mini" auto-complete="off"
              placeholder="无"></el-input>
          </template>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="formCancal">取 消</el-button>
        <el-button type="primary" @click="formSubmit('form')">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Breadcrumb from '@/components/Breadcrumb'
import Hamburger from '@/components/Hamburger'
import {  updatePassword } from '@/api/user'

export default {
  components: {
    Breadcrumb,
    Hamburger
  },
  data() {
    return {
      SEDJJXCX_LHFW_UserInformation_HT: {},
      editFormVisible: false,
      newPassword: '',
      affirmPassword: '',
      dbNAME:""

    }
  },
  computed: {
    ...mapGetters([
      'sidebar',
      'avatar'
    ])
  },
  created() {

    const SEDJJXCX_LHFW_UserInformation_HT = JSON.parse(decodeURIComponent(window.localStorage.getItem('SEDJJXCX_LHFW_UserInformation_HT')))
    this.SEDJJXCX_LHFW_UserInformation_HT = SEDJJXCX_LHFW_UserInformation_HT;
    console.log(this.SEDJJXCX_LHFW_UserInformation_HT);
   this.dbNAME= window.localStorage.getItem('JJHTDBnmame') =='sedysgd_cn' ? '中文':"英文"
    
  },
  methods: {
    qhyy(e){

      window.localStorage.setItem('JJHTDBnmame', e);
          this.$router.replace("/");

    },
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    async logout() {

      window.localStorage.removeItem('SEDJJXCX')
      window.localStorage.removeItem('SEDJJXCX')
      this.$router.push(`/login?redirect=${this.$route.fullPath}`)
      location.reload()

    },
    async ChangePassword() {
      this.editFormVisible = true;

    },
    async formCancal() {
      this.editFormVisible = false;
      this.newPassword = ''
      this.affirmPassword = ''

    },
    async formSubmit() {

      if (this.newPassword == this.affirmPassword) {
        if (this.newPassword == '') {
          this.$message({
            showClose: true,
            message: '请输入新密码！',
            type: 'warning'
          });
          return
        }
        if (this.affirmPassword == '') {
          this.$message({
            showClose: true,
            message: '请输入确认密码',
            type: 'warning'
          });
          return
        }



        let data={
          userCode: this.SEDJJXCX_LHFW_UserInformation_HT.userCode,
          oldPassword: this.SEDJJXCX_LHFW_UserInformation_HT.password,
          newPassword:this.$md5(this.affirmPassword)
        }
        updatePassword(data).then(response => {
          if (response.data.code === '1') {
            this.editFormVisible = false;
            this.newPassword = '';
            this.affirmPassword = '';
            this.$message({
              type: 'success',
              message: '修改成功请重新登录'
            })
            setTimeout(() => {
              window.localStorage.removeItem('token')
              window.localStorage.removeItem('SEDJJXCX_LHFW_UserInformation_HT')
              this.$router.push(`/login?redirect=${this.$route.fullPath}`)
              location.reload()
            }, 2000)

          } else {
            this.$message({
              type: 'warning',
              message: '修改失败'

            })
          }
        })
      } else {
        this.$message({
          showClose: true,
          message: '二次输入密码不一致！',
          type: 'warning'
        });
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  box-shadow: 0 1px 4px rgba(0, 21, 41, .08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background .3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, .025)
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background .3s;

        &:hover {
          background: rgba(0, 0, 0, .025)
        }
      }
    }

    .avatar-container {
      margin-right: 30px;

      .avatar-wrapper {
        margin-top: 5px;
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 18px;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
