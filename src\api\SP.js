import request from '@/utils/request'

export function createVideoType(params) {
  return request({
    url: 'oe_createVideoType_.csp',
    method: 'post',
    params
  })
}
export function videoTypeList(params) {
  return request({
    url: 'oe_videoTypeList_.csp',
    method: 'post',
    params
  })
}

export function deleteVideoType(params) {
  return request({
    url: 'oe_deleteVideoType_.csp',
    method: 'post',
    params
  })
}

export function createVideo(params) {
  return request({
    url: 'oe_createVideo_.csp',
    method: 'post',
    params
  })
}
export function aiVideoList(params) {
  return request({
    url: 'oe_aiVideoList_.csp',
    method: 'post',
    params
  })
}
export function allVideoType(params) {
  return request({
    url: 'oe_allVideoType_.csp',
    method: 'post',
    params
  })
}