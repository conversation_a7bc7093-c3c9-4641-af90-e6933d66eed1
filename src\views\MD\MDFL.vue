<template>
    <div class="app-container">

        <!-- 添加按钮 -->
        <el-form :inline="true" class="user-search">
            <el-form-item>
                <el-button size="mini" @click="handleEdit" type="primary">添加分类</el-button>

            </el-form-item>
        </el-form>
        <!-- 表格 -->
        <el-table :data="data" border fit highlight-current-row>
            <el-table-column align="center" label="序号">
                <template slot-scope="scope">{{scope.$index + 1}}</template>
            </el-table-column>
            <el-table-column align="center" prop="name" label="分类名称">
                <template slot-scope="scope">{{ scope.row.name }}</template>

            </el-table-column>
            <el-table-column align="center" prop="name" label="是否显示">
                <template slot-scope="scope">
                    <el-tag v-if="scope.row.delFlag == '1'">不显示</el-tag>
                    <el-tag v-if="scope.row.delFlag == '0'" type="success">显示</el-tag>
                </template>

            </el-table-column>
            <el-table-column align="center" prop="name" label="创建时间">
                <template slot-scope="scope">{{ scope.row.createTime }}</template>

            </el-table-column>
            <el-table-column fixed="right" label="操作" align="center">
                <template slot-scope="scope">
                    <el-button size="mini" @click="handleEdit(scope.$index, scope.row)">编辑</el-button>

                    <el-button size="mini" @click="deleteUser(scope.$index, scope.row)" type="danger">删除</el-button>

                </template>
            </el-table-column>
            </el-table-column>
        </el-table>
        <!-- 编辑界面 -->
        <el-dialog  :title="title" :visible.sync="editFormVisible" width="30%" @close="closeDialog">
            <el-form label-width="120px" ref="editForm">
                <el-form-item label="分类名称:" prop="authority.name">
                    <el-input size="mini" v-model="authority.name" auto-complete="off"
                        placeholder="请输入分类名称"></el-input>
                </el-form-item>
            
                <!-- <el-form-item label="是否删除:" prop="authority.delFlag">
                    <el-radio-group v-model="authority.delFlag">
                        <el-radio :label="1">删除</el-radio>

                        <el-radio :label="0">未删除</el-radio>

                    </el-radio-group>
                </el-form-item> -->
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button size="mini" @click="closeDialog">取消</el-button>
                <el-button size="mini" type="primary" class="title" @click="submitForm()">保存</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import {createDatumType ,datumTypeList ,deleteDatumType} from '@/api/ZL'
import { log } from 'console'

export default {
    data() {
        return {
            editFormVisible: false,
            data: [],
            title: '添加',
            authority: {
                name: '',
                delFlag: 1,
                id: '',
            },
            SEDJJXCX_LHFW_UserInformation_HT:{}
        }
    },
    created() {
        const SEDJJXCX_LHFW_UserInformation_HT = JSON.parse(decodeURIComponent(window.localStorage.getItem('SEDJJXCX_LHFW_UserInformation_HT')))
    this.SEDJJXCX_LHFW_UserInformation_HT = SEDJJXCX_LHFW_UserInformation_HT
    this.fetchData()
    },
    methods: {
        fetchData(val = 1) {
            const params = {
                                    dbName: window.localStorage.getItem('JJHTDBnmame'),

                curPage: val
            }
            datumTypeList(params).then(response => {
                if (response.data.code == '1') {
                    this.data = response.data.records
                }
            })
        },
        handleEdit: function (index, row) {

            this.editFormVisible = true;
            if (row != undefined && row != 'undefined') {
                this.title = '修改'
                this.authority.id = row.id
                this.authority.name = row.name
            } else {
                this.title = '添加'
                this.authority.id = ''
                this.authority.sort = ''
            }
        },
        // 关闭编辑、增加弹出框
        closeDialog() {

            this.editFormVisible = false;
            this.deptName = '';
            this.authority = {};
        },
        // 编辑、增加页面保存方法
        submitForm() {
            if (this.authority && this.authority.name) {
                const params = {
                    name: this.authority.name,
                                        dbName: window.localStorage.getItem('JJHTDBnmame'),

                    id:this.title == '修改' ?  this.authority.id : ''
                }
               
                createDatumType(params).then(response => {
                    if (response.data.code == '1') {
                        this.editFormVisible = false
                        this.authority.name = '';
                        this.authority.delFlag = '';
                        this.fetchData()
                        this.$message({
                            type: 'success',
                            message:  this.title == '修改' ?'分类修改成功！' : '分类创建成功！'

                        })
                    } else {
                        this.$message({
                            type: 'warning',
                            message: '分类创建失败！'
                        })
                    }
                })
            } else {
                this.$message({
                    showClose: true,
                    message: '请填写分类名称！',
                    type: 'warning'
                });
            }

        },
        deleteUser(index, row) {
            this.$confirm('此操作将永久删除分类, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                const params = {
                    listid: row.id,
                                        dbName: window.localStorage.getItem('JJHTDBnmame'),

                }; deleteDatumType(params).then(response => {
                    if (response.data.code == '1') {
                        this.fetchData()
                    }
                })
                this.$message({
                    type: 'success',
                    message: '删除成功!'
                });
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });

        }
    }
}
</script>

<style lang="scss" scoped>
.el-row {
    margin-bottom: 20px;
}

.el-row:last-child {
    margin-bottom: 0;
}

.avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.avatar-uploader .el-upload:hover {
    border-color: #409eff;
}

.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}

.avatar {
    width: 178px;
    height: 178px;
    display: block;
}

.el-button--medium {
    margin-top: 10px;
    margin-left: 600px;
    height: 45px;
    width: 100px;
}

.el-input {
    width: 200px;
}

// .el-input.inp {
//   width: auto !important;
// }
</style>
