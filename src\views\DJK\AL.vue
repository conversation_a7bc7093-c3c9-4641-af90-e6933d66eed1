<template>
  <div class="dashboard-container">

    <el-form :inline="true" class="user-search">



      <el-form-item>
        <el-input size="small" placeholder="请输入内容" v-model="input4" class="el-input_ss1">
          <el-button size="small" slot="append" icon="el-icon-search" @click="change1"></el-button>
        </el-input>
      </el-form-item>
      <el-form-item>
        <!-- <el-button size="mini" type="primary" @click="addincrease('ruleForm')">添加</el-button> -->

        <el-button size="mini" type="info" @click="getgetselectCase1('1')">重新加载</el-button>

        <!-- 搜索筛选 -->


      </el-form-item>
    </el-form>
    <el-table max-height="550" :row-style="{ height: '0' }" :row-class-name="tableRowClassName"
      :header-cell-style="{ background: '#f2f6fc' }" :data="data" fit highlight-current-row
      @selection-change="handleSelectionChange">
      <!-- <el-table-column align="center" type="selection" width="60">
        </el-table-column> -->
      <el-table-column type="index" label="序号" />



      <el-table-column align="center" label="代金卡编码">
        <template slot-scope="scope">{{ scope.row.couponCode }}</template>
      </el-table-column>
      <el-table-column align="center" label="门店名称">
        <template slot-scope="scope">{{ scope.row.storeName }}</template>
      </el-table-column> <el-table-column align="center" label="经销商名称">
        <template slot-scope="scope">{{ scope.row.storeUserName }}</template>
      </el-table-column>

      <el-table-column align="center" label="经销商手机号">
        <template slot-scope="scope">{{ scope.row.storeUserPhone }}</template>
      </el-table-column>

      <el-table-column align="center" label="客户名字">
        <template slot-scope="scope">{{ scope.row.customerName }}</template>
      </el-table-column>


      <el-table-column align="center" label="客户电话">
        <template slot-scope="scope">{{ scope.row.customerPhone }}</template>
      </el-table-column>
      <el-table-column align="center" label="客户送货地址">
        <template slot-scope="scope">{{ scope.row.customerAddress }}</template>
      </el-table-column>
      <el-table-column align="center" label="初始金额">
        <template slot-scope="scope">￥{{ scope.row.totalMoney }}</template>
      </el-table-column>
      <el-table-column align="center" label="使用金额">
        <template slot-scope="scope">￥{{ scope.row.usedMoney }}</template>
      </el-table-column>
      <el-table-column align="center" label="剩余金额">
        <template slot-scope="scope">￥{{ scope.row.residueMoney }}</template>
      </el-table-column>
      <el-table-column label="代金卡二维码" align="center" width="150">
        <template slot-scope="scope">
          <el-popover placement="bottom" trigger="hover" width="300">
            <img :src="scope.row.qrCode" width="100%" />
            <img slot="reference" :src="scope.row.qrCode" :alt="scope.row.qrCode"
              style="max-height: 30px;max-width: 30px; " />
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column align="center" label="状态" width="100">
        <template slot-scope="scope">
          <el-tag size="medium" effect="warning" v-if="scope.row.status == '0'">
            审核中
          </el-tag>

          <el-tag size="medium" v-if="scope.row.status == '-1'" effect="danger">
            未通过
          </el-tag>

          <el-tag size="medium" v-if="scope.row.status == '1'" effect="success">
            已通过
          </el-tag>
          <el-tag size="medium" v-if="scope.row.status == '2'" effect="danger">
            已激活
          </el-tag>
          <el-tag size="medium" v-if="scope.row.status == '3'" effect="info">
            已过期
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column fixed="right" label="操作" width="200" align="center">
        <template slot-scope="scope">
          <el-button size="mini" icon="el-icon-edit" type="primary" @click="handleEditzt(scope.row)">审核</el-button>
          <!-- <el-button type="warning" icon="el-icon-delete" size="mini"
            @click="deleteclassification(scope.row)">删除</el-button> -->

        </template>
      </el-table-column>
    </el-table>

    <el-row>
      <el-pagination class="pagdw" :current-page.sync="currentPage" background :page-size="pageSize"
        layout="total,prev,pager,next,jumper" :total="total" @current-change="handleCurrentChange1" />

    </el-row>

    <!-- 编辑界面 -->
    <el-dialog :close-on-click-modal="false" :close-on-press-escape="false" @close="closeDialog" title="代金卡详情"
      :visible.sync="editFormVisible" width="60%" top="6vh">
      <div class="import-dialog">
        <el-row>
          <el-col :span="8">
            <el-form label-width="120px">
              <el-form-item label="代金卡编码:">
                <div>{{ form.couponCode }}</div>
              </el-form-item>

            </el-form>
          </el-col>
          <el-col :span="8">
            <el-form label-width="120px">
              <el-form-item label="门店名称:">
                <div>{{ form.storeName }}</div>

              </el-form-item>

            </el-form>
          </el-col>


          <el-col :span="8">
            <el-form label-width="120px">
              <el-form-item label="经销商名称:">
                <div>{{ form.storeUserName }}</div>

               
              </el-form-item>

            </el-form>
          </el-col>
          <el-col :span="8">
            <el-form label-width="120px">
              <el-form-item label="经销商手机号:">
                <div>{{ form.storeUserPhone }}</div>

                
              </el-form-item>

            </el-form>
          </el-col>
          <el-col :span="8">
            <el-form label-width="120px">
              <el-form-item label="客户名字:">
                <div>{{ form.customerName }}</div>

              </el-form-item>

            </el-form>
          </el-col>
          <el-col :span="8">
            <el-form label-width="120px">
              <el-form-item label="客户电话:">
                <div>{{ form.customerPhone }}</div>

                
              </el-form-item>

            </el-form>
          </el-col>
          <el-col :span="8">
            <el-form label-width="120px">
              <el-form-item label="客户送货地址:">
                <div>{{ form.customerAddress }}</div>

              
              </el-form-item>

            </el-form>
          </el-col>
          <el-col :span="8">
            <el-form label-width="120px">
              <el-form-item label="初始金额:">
                <div>{{ form.totalMoney }}</div>

              </el-form-item>

            </el-form>
          </el-col> <el-col :span="8">
            <el-form label-width="120px">
              <el-form-item label="使用金额:">
                <div>{{ form.usedMoney }}</div>

              </el-form-item>

            </el-form>
          </el-col> <el-col :span="8">
            <el-form label-width="120px">
              <el-form-item label="剩余金额:">
                <div>{{ form.residueMoney }}</div>

              </el-form-item>

            </el-form>
          </el-col>
          <el-col :span="8">
            <el-form label-width="120px">
              <el-form-item label="状态:">
                <el-tag size="medium" effect="warning" v-if="form.status == '0'">
                  审核中
                </el-tag>

                <el-tag size="medium" v-if="form.status == '-1'" effect="danger">
                  未通过
                </el-tag>

                <el-tag size="medium" v-if="form.status == '1'" effect="success">
                  已通过
                </el-tag>
                <el-tag size="medium" v-if="form.status == '2'" effect="danger">
                  已激活
                </el-tag>
                <el-tag size="medium" v-if="form.status == '3'" effect="info">
                  已过期
                </el-tag>
              </el-form-item>

            </el-form>
          </el-col>
          <el-col :span="8">
            <el-form label-width="120px">
              <el-form-item label="代金卡二维码:">
                <img :src="form.qrCode" width="20%" />

              </el-form-item>

            </el-form>
          </el-col>

          <el-col :span="8">
            <el-form label-width="120px">
              <el-form-item label="附件:">
                <el-image 
                 :preview-src-list="form.annex.map(v => v.url)" v-for="item in form.annex" :src="item.url"
    style="width: 100px; height: 100px"
 
    >
  </el-image>
              </el-form-item>

            </el-form>
          </el-col>

        </el-row>





        <div class="cs">消费记录</div>

        <el-table max-height="550" :row-style="{ height: '0' }" :row-class-name="tableRowClassName"
      :header-cell-style="{ background: '#f2f6fc' }" :data="form.records" fit highlight-current-row
      @selection-change="handleSelectionChange">
      <!-- <el-table-column align="center" type="selection" width="60">
        </el-table-column> -->
      <el-table-column type="index" label="序号" />



      <el-table-column align="center" label="消费时间">
        <template slot-scope="scope">{{ scope.row.createTime }}</template>
      </el-table-column>
     
   
      
      <el-table-column align="center" label="消费金额">
        <template slot-scope="scope">￥{{ scope.row.money }}</template>
      </el-table-column>
      <el-table-column align="center" label="备注" width="300">
        <template slot-scope="scope">{{ scope.row.remark }}</template>
      </el-table-column>
      <el-table-column label="附件" align="center" width="150">
        <template slot-scope="scope">
        

              <el-image 
                 :preview-src-list="scope.row.annex.map(v => v.url)" v-for="item in scope.row.annex" :src="item.url"
    style="width: 50px; height: 50px"
 
    >
  </el-image>
        </template>
      </el-table-column>
      

     
    </el-table>











      </div>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="form.status =='0'" type="danger" @click="RechargeRole(1)">驳回</el-button>
        <el-button v-if="form.status =='0'" type="success" @click="RechargeRole(2)">通过</el-button>
        <el-button   @click="RechargeRole(3)">关闭</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { Loading } from 'element-ui';
import { upload } from '@/api/upload'
import wangEditor from "@/components/wangEditor/wangEditor.vue";
import { log } from 'console';
import AMapLoader from "@amap/amap-jsapi-loader";

export default {
  components: {
    // VueEditor,
    wangEditor
  },
  data() {

    return {
      cpid: "",
      tabs: [{
        name: '中文简体',
        dbName: 'sedysgd_cn',

      }, {
        name: '英语',
        dbName: 'sedysgd_en',


      }
      ],
      dbName: 'sedysgd_cn',
      activeName: '1',
      ssxmlist: [],
      drawer: false,
      map: null, // 地图实例
      addressData: "6686", // 查找后地址数据
      poiPicker: "", // 选取的poi地点
      wxz: {
        add: "",
        Q: "",
        R: "",
      },
      limitnum: 10,
      data1: [],
      value: [],
      value4: [],
      filterMethod(query, item) {
        return item.pinyin.indexOf(query) > -1;
      },
      isClear: false,//设置为true的时候，这个可以用this.wangEditorDetail=''来替代
      wangEditorDetail: "",
      title: '',
      editFormVisible: false,
      classification: [],

      data: [],
      defaultProps: {
        children: 'children',
        label: 'title'
      },
      SEDJJXCX_LHFW_UserInformation_HT: {},
      gg: {
        title: '',
        displayOrder: '',
        status: 1

      },
      AL: {
        title: "",
        displayOrder: '',
        level: '',
        pId: '',
        status: 1
      },
      form: {
        title: '',
        txt: '',
        type: '',
        photo: '',
        status: '1',
      },
      bookUrllist2: [],
      bookUrllist1: [],
      Subitemshow: false,
      //上传后的视频列表
      fileList: [],
      // 允许的视频类型
      fileType: ["pdf", "doc", "docx", "xls", "xlsx", "txt", "png", "jpg", "bmp", "jpeg", 'mp4', 'ogg', 'flv', 'avi', 'wmv', 'rmvb', 'mov'],
      // 运行上传视频大小，单位 M
      fileSize: 50,
      // 附件数量限制
      fileLimit: 1,
      //请求头
      headers: { "Content-Type": "multipart/form-data" },
      dialogVisibleImg: false,
      curPage: 1,
      currentPage: 1,
      total: 1,
      pageSize: 10,
      bookUrllist: [],
      bookUrllist1: [],
      bookUrllist2: [],
      bookUrllist3: [],
      bookUrllist4: [],
      form: {
        title: '',
        productModel: '',
        productVr: '',
        productSynopsis: '',
        productImage: '',
        productImages: [],
        status: 0,
        productView: '',
        productId: [],
        preview: '',
        productUrl: [],
        photo: [],
        productPaixu: 0,
        qrImage: '',
        style: '',
        space: '',
        colour: '',
        space: '',
        series: '',
        longpic: '',
        formNum: '',
        yuliuone: '',
        productId: '',
        caseProductId: '',
        sort: '',
        topFlag: 0
      },
      productUrlindex: 1,
      productImageslindex: 1,
      curPage: 1,
      limitnum: 10,
      checkList: [
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
      ],
      action: process.env.VUE_APP_BASE_API + "/uploadCloud?filename=11111",
      tableData: [{
        namem: '',
        introduction: '',
        kongjianimages: [],
        producttype: '',


      }],
      IMGindex: 0,
      GJ_img: '',
      gg_img: '',
      input1: '',
      input2: '',
      input3: '',
      input4: '',
      GGlist: [],
      XLList: [],
      dialogVisibleImg1: false,
      dialogVisibleImg2: false,
      dialogVisibleImg3: false,
      dialogVisibleImg4: false,
      isHandlingChange: false, // 添加一个标识来判断是否正在处理change事件
      xz_lingth: '',
      form: {
        name: '',
        accountNum: '',
        password: '',
        phone: '',
        address: '',
      }

    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    }
  },
  methods: {
    GETdzk() {
      this.initMap();
      this.drawer = true;
    },
    initMap() {
      const that = this;
      AMapLoader.load({
        key: "4b6837a6fd9da1e644527a189b514049", // 申请好的Web端开发者Key，首次调用 load 时必填
        // 2.0版本太卡了 ，所以使用的1.4.0版本  其插件也有不同  如：ToolBar
        version: "1.4.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
        resizeEnable: true,
        // plugins: [
        //   "AMap.ToolBar", //工具条
        //   "AMap.Scale", // 比例尺
        //   "AMap.Geolocation", //定位
        //   "AMap.Autocomplete", //输入提示插件
        //   "AMap.PlaceSearch", //POI搜索插件
        // ], // 需要使用的的插件列表，如比例尺'AMap.Scale'等
        AMapUI: {
          version: "1.0",
          plugins: ["misc/PathSimplifier", "overlay/SimpleMarker"], // SimpleMarker设置自定义图标，PathSimplifier轨迹展示组件
        },
      })
        .then((AMap) => {
          // console.log(AMap);
          this.map = new AMap.Map("container", {
            // 设置地图容器id
            viewMode: "3D", // 是否为3D地图模式
            zoom: 11, // 初始化地图级别
            center: that.mrcenter, // 初始化地图中心点位置
          });

          AMapUI.loadUI(["misc/PoiPicker"], function (PoiPicker) {
            var poiPicker = new PoiPicker({
              // city:'北京',
              input: "ipt",
            });

            that.changeIpt(poiPicker);
          });

          // console.log(this.map);
          // console.log(AMap);
        })
        .catch((e) => {
        });
    },

    cancelForm() {
      this.drawer = false;
      this.wxz = {
        add: "",
        Q: "",
        R: "",
      };
    },
    closeDrawer() {
      // this.temp.address = this.wxz.add;
      this.temp.longitude = this.wxz.R;
      this.temp.latitude = this.wxz.Q;
      this.mrcenter = [this.wxz.R, this.wxz.Q];
      this.drawer = false;
    },
    change7() {
      this.wxz.add = this.temp.address;
      console.log(this.wxz.add);
    },
    changeIpt(poiPicker) {
      const that = this;
      // console.log(that,'that')
      // console.log(poiPicker);
      that.poiPicker = poiPicker;

      var marker = new AMap.Marker();

      var infoWindow = new AMap.InfoWindow({
        offset: new AMap.Pixel(0, -20),
      });

      try {
        poiPicker.on("poiPicked", function (poiResult) {
          const addData = poiResult;

          var source = poiResult.source;
          var poi = poiResult.item;
          var info = {
            name: poi.name,
            address: poi.address,
          };

          marker.setMap(that.map);
          infoWindow.setMap(that.map);

          marker.setPosition(poi.location);
          infoWindow.setPosition(poi.location);

          infoWindow.setContent(
            "POI信息: <pre>" + JSON.stringify(info, null, 2) + "</pre>"
          );

          infoWindow.open(that.map, marker.getPosition());

          that.map.setCenter(marker.getPosition());
          console.log(addData);
          that.wxz.add = addData.item.district ? addData.item.district : '' + addData.item.address;
          that.wxz.Q = addData.item.location.Q;
          that.wxz.R = addData.item.location.R;
        });
      } catch (error) {
        console.log(error);
      }

      // //点击选中地址
      // AMap.event.addListener(placeSearch,'markerClick',function(e){
      //   // let that = this
      //   if(e){
      //     let selectAddress = e
      //     //  console.log(e);
      //   that.$emit('getAddress',selectAddress)
      //   }
      // })
    },
    handleChange(value, direction, movedKeys) {
      this.form.productId = movedKeys.join()
      this.form.caseProductId = movedKeys.join()
      console.log(value, direction, movedKeys);
    },
    generateData() {


      var params = new URLSearchParams();
      params.append('dbName', window.localStorage.getItem('JJHTDBnmame'));



      this.$store.dispatch('CP/getAllProduct', params).then((res) => {
        if (res.code == '1') {
          var a = [];

          res.record.forEach((item, index) => {
            a.push({
              label: item.title + '-' + item.productModel,
              key: item.id,
              pinyin: res.record[index]
            });

          });
          this.data1 = a

        } else {
          this.data1 = []
        }

      }).catch(() => {
      })



    },
    wangEditorChange(e) {
      this.form.txt = e;
    },
    // 删除品牌
    deleteclassification(row) {
      this.$confirm('此操作将永久删除, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        var params = new URLSearchParams();
        params.append('id', row.id);
        params.append('entity', 'storeAccount2');
        // params.append('dbName', this.dbName);
        params.append('dbList', this.$dbList);


        this.$store.dispatch('CP/delProduct', params).then((res) => {

          if (res.code >= 0) {

            this.$message({
              type: 'success',
              message: '删除成功！'
            })
            this.storeAccountCURD();

          } else {

          }

        }).catch(() => {
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });

    },
    getgetselectCase1() {
      this.input1 = ''
      this.input2 = ''
      this.input3 = ''
      this.input4 = ''
      this.curPage = 1;
      this.dbName = 'sedysgd_cn'
      this.storeAccountCURD()
    },
    // 上下架筛选
    change1(row) {


      var params = new URLSearchParams();
      params.append('dbName', this.dbName);

      params.append('curPage', this.curPage);
      if (this.input1) {
        params.append('status', this.input1);

      }

      if (this.input2) {
        params.append('size', this.input2);

      }
      if (this.input3) {
        params.append('series', this.input3);

      }
      if (this.input4) {
        params.append('keyword', this.input4);
      }


      this.$store.dispatch('CP/cashCouponList', params).then((res) => {
        this.total = res.totalNum;
        this.currentPage = 1;

        if (res.code == '1') {
          this.data = res.list
        } else {
          this.data = []
        }

      }).catch(() => {
        this.loading = false
      })
    },

    onPauser() {
      this.playing = false

    }, onPaly() {

      this.playing = true
    },
    getshow(e) {
      // if (this.checkList[e].length > 1) {
      //   this.checkList[e].splice(0, 1)
      // }
    },
    addincrease() {
      this.initMap();

      this.editFormVisible = true;
      this.title = '添加';
      this.form = {
        name: '',
        accountNum: '',
        password: '',
        phone: '',
        address: '',
      }
    },
    // 编辑
    handleEditzt(e) {
      var params = new URLSearchParams();
      this.cpid = e.id
      params.append('id', e.id);
      params.append('dbName', this.dbName);
      this.$store.dispatch('CP/getCouponById', params).then((res) => {
        if(res.code == '1'){

// this.formimages = res.records;
this.editFormVisible = true;
if(res.record && res.record.annex){
  res.record.annex=JSON.parse(res.record.annex)
}
if(res.record && res.record.records&& res.record.records.length > 0){
  res.record.records.forEach((item)=>{
    if(item.annex){
      item.annex=JSON.parse(item.annex)
    }
  })
  
}
this.form = res.record;
        }



      })










    },
    //上传视频之前
    beforeUpload1(file) {
      if (file.type != "" || file.type != null || file.type != undefined) {
        //截取视频的后缀，判断视频类型
        const FileExt = file.title.replace(/.+\./, "").toLowerCase();
        //计算视频的大小
        const isLt5M = file.size / 1024 / 1024 < 500; //这里做视频大小限制
        //如果大于50M
        if (!isLt5M) {
          this.$message.error('上传视频大小不能超过 500MB!')
          return false;
        }
        //如果视频类型不在允许上传的范围内
        if (this.fileType.includes(FileExt)) {
          return true;
        }
        else {
          this.$message.error("上传视频格式不正确!");
          return false;
        }
      }
    },
    //上传了的视频给移除的事件，由于我没有用到默认的展示，所以没有用到
    handleRemove() {
    },
    //这是我自定义的移除事件
    handleClose(i) {
      this.fileList.splice(i, 1);//删除上传的视频
      if (this.fileList.length == 0) {//如果删完了
        this.fileflag = true;//显示url必填的标识
        this.$set(this.rules.url, 0, { required: true, validator: this.validatorUrl, trigger: 'blur' })//然后动态的添加本地方法的校验规则
      }
    },
    //超出视频个数的回调
    handleExceed() {
      this.$message({
        type: 'warning',
        message: '超出最大上传视频数量的限制！'
      }); return
    },
    //上传视频的事件
    uploadFile(item) {
      this.loading = Loading.service({
        lock: true,
        text: '视频上传中........',
        // background: 'rgba(0, 0, 0, 0.7)',
        target: document.querySelector('#loadingDiv')
      })
      //上传视频的需要formdata类型;所以要转
      let FormDatas = new FormData()
      FormDatas.append('file', item.file);
      upload(FormDatas).then(res => {
        if (res.data.code == '0') {
          setTimeout(() => {
            this.loading.close()

          }, 2000);
          this.form.caseVideo = res.data.yunUrl;
          this.$set(this.form, "videoUrl", this.form.caseVideo + '/' + 1)
          console.log(this.form.caseVideo);
          // this.Product.filename=res.data.fileNameOld;
        }
      })

      // 	})
    },
    //上传成功后的回调
    handleSuccess() {

    },
    tabClick(el) {
      const currentTab = JSON.parse(el.$attrs.value)
      let e = {
        id: this.cpid
      }
      this.dbName = currentTab.dbName
      this.handleEditzt(e)
    },
    // 添加品牌
    RechargeRole(type) {
      if(type ==3){
        this.editFormVisible = false;
          this.form = {
            title: '',
            txt: '',
            type: '',
            photo: '',
            status: '1',
          };
          return
      }
      // if (!this.form.title) {
      //   this.$message({
      //     showClose: true,
      //     message: '请输入品牌名称！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (!this.form.productModel) {
      //   this.$message({
      //     showClose: true,
      //     message: '请输入品牌型号！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (!this.form.formNum) {
      //   this.$message({
      //     showClose: true,
      //     message: '请输入品牌面数！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (!this.form.productVr) {
      //   this.$message({
      //     showClose: true,
      //     message: '请输入品牌vr链接！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (!this.form.productView) {
      //   this.$message({
      //     showClose: true,
      //     message: '请输入品牌视频链接！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (!this.form.productSynopsis) {
      //   this.$message({
      //     showClose: true,
      //     message: '请输入品牌简介！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (this.checkList && this.checkList[0].length == 0) {
      //   this.$message({
      //     showClose: true,
      //     message: '请选择品牌！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (this.checkList && this.checkList[1].length == 0) {
      //   this.$message({
      //     showClose: true,
      //     message: '请选择系列！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (this.checkList && this.checkList[2].length == 0) {
      //   this.$message({
      //     showClose: true,
      //     message: '请选择色彩！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (this.checkList && this.checkList[3].length == 0) {
      //   this.$message({
      //     showClose: true,
      //     message: '请选择规格！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (this.checkList && this.checkList[4].length == 0) {
      //   this.$message({
      //     showClose: true,
      //     message: '请选择系列！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (!this.form.productImage) {
      //   this.$message({
      //     showClose: true,
      //     message: '请上传品牌封面图！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (!this.form.productUrl) {
      //   this.$message({
      //     showClose: true,
      //     message: '请上传品牌单片图！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (!this.form.qrImage) {
      //   this.$message({
      //     showClose: true,
      //     message: '请上传二维码！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (this.form.productImages && this.form.productImages.length == 0) {
      //   this.$message({
      //     showClose: true,
      //     message: '请上传品牌展示图！',
      //     type: 'warning'
      //   });
      //   return
      // }




      var params = new URLSearchParams();

      params.append('dbName', this.dbName);

    
      params.append('id', this.form.id ? this.form.id : '');
      params.append('status', type == 1 ? '-1' : '1');
  


      this.$store.dispatch('CP/approvalCoupon', params).then((res) => {

        if (res.code == '1') {
          this.$message({
            type: 'success',
            message: res.msg

          })
          this.storeAccountCURD();
          this.editFormVisible = false;
          this.wangEditorDetail = ''
          this.form = {
            title: '',
            txt: '',
            type: '',
            photo: '',
            status: '1',
          };

          this.activeName = '1'
          this.dbName = 'sedysgd_cn'
          this.checkList = [
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            [],
          ]
        } else {
        }

      }).catch(() => {
        this.loading = false
      })
    },
    uploadSectionFile() { },
    // 删除规格
    handleClick(index, row) {
      if (index == '0') {
        this.$message({
          type: 'warning',
          message: '规格中最后一项不可删除！'
        })
      } else {
        this.tableData.splice(index, 1)
      }
    },
    handleUploadAgain() {
      this.isHandlingChange = false

    },
    handleCheck(index, row) {
      console.log(index);
      this.IMGindex = index;

    },
    handlePreview11(file) {
      this.dialogVisibleImg4 = true
      this.gg_img = file.url

    },

    handlePreviewgg(file) {
      this.dialogVisibleImg3 = true
      this.GJ_img = file.url

    },
    handlePreview(file) {
      this.form.zhuye = file.url
      this.dialogVisibleImg = true
    },
    handlePreview1(file) {
      this.GJ_img = file.url
      this.dialogVisibleImg3 = true
    },
    handlePreviewcp(file) {
      this.form.productUrl = file.url
      this.dialogVisibleImg1 = true
    },

    // 点击取消
    formCancal() {
      this.specialShowgg = false;
      // this.$refs.vueEditor.editor.root.innerHTML = '';
      // this.tableOption = [];
      // this.bookUrllist = [];
      if (this.tableData && this.tableData[0] && this.tableData[0].num == '') {
        // this.Product.useAttr = false;
      }
      if (this.dialogTitle == '新增商品') {
        this.tableData = [{
          namem: '',
          introduction: '',
          kongjianimages: [],
          producttype: '',
        }]
      }

    },
    handleCurrentChange() { },
    // 添加一行
    add() {

      let obj = {
        namem: '',
        introduction: '',
        kongjianimages: [],
        producttype: '',

      }
      this.tableData.push(obj)
    },
    // 关于上传pdf部分 start
    handleSuccess(res, file) {  // 上传成功的钩子
      this.form.yuliuone = file.response.yunUrl

    },
    fileChange(file, fileList) {  //文件状态改变时的钩子，添加文件、上传成功和上传失败时都会被调用

      this.fileList = fileList;

    },
    8(file, fileList) {//文件列表移除文件时的钩子
      this.form.yuliuone = ''

    },
    handlePreview1111(file) {//点击文件列表中已上传的文件时的钩子
    },
    handleExceed(files, fileList) {
      this.$message.warning(`当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);
    },
    beforeUpload(file) {//文件上传之前的钩子函数
      this.file = file;
      this.fileName = file.title;
      // this.fileSize = file.size;
      const extension = file.title.split('.').slice(-1) == 'pdf'
      if (!extension) {
        this.$message.warning('上传模板只能是pdf格式!')
        return false
      }
      // let reader = new FileReader();
      // reader.readAsDataURL(file);
      // console.log(new FileReader().readAsDataURL(file),'reader.readAsDataURL(file)')
      // console.log(reader.result,'reader.result')

      // let that = this;
      // reader.onload = function() {
      //   that.fileData = reader.result;
      // };
      // console.log(that.fileData,'that.fileData')
      // return false; // 返回false不会自动上传
    },
    // 删除文件之前的钩子
    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.title}？`);
    },
    formCancal2() { },
    formCancal1() { },
    uploadFileError(res, file, fileList) {
    },
    handleBeforeUpload(file) {

    },
    uploadFileSuccess(res, file, fileList) {
      if (res.code == 0) {
        fileList.forEach((att) => {
          if (att.response) {
            let obj = {
              url: ""
            }
            if (file.response.yunUrl) {
              this.form.productUrl = file.response.yunUrl
            }

          }
        })

      }

    },
    uploadFileSuccess1(res, file, fileList) {
      console.log(211111);

      // this.form.productImages = [];
      if (res.code == 0) {
        fileList.forEach((att) => {
          console.log(att);
          if (att.response) {
            let obj = {
              url: ""
            }
            obj.url = file.response.yunUrl;
            this.form.photo.push(obj)
          }
        })
      }




    },
    exceedFile(res, file, fileList) {
      this.$message.error('只能上传' + this.limitnum + '个文件');
    },
    handleSmallPicSuccess(res, file, fileList) {
      if (file && file.length == 2) {
        file.shift()
      }
      this.uploadImg(res, 1);

    },
    handleSmallPicSuccess1(res, file, fileList) {

      this.uploadImg(res, 2);

    },
    handleSmallPicSuccessgg(res, file, fileList) {
      this.xz_lingth = file.length


      // 处理上传成功逻辑
      this.uploadImg(res, 6);
      // ...

      // 标记已经上传过文件



    },
    handleSmallPicSuccess1(res, file, fileList) {

      this.uploadImg(res, 2);

    }, handleSmallPicSuccess2(res, file, fileList) {
      if (file && file.length == 2) {
        file.shift()
      }
      this.uploadImg(res, 3);

    }, handleSmallPicSuccess3(res, file, fileList) {

      this.uploadImg(res, 4);

    },
    handleSmallPicSuccess4(res, file, fileList) {

      this.uploadImg(res, 5);

    },
    uploadImg(file, type) {
      let that = this;
      let formData = new FormData()
      formData.append('file', file.raw)
      that.loading = Loading.service({
        lock: true,
        text: '上传中...',
        // background: 'rgba(0, 0, 0, 0.7)',
        target: document.querySelector('#loadingDiv')
      })
      upload(formData).then((res) => {
        if (res.data.code == 0) {
          if (type == 1) {
            that.loading.close();

            that.form.photo = res.data.yunUrl;
          } else if (type == 2) {
            that.loading.close();
            let obj = {
              url: res.data.yunUrl,
              title: res.data.fileName
            }
            that.form.photo.push(obj);

          } else if (type == 3) {
            that.loading.close();

            that.form.qrImage = res.data.yunUrl;
          } else if (type == 4) {
            that.loading.close();

            let obj = {
              url: res.data.yunUrl
            }

            that.form.productImages.push(obj);
          } else if (type == 5) {
            that.loading.close();

            that.form.longpic = res.data.yunUrl;;
          } else if (type == 6) {

            let aa = {
              url: res.data.yunUrl
            }
            this.tableData[this.IMGindex].kongjianimages.push(aa)
            if (this.xz_lingth == this.tableData[this.IMGindex].kongjianimages.length) {
              that.loading.close();

            }
          }


        }

      }).catch((err) => {
        console.log(err);
      })


    },
    tableRowClassName({ row, rowIndex }) {
      if (rowIndex === 1) {
        return 'warning-row';
      } else if (rowIndex === 3) {
        return 'success-row';
      }
      return '';
    },
    handleRemove1(file) {

    },
    handleRemove8() { },
    handleRemove2(file) {
      // 1.获取将要删除图片的临时路径
      const filePath = file.url;
      // 2.从pics数组中，找到图片对应的索引值
      const i = this.form.productUrl.findIndex((x) => x.url === filePath);
      // 3.调用splice方法，移除图片信息
      this.form.productUrl.splice(i, 1);
    },
    handleRemove3() { },
    handleRemove4(file) {
      // 1.获取将要删除图片的临时路径
      const filePath = file.url;
      // 2.从pics数组中，找到图片对应的索引值
      const i = this.form.productImages.findIndex((x) => x.url === filePath);
      // 3.调用splice方法，移除图片信息
      this.form.productImages.splice(i, 1);

    },
    handleRemove5() {
      this.form.longpic = '';
    },
    closeDialog() {
      this.form = {
        title: '',
        txt: '',
        type: '',
        photo: '',
        status: '1',
      };
      this.dbName = 'sedysgd_cn'
      this.activeName = '1';
      this.bookUrllist = []
      this.bookUrllist1 = []
      this.bookUrllist2 = []
      this.bookUrllist3 = []
    },
    // 查询品牌分类
    getproductTypeList() {
      var params = new URLSearchParams();
      params.append('dbName', window.localStorage.getItem('JJHTDBnmame'));



      this.$store.dispatch('CP/getqueryCaseTypes', params).then((res) => {

        if (res.code >= 0) {

          this.classification = res.data[0].children;
          this.classification1 = res.data[0].children[0].children;
          res.data[0].children.forEach((item) => {
            if (item.title == '规格') {
              this.GGlist = item.children
            }
            if (item.title == '系列') {
              this.XLList = item.children
            }
          })
        } else {
          this.classification = []
          this.classification1 = []
        }

      }).catch(() => {
        this.loading = false
      })
    },

    // 查询品牌分类
    storeAccountCURD() {
      var params = new URLSearchParams();
      params.append('dbName', 'sedysgd_cn');

      params.append('curPage', this.curPage);


      this.$store.dispatch('CP/cashCouponList', params).then((res) => {
        this.total = res.totalNum;
        if (res.code == '1') {
          this.data = res.list
        } else {
          this.data = []
        }

      }).catch(() => {
        this.loading = false
      })
    },
    handleCurrentChange1(val) {
      this.curPage = val;
      this.storeAccountCURD()
    },
    handleSelectionChange() { },


  },
  created() {
    const SEDJJXCX_LHFW_UserInformation_HT = JSON.parse(decodeURIComponent(window.localStorage.getItem('SEDJJXCX_LHFW_UserInformation_HT')))
    this.SEDJJXCX_LHFW_UserInformation_HT = SEDJJXCX_LHFW_UserInformation_HT;

    this.storeAccountCURD();
    // this.getproductTypeList();
    // this.generateData()


  },
  mounted() {

  }
}
</script>

<style lang="scss">
.el-table thead {
  color: #000000 !important;
}

.el-tree-node__label {
  font-weight: 900 !important;
}

.el-tree-node__content {
  font-weight: 900 !important;

}

.dashboard {
  &-container {
    margin: 30px;
  }

  &-text {
    font-size: 30px;
    line-height: 46px;
  }
}

.item {
  margin-bottom: 18px;
}


.pagdw {
  float: right;
  margin-top: 46px;
}

.el-input_ss1 {
  margin-top: 4px;
}
</style>
<style scoped>
#container {
  padding: 0px;
  margin: 0px;
  width: 100%;
  height: 400px;
}

.demo-drawer__footer {
  float: right;
  margin-top: 20px;
}
.cs{
  font-size: 16px;
  font-weight: 800;
  color: #000;
  margin-bottom: 30px;
}
</style>