<template>
  <div class="dashboard-container">
    <el-form :inline="true" class="user-search">
      <el-form-item label="语言:">
        <el-select
          size="small"
          @change="change1"
          v-model="dbName"
          placeholder="中文繁体"
        >
          <el-option
            size="small"
            v-for="items in tabs"
            :key="items.id"
            :label="items.name"
            :value="items.dbName"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="上线状态:">
        <el-select
          size="small"
          placeholder="上线状态"
          @change="change1"
          v-model="input1"
        >
          <el-option label="上线" value="1"></el-option>
          <el-option label="下线" value="0"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="规格:" prop="Product.catld">
        <el-select
          size="small"
          v-model="input2"
          placeholder="规格"
          @change="change1"
        >
          <el-option
            size="small"
            v-for="item in GGlist"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="系列:" prop="Product.catld">
        <el-select
          size="small"
          v-model="input3"
          placeholder="系列"
          @change="change1"
        >
          <el-option
            size="small"
            v-for="item in XLList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="产品分类:">
        <el-select size="small" placeholder="产品分类" @change="change1" v-model="input5">
          <el-option label="定制" value="1"></el-option>
          <el-option label="成品" value="2"></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-input
          size="small"
          placeholder="请输入内容"
          v-model="input4"
          class="el-input_ss1"
        >
          <el-button
            size="small"
            slot="append"
            icon="el-icon-search"
            @click="change1"
          ></el-button>
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-button size="mini" type="primary" @click="addincrease('ruleForm')">
          添加
        </el-button>

        <el-button size="mini" type="info" @click="getgetselectCase1('1')">
          重新加载
        </el-button>
        <el-button
          size="mini"
          type="success"
          @click="leading"
          icon="el-icon-download"
          >批量下载</el-button
        >

        <!-- 搜索筛选 -->
      </el-form-item>
    </el-form>

    <el-table
      border
      :data="data"
      fit
      highlight-current-row
      @selection-change="handleSelectionChange"
    >
      <el-table-column align="center" type="selection" width="60">
      </el-table-column>
      <el-table-column align="center" type="index" label="序号" width="100" />

      <el-table-column align="center" label="产品id">
        <template slot-scope="scope">{{ scope.row.id }}</template>
      </el-table-column>

      <el-table-column label="封面图" align="center" width="100">
        <template slot-scope="scope">
          <!-- <el-popover placement="bottom" trigger="hover" width="300"> -->
          <!-- <img :src="scope.row.productUrl" width="100%" /> -->
          <img
            slot="reference"
            :src="scope.row.productUrl"
            :alt="scope.row.productUrl"
            style="max-height: 30px; max-width: 30px"
          />
          <!-- </el-popover> -->
        </template>
      </el-table-column>
      <el-table-column align="center" label="产品名称">
        <template slot-scope="scope">{{ scope.row.name }}</template>
      </el-table-column>

      <el-table-column align="center" label="产品型号">
        <template slot-scope="scope">{{ scope.row.productModel }}</template>
      </el-table-column>

      <el-table-column align="center" label="上线状态" width="100">
        <template slot-scope="scope">
          <el-tag size="medium" effect="success" v-if="scope.row.status == '1'">
            上线
          </el-tag>

          <el-tag size="medium" v-if="scope.row.status == '0'" effect="danger">
            未上线
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column align="center" label="产品分类">
        <template slot-scope="scope">
          <div class="aaaa">
            {{ scope.row.yuliutwo == "1" ? "定制" : "成品" }}
          </div>
        </template>
      </el-table-column>

      <el-table-column align="center" label="产品价格">
        <template slot-scope="scope">
          <div class="aaaa">
            {{ scope.row.productMoney ? scope.row.productMoney : "暂无" }}
          </div>
        </template>
      </el-table-column>

      <el-table-column align="center" label="创建时间">
        <template slot-scope="scope">{{ scope.row.createTime }}</template>
      </el-table-column>

      <el-table-column fixed="right" label="操作" width="400" align="center">
        <template slot-scope="scope">
          <el-button
            size="mini"
            icon="el-icon-edit"
            type="primary"
            @click="handleEditzt(scope.row)"
            >编辑</el-button
          >
          <el-button
            type="warning"
            icon="el-icon-delete"
            size="mini"
            @click="deleteclassification(scope.row)"
            >删除</el-button
          >
          <el-button
            @click="synchronous(scope.row)"
            size="mini"
            type="info"
            icon="el-icon-help"
            >同步</el-button
          >

          <el-button
            size="mini"
            type="success"
            @click="QRcode(scope.row)"
            icon="el-icon-zoom-in"
            >二维码</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-row>
      <el-pagination
        class="pagdw"
        :current-page.sync="currentPage"
        background
        :page-size="pageSize"
        layout="total,prev,pager,next,jumper"
        :total="total"
        @current-change="handleCurrentChange1"
      />
    </el-row>

    <!-- 编辑界面 -->
    <el-dialog
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="closeDialog"
      :title="title"
      :visible.sync="editFormVisible"
      width="80%"
      top="6vh"
    >
      <el-tabs v-model="activeName" @tab-click="tabClick">
        <el-tab-pane
          v-for="(tab, index) in tabs"
          :key="index"
          :label="tab.name"
          :name="index + 1 + ''"
          :lazy="true"
          :value="JSON.stringify(tab)"
        >
        </el-tab-pane>
      </el-tabs>
      <div class="import-dialog">
        <el-row>
          <el-col :span="24">
            <el-form label-width="120px">
              <el-form-item label="产品名称:" prop="form.name">
                <el-input
                  size="small"
                  v-model="form.name"
                  auto-complete="off"
                  placeholder="请输入产品名称"
                ></el-input>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form label-width="120px">
              <el-form-item label="产品型号:" prop="form.productModel">
                <el-input
                  size="small"
                  v-model="form.productModel"
                  auto-complete="off"
                  placeholder="请输入产品型号"
                ></el-input>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form label-width="120px">
              <el-form-item label="台脚/架子材质:" prop="form.formNum">
                <el-input
                  size="small"
                  v-model="form.formNum"
                  auto-complete="off"
                  placeholder="请输入台脚/架子材质"
                ></el-input>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form label-width="120px">
              <el-form-item label="台面/面料工艺:" prop="form.craft">
                <el-input
                  type="textarea"
                  size="small"
                  v-model="form.craft"
                  auto-complete="off"
                  placeholder="请输入台面/面料工艺"
                ></el-input>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form label-width="120px">
              <el-form-item label="材料标准:" prop="form.unit">
                <el-input
                  size="small"
                  v-model="form.unit"
                  auto-complete="off"
                  placeholder="请输入材料标准"
                ></el-input>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form label-width="120px">
              <el-form-item label="产品VR:" prop="form.productVr">
                <el-input
                  size="small"
                  v-model="form.productVr"
                  auto-complete="off"
                  placeholder="请输入产品VR"
                ></el-input>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form label-width="120px">
              <el-form-item label="产品规格:" prop="form.yuliuone">
                <el-input
                  size="small"
                  v-model="form.yuliuone"
                  auto-complete="off"
                  placeholder="请输入产品规格"
                ></el-input>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form label-width="120px">
              <el-form-item label="产品价格:" prop="form.productMoney">
                <el-input
                  size="small"
                  v-model="form.productMoney"
                  auto-complete="off"
                  placeholder="请输入产品价格"
                ></el-input>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form label-width="120px">
              <el-form-item label="经销商价:" prop="form.productMoney">
                <el-input
                  size="small"
                  v-model="form.seriesRootId"
                  auto-complete="off"
                  placeholder="请输入经销商价"
                ></el-input>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <!-- <el-row>
              <el-col :span="24">
                <el-form label-width="120px" >
                  <el-form-item label="产品视频:" prop="form.productView">
                    <el-input size="small" v-model="form.productView" auto-complete="off"
                      placeholder="请输入产品名称"></el-input>
                  </el-form-item>

                </el-form>
              </el-col>

            </el-row> -->
        <el-row>
          <el-col :span="24"
            ><el-form label-width="120px">
              <el-form-item label="产品简介:" prop="form.productSynopsis">
                <el-input
                  type="textarea"
                  size="small"
                  v-model="form.productSynopsis"
                  auto-complete="off"
                  placeholder="请输入产品简介"
                ></el-input>
              </el-form-item> </el-form
          ></el-col>
        </el-row>
        <el-row>
          <!-- <el-col :span="24"><el-form label-width="120px">
              <el-form-item label="产品分类:" prop="form.yuliutow">
                <el-radio-group v-model="form.yuliutwo">
                  <el-radio :label="'1'">定制</el-radio>
                  <el-radio :label="'2'">成品</el-radio>
                </el-radio-group>
              </el-form-item>

            </el-form>
          </el-col> -->
          <el-col :span="24"
            ><el-form label-width="120px">
              <el-form-item label="是否上线:" prop="form.status">
                <el-radio-group v-model="form.status">
                  <el-radio :label="1">是</el-radio>
                  <el-radio :label="0">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24"
            ><el-form label-width="120px">
              <el-form-item label="首页推荐:" prop="form.isOwn">
                <el-radio-group v-model="form.isOwn">
                  <el-radio :label="'1'">是</el-radio>
                  <el-radio :label="'0'">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24"
            ><el-form label-width="120px">
              <el-form-item label="是否使用规格:">
                <el-checkbox v-model="form.caseJiluId" @change="selectBox"
                  >使用规格</el-checkbox
                >
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24"
            ><el-form label-width="120px">
              <el-form-item label="排序:" prop="form.productPaixu">
                <el-input
                  size="small"
                  v-model="form.productPaixu"
                  auto-complete="off"
                  placeholder="请输入排序"
                ></el-input>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>

        <el-row>
          <el-col
            :span="24"
            v-for="(item, index) in classification"
            :key="item.id"
          >
            <el-form label-width="120px">
              <el-form-item :label="item.name">
                <el-checkbox-group
                  v-model="checkList[index]"
                  @change="getshow(index)"
                >
                  <!-- <el-checkbox :value=items.id :label=items.name v-for="(items,indexs) in item.children" :key="items.id"></el-checkbox> -->
                  <el-checkbox
                    :label="items.id"
                    v-for="(items, indexs) in item.children"
                    :key="items.id"
                    >{{ items.name }}</el-checkbox
                  >
                </el-checkbox-group>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form label-width="120px">
              <el-form-item label="设计师头像:">
                <el-upload
                  action="#"
                  list-type="picture-card"
                  :on-preview="handlePreviewsjs"
                  :on-remove="handleRemove1"
                  :auto-upload="false"
                  :file-list="bookUrllistsjs"
                  multiple
                  :on-change="handleSmallPicSuccesssjs"
                >
                  <i class="el-icon-plus" />
                </el-upload>
                <el-dialog
                  :visible.sync="dialogVisibleImgsjs"
                  top="0"
                  center
                  :modal="false"
                >
                  <img width="100%" :src="form.stylePhoto" alt />
                </el-dialog>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24"
            ><el-form label-width="120px">
              <el-form-item label="设计师名称:" prop="form.productPaixu">
                <el-input
                  size="small"
                  v-model="form.styleName"
                  auto-complete="off"
                  placeholder="请输入设计师名称"
                ></el-input>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24"
            ><el-form label-width="120px">
              <el-form-item label="设计师国籍:" prop="form.productPaixu">
                <el-input
                  size="small"
                  v-model="form.styleCountry"
                  auto-complete="off"
                  placeholder="请输入设计师国籍"
                ></el-input>
              </el-form-item>
            </el-form>
          </el-col>
          <el-col :span="24"
            ><el-form label-width="120px">
              <el-form-item label="设计师介绍:" prop="form.productPaixu">
                <el-input
                  type="textarea"
                  size="small"
                  v-model="form.styleAbout"
                  auto-complete="off"
                  placeholder="请输入设计师介绍"
                ></el-input>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form label-width="120px">
              <el-form-item label="产品主图:" prop="form.productImage">
                <el-upload
                  action="#"
                  list-type="picture-card"
                  :on-preview="handlePreview"
                  :on-remove="handleRemove1"
                  :auto-upload="false"
                  :file-list="bookUrllist"
                  multiple
                  :on-change="handleSmallPicSuccessxsf"
                >
                  <i class="el-icon-plus" />
                </el-upload>
                <el-dialog
                  :visible.sync="dialogVisibleImg"
                  top="0"
                  center
                  :modal="false"
                >
                  <img width="100%" :src="form.productImage" alt />
                </el-dialog>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form label-width="120px">
              <el-form-item label="封面图:" prop="form.productUrl">
                <el-upload
                  action="#"
                  list-type="picture-card"
                  :on-preview="handlePreviewcp"
                  :on-remove="handleRemove2"
                  :auto-upload="false"
                  :file-list="bookUrllist1"
                  multiple
                  :on-change="handleSmallPicSuccessfmt"
                >
                  <i class="el-icon-plus" />
                </el-upload>
                <el-dialog
                  :visible.sync="dialogVisibleImg1"
                  top="0"
                  center
                  :modal="false"
                >
                  <img width="100%" :src="form.productUrl" alt />
                </el-dialog>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form label-width="120px">
              <el-form-item label="空间效果图:">
                <el-upload
                  ref="ImageChang2"
                  action="#"
                  list-type="picture-card"
                  :on-preview="handlePreviewgg"
                  :on-remove="handleRemove4"
                  :auto-upload="false"
                  :file-list="bookUrllist3"
                  multiple
                  :on-change="handleSmallPicSuccesskjxgt"
                >
                  <i class="el-icon-plus" />
                </el-upload>
                <el-dialog
                  :visible.sync="dialogVisibleImg3"
                  top="0"
                  center
                  :modal="false"
                >
                  <img width="100%" :src="GJ_img" alt />
                </el-dialog>
                <div class="tsy">可通过拖拉动作来修改图片显示顺序</div>

                <!-- <el-upload ref="ImageChang3"  class="el_upload_above" multiple :limit="limitnum" :action="action"
                  list-type="picture-card" :http-request="uploadSectionFile" :auto-upload="true"
                  :on-preview="handlePreviewgg" :file-list="bookUrllist3" :on-error="uploadFileError"
                  :on-success="uploadFileSuccess1" :on-exceed="exceedFile"   :on-remove="handleRemove4">
                  <i class="el-icon-plus" />
                </el-upload>

                <el-dialog :visible.sync="dialogVisibleImg3" top="0" center :modal="false">
                  <img width="100%" :src="GJ_img" alt>
                </el-dialog> -->
              </el-form-item>
            </el-form>
          </el-col>
          <el-col :span="24">
            <el-form label-width="120px">
              <el-form-item label="产品尺寸图:" prop="form.longpic">
                <el-upload
                  ref="ImageChang3"
                  action="#"
                  list-type="picture-card"
                  :on-preview="handlePreviewgg22"
                  :on-remove="handleRemove5"
                  :auto-upload="false"
                  :file-list="bookUrllist5"
                  multiple
                  :on-change="handleSmallPicSuccesscpcct"
                >
                  <i class="el-icon-plus" />
                </el-upload>
                <el-dialog
                  :visible.sync="dialogVisibleImg5"
                  top="0"
                  center
                  :modal="false"
                >
                  <img width="100%" :src="xqt_img" alt />
                </el-dialog>
                <div class="tsy">可通过拖拉动作来修改图片显示顺序</div>

                <!-- <el-upload   class="el_upload_above" multiple :limit="limitnum" :action="action"
                  list-type="picture-card" :http-request="uploadSectionFile" :auto-upload="true"
                  :on-preview="handlePreviewgg22" :file-list="bookUrllist5" :on-error="uploadFileError"
                  :on-success="uploadFileSuccess2" :on-exceed="exceedFile"   :on-remove="handleRemove5">
                  <i class="el-icon-plus" />
                </el-upload>

                <el-dialog :visible.sync="dialogVisibleImg5" top="0" center :modal="false">
                  <img width="100%" :src="xqt_img" alt>
                </el-dialog> -->
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form label-position="top" label-width="120px" ref="editForm">
              <el-form-item label="选择产品" prop="uploadFile">
                <!--  -->
                <el-transfer
                  style="text-align: left; display: inline-block"
                  v-model="value4"
                  filterable
                  :right-default-checked="this.value4"
                  :titles="['未选中', '已选中']"
                  :button-texts="['取消选中', '选中产品']"
                  :format="{
                    noChecked: '${total}',
                    hasChecked: '${checked}/${total}',
                  }"
                  @change="handleChange"
                  :data="data1"
                >
                  <span slot-scope="{ option }"
                    >{{ option.key }} - {{ option.label }}</span
                  >
                </el-transfer>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="RechargeRole('form')"
          >确 定</el-button
        >
      </div>
    </el-dialog>
    <!-- 二维码 -->
    <el-dialog
      width="20%"
      title="产品二维码"
      :visible.sync="ewmShowgg"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <img
        style="margin-left: 20px"
        class="img"
        width="300px"
        height="300px"
        :src="qrImage"
      />

      <div slot="footer" class="dialog-footer">
        <el-button @click="download" icon="el-icon-download"></el-button>
      </div>
    </el-dialog>
    <!-- 选择规格 -->

    <el-dialog
      width="80%"
      title="选择规格"
      :visible.sync="specialShowgg"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <template>
        <el-form label-width="150px" ref="editForm">
          <el-form>
            <!-- <el-form-item prop="specifications.assort">
              <template slot-scope="scope">
                <el-button slot="append" @click="add" icon="el-icon-circle-plus">增加一行</el-button>
              </template>
  </el-form-item> -->
            <el-form-item label="规格组和规格值:" prop="specifications.assort">
              <template slot-scope="scope">
                <el-input
                  placeholder="请输入规格组和规格值"
                  v-model="input5"
                  class="el-input_ss"
                >
                  <el-button slot="append" @click="change5">添加</el-button>
                </el-input>
                <el-button
                  class="float_rigth"
                  slot="append"
                  @click="add"
                  icon="el-icon-circle-plus"
                  >增加一行</el-button
                >
              </template>
            </el-form-item>
          </el-form>
        </el-form>
        <el-table
          @click="formCancal"
          border
          ref="singleTable"
          :data="tableData"
          highlight-current-row
          @current-change="handleCurrentChange"
          style="width: 100%"
        >
          <el-table-column
            width="200"
            v-for="(item, index) in tableOption"
            :key="index"
            :label="item.label"
            align="center"
          >
            <template slot-scope="scope">
              <el-input
                @blur="change6(scope.$index, index)"
                clearable
                v-model="scope.row.attr_list[index].attr_name"
                placeholder="请输入"
              ></el-input>
            </template>
          </el-table-column>
          <!-- <el-table-column align="center" property="specifications" label="规格" width="120">
            <template slot-scope="scope">
              <el-input @blur="change6(scope.$index, scope.row)" v-model="scope.row.specifications"
                placeholder="请输入规格"></el-input>
            </template>
          </el-table-column> -->
          <el-table-column align="center" property="num" label="库存">
            <template slot-scope="scope">
              <el-input
                clearable
                v-model="scope.row.num"
                placeholder="请输入库存"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column align="center" property="price" label="价格">
            <template slot-scope="scope">
              <el-input
                clearable
                v-model="scope.row.price"
                placeholder="请输入价格"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column align="center" property="price" label="经销商价">
            <template slot-scope="scope">
              <el-input
                clearable
                v-model="scope.row.seriesRootId"
                placeholder="请输入经销商价"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column align="center" property="no" label="货号">
            <template slot-scope="scope">
              <el-input
                clearable
                v-model="scope.row.no"
                placeholder="请输入货号"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column align="center" property="pic" label="规格图片">
            <template slot-scope="scope">
              <div
                @click="handleCheck(scope.$index, scope.row)"
                style="display: flex"
              >
                <el-input
                  class="el-input_ss"
                  v-model="scope.row.pic"
                  placeholder="请选择图片"
                ></el-input>
                <el-upload
                  class="upload-demo"
                  action="#"
                  :on-preview="handlePreview1"
                  :on-remove="handleRemove1"
                  :on-change="handleSmallPicSuccess1"
                  multiple
                  :limit="3"
                >
                  <el-button icon="el-icon-upload"></el-button>
                </el-upload>
              </div>
              　　　　<img
                v-if="scope.row.pic"
                :src="scope.row.pic"
                width="40"
                height="40"
                class="head_pic"
              />
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="100">
            <template slot-scope="scope">
              <el-button
                @click="handleClick(scope.$index, scope.row)"
                type="danger"
                size="small"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <div slot="footer" class="dialog-footer">
          <el-button @click="formCancal">取 消</el-button>
          <el-button type="primary" @click="specificationsff('form')"
            >确 定</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import Sortable from "sortablejs";
import { mapGetters } from "vuex";
import { Loading } from "element-ui";
import { upload } from "@/api/upload";
import {
  queryProductByIdGD,
  delProduct,
  getproductList,
  createProductGD,
  getproductTypeList,
  QueryAttrGroup,
  QueryAttr,
  syncPicturesGD,
} from "@/api/CP";
// import { log } from "console";

export default {
  data() {
    return {
      fmtuploadingCount: 0,
      fmtuploadingCountcpcct: 0,
      watermarkImage:
        "data:image/png;base64,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",

      tabs: [
        {
          name: "中文简体",
          dbName: "sedysgd_cn",
        },
        {
          name: "英语",
          dbName: "sedysgd_en",
        },
      ],
      dbName: "sedysgd_cn",
      activeName: "1",
      data1: [],
      value: [],
      value4: [],
      tableOption: [],
      input5: "",
      specialShowgg: false,
      isClear: false, //设置为true的时候，这个可以用this.wangEditorDetail=''来替代
      wangEditorDetail: "",
      pickerOptions: {
        shortcuts: [
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      dialogTitle: "新增商品",
      ewmShowgg: false,
      specialShow: false,
      formList: {},
      data: [],
      pageSize: 10,
      value1: "",
      value2: "",
      input2: "",
      input3: "",
      input4: "",
      input5: "",
      currentPage: 1,
      handleSelectionChangeList: [],
      bookUrllist: [],
      dialogVisibleImg: false,
      formData: {
        startTime: "",
        endTime: "",
      },
      Product: {
        catId: "",
        name: "",
        subTitle: "",
        unit: "",
        sort: "",
        virtualSales: "",
        confineCount: 1,
        weight: "",
        coverPic: "",
        price: "",
        costPrice: "",
        originalPrice: "",
        service: "",
        isNegotiable: "",
        goodsNum: "",
        caseJiluId: "",
        attr: "",
        integral: "",
        detail: "",
        hotCakes: 0,
      },
      specifications: {
        price: "",
        assort: "",
        stock: "",
        price: "",
        Article: "",
        url: "",
      },
      total: 1,
      // 富文本器的配置
      config: {
        // 上传图片的配置
        uploadImage: {
          // url: "/api/upload/fileUpload", //服务器地址
          url: `${process.env.VUE_APP_BASE_API}/uploadCloud?filename=11111`,
          name: "file", // 参考接口文档的文件上传的参数
          // headers: { Authorization: localStorage.getItem("mytoken") }, //配置token
          // res是结果，insert方法会把内容注入到编辑器中，res.data.url是资源地址
          uploadSuccess(res, insert) {
            console.log(res); // 是否上传成功的响应结果和url地址
            insert(res.data.yunUrl);
          },
        },
        // 上传视频的配置
        // uploadVideo: {
        //     url: `${process.env.VUE_APP_BASE_API}upload/fileUpload`,
        //     name: 'file',
        //     uploadSuccess(res, insert) {
        //         insert(res.data.data)
        //     }
        // }
      },
      classify: [],
      obj: {
        num: "",
        price: "",
        no: "",
        pic: "",
      },

      attrGroup_ID: [],
      tableData: [
        {
          no: "",
          attr_list: [],

          price: "",
          num: "",
          pic: "",
          seriesRootId: "",
        },
      ],
      SEDJJXCX_LHFW_UserInformation_HT: {},
      title: "",
      editFormVisible: false,
      classification: [],

      data: [],
      defaultProps: {
        children: "children",
        label: "name",
      },
      MT_LHFW_UserInformation_HT: {},
      gg: {
        name: "",
        displayOrder: "",
        status: 1,
      },
      AL: {
        name: "",
        displayOrder: "",
        level: "",
        pId: "",
        status: 1,
      },
      bookUrllistsjs: [],
      dialogVisibleImgsjs: false,
      form: {
        stylePhoto: "",
        styleName: "",
        styleCountry: "",
        styleAbout: "",
        name: "",
        yuliutwo: 2,
        slogan: "",
        seriesSynopsis: "",
        seriesPhoto: "",
        seriesVideo: "",
        seriesRecomment: 1,
        status: 1,
        paixu: 0,
        typeId: [],
        caseJiluId: "",
      },
      bookUrllist2: [],
      bookUrllist1: [],
      Subitemshow: false,
      //上传后的视频列表
      fileList: [],
      // 允许的视频类型
      fileType: [
        "pdf",
        "doc",
        "docx",
        "xls",
        "xlsx",
        "txt",
        "png",
        "jpg",
        "bmp",
        "jpeg",
        "mp4",
        "ogg",
        "flv",
        "avi",
        "wmv",
        "rmvb",
        "mov",
      ],
      // 运行上传视频大小，单位 M
      fileSize: 50,
      // 附件数量限制
      fileLimit: 1,
      //请求头
      headers: { "Content-Type": "multipart/form-data" },
      dialogVisibleImg: false,
      dialogVisibleImg211: false,
      curPage: 1,
      currentPage: 1,
      total: 1,
      pageSize: 10,
      bookUrllist: [],
      bookUrllist1: [],
      bookUrllist2: [],
      bookUrllist3: [],
      bookUrllist4: [],
      bookUrllist5: [],
      bookUrllist6: [],
      form: {
        name: "",
        productGuige: [],
        productModel: "",
        productVr: "",
        yuliuone: "",
        productMoney: "",
        seriesRootId: "",
        productSynopsis: "",
        yuliutow: "",
        productImage: "",
        productUrl2: "",
        productImages: [],
        status: 0,
        yuliutwo: 2,
        productView: "",
        productId: [],
        preview: "",
        productUrl: "",
        productPaixu: "",
        qrImage: "",
        style: "",
        space: "",
        colour: "",
        series: "",
        longpic: [],
        formNum: "",
      },
      productUrlindex: 1,
      productImageslindex: 1,
      curPage: 1,
      limitnum: 100,
      checkList: [[], [], [], [], [], [], [], [], [], []],
      action: process.env.VUE_APP_BASE_API + "/uploadCloud?filename=11111",
      tableData: [
        {
          no: "",
          attr_list: [],

          price: "",
          num: "",
          pic: "",
          seriesRootId: "",
        },
      ],
      IMGindex: 0,
      GJ_img: "",
      gg_img: "",
      xqt_img: "",
      input1: "",
      input2: "",
      input3: "",
      input4: "",
      GGlist: [],
      XLList: [],
      dialogVisibleImg1: false,
      dialogVisibleImg2: false,
      dialogVisibleImg3: false,
      dialogVisibleImg4: false,
      dialogVisibleImg5: false,
      isHandlingChange: false, // 添加一个标识来判断是否正在处理change事件
      qrImage: "",
      ewmShowgg: false,
      projectName: "",
      handleSelectionChangeList: [],
      cpid: "",
    };
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
  },
  methods: {
    synchronous(row) {
      this.$confirm("请检查一遍当前选择的语言是否中文简体！", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          var params = new URLSearchParams();
          params.append("id", row.id);
          params.append("entity", "productContentsGD");
          params.append("dbList", this.$dbList);

          params.append(
            "parame",
            "caseJiluIdList,productImages,,qrImage,productUrl2,style,series,stylePhoto,yuliuone,productVr,productModel,productUrl,productImage,longpic"
          );
          syncPicturesGD(params)
            .then((res) => {
              if (res.data.code == "1") {
                this.$message({
                  type: "success",
                  message: "同步成功！",
                });
                this.getqueryCaseType();
              } else {
              }
            })
            .catch(() => {});
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    tabClick(el) {
      const currentTab = JSON.parse(el.$attrs.value);
      let e = {
        id: this.cpid,
      };
      this.dbName = currentTab.dbName;
      this.handleEditzt(e);
    },
    // 添加规格确定
    specificationsff() {
      if (this.tableData && this.tableData[0] && this.tableData[0].num == "") {
        this.$message({
          type: "warning",
          message: "请输入规格组和规格值",
        });
        return;
      }
      for (let a in this.tableData) {
        if (
          this.tableData[a].attr_list &&
          this.tableData[a].attr_list.length == 0
        ) {
          let obj = {
            attr_group_id: 1, //表头id
            attr_group_name: "规格", //表头
            attr_id: 1,
            attr_name: "默认",
          };
          this.tableData[a].attr_list.push(obj);
        }
      }
      this.form.productGuige = JSON.stringify(this.tableData);
      this.specialShowgg = false;
      this.input5 = "";
    },
    // 商品二维码
    QRcode(row) {
      this.qrImage = row.qrImage;
      this.ewmShowgg = true;
      this.projectName = row.name + "-" + row.productModel;
    },
    // 下载二维码
    download(name, url) {
      var image = new Image();
      image.setAttribute("crossOrigin", "anonymous");
      var _this = this;
      image.onload = function () {
        var canvas = document.createElement("canvas");
        canvas.width = image.width;
        canvas.height = image.height;
        var context = canvas.getContext("2d");
        context.drawImage(image, 0, 0, image.width, image.height);
        var url = canvas.toDataURL("image/png"); //得到图片的base64编码数据
        var a = document.createElement("a"); // 生成一个a元素
        var event = new MouseEvent("click"); // 创建一个单击事件
        a.download = _this.projectName || name; // 设置图片名称
        a.href = url; // 将生成的URL设置为a.href属性
        a.dispatchEvent(event); // 触发a的单击事件
      };
      image.src = this.qrImage ? this.qrImage : url;
    },
    leading() {
      if (
        this.handleSelectionChangeList &&
        this.handleSelectionChangeList.length
      ) {
        this.handleSelectionChangeList.forEach((item) => {
          let bb = item.name + "-" + item.productModel;
          this.download(bb, item.qrImage);
        });
      } else {
        this.$message({
          type: "warning",
          message: "请选择你下载的产品二维码",
        });
      }
    },
    // 删除产品
    deleteclassification(row) {
      this.$confirm("此操作将永久删除产品, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          var params = new URLSearchParams();
          params.append("id", row.id);
          params.append("entity", "productContentsGD");
          params.append("dbList", this.$dbList);

          delProduct(params)
            .then((res) => {
              if (res.data.code == "1") {
                this.$message({
                  type: "success",
                  message: "删除成功！",
                });
                this.getqueryCaseType();
              } else {
              }
            })
            .catch(() => {});
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    getgetselectCase1() {
      this.input1 = "";
      this.input2 = "";
      this.input3 = "";
      this.input5 = "";
      this.input4 = "";
      this.curPage = 1;
      this.dbName = "sedysgd_cn";
      this.getqueryCaseType();
    },
    // 上下架筛选
    change1(row) {
      this.activeName = this.dbName == "sedysgd_cn" ? "1" : "2";

      this.getqueryCaseType();
      return;
      var params = new URLSearchParams();
      params.append("dbName", this.dbName);

      params.append("curPage", this.curPage);
      if (this.input1) {
        params.append("status", this.input1);
      }

      if (this.input2) {
        params.append("size", this.input2);
      }
      if (this.input3) {
        params.append("series", this.input3);
      }
      if (this.input4) {
        params.append("keyword", this.input4);
      }
      if (this.input5) {
        params.append("yuliutwo", this.input5);
      }

      getproductList(params)
        .then((res) => {
          this.total = res.data.totalNum;
          this.currentPage = 1;

          if (res.data.code == "1") {
            this.data = res.data.list;
          } else {
            this.data = [];
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },

    onPauser() {
      this.playing = false;
    },
    onPaly() {
      this.playing = true;
    },
    getshow(e) {
      console.log(this.checkList);
      // if (this.checkList[e].length > 1) {
      //   this.checkList[e].splice(0, 1)
      // }
    },
    addincrease() {
      this.editFormVisible = true;
      this.fmtuploadingCount = 0;
      this.fmtuploadingCountcpcct = 0;
      this.title = "添加";
      this.value4 = [];
      this.bookUrllistsjs = [];
      this.bookUrllist5 = [];

      this.dbName = "sedysgd_cn";
      this.activeName = "1";
      this.form = {
        stylePhoto: "",
        styleName: "",
        styleCountry: "",
        styleAbout: "",
        name: "",
        productModel: "",
        productVr: "",
        yuliuone: "",
        productMoney: "",
        seriesRootId: "",
        productSynopsis: "",
        yuliutow: "",
        productImage: "",
        productImages: [],
        status: 0,
        yuliutwo: 2,
        productView: "",
        productId: [],
        preview: "",
        productUrl: "",
        productPaixu: "",
        qrImage: "",
        style: "",
        space: "",
        colour: "",
        space: "",
        series: "",
        longpic: [],
        formNum: "",
        isOwn: "0",
      };
      this.activeName = "1";
      this.bookUrllist = [];
      this.bookUrllist1 = [];
      this.bookUrllist2 = [];
      this.bookUrllist3 = [];
      this.bookUrllist4 = [];

      this.checkList = [[], [], [], [], [], [], [], [], [], []];
      this.tableData = [
        {
          attr_list: [],
          num: "",
          price: "",
          no: "",
          pic: "",
          specifications: "",
          seriesRootId: "",
        },
      ];
      this.tableOption = [];
    },
    // 编辑
    handleEditzt(e) {
      var params = new URLSearchParams();
      this.cpid = e.id;
      params.append("id", e.id);
      params.append("dbName", this.dbName);
      queryProductByIdGD(params)
        .then((res) => {
          if (res.data.code == "1") {
            let row = res.data.records[0];
            this.form = row;

            if (row.longpic == "" || row.longpic == undefined) {
              if (row.longpic == "" || row.longpic == undefined) {
                this.form.longpic = [];
                this.bookUrllist5 = [];
                this.fmtuploadingCountcpcct = 0;
              } else {
                this.form.longpic = JSON.parse(row.longpic);
                this.bookUrllist5 = this.form.longpic;
                this.fmtuploadingCountcpcct = this.form.longpic.length;
              }
            } else {
              this.form.longpic = JSON.parse(row.longpic);
              this.bookUrllist5 = this.form.longpic;
              this.fmtuploadingCountcpcct = this.form.longpic.length;
            }
            this.form = Object.assign({}, row); // copy obj

            if (
              row.space ||
              row.series ||
              row.size ||
              row.colour ||
              row.style
            ) {
              row.space = row.space.split(",").map(Number);
              row.series = row.series.split(",").map(Number);
              row.size = row.size.split(",").map(Number);
              row.colour = row.colour.split(",").map(Number);
              row.style = row.style.split(",").map(Number);
            }
            if (row.caseJiluIdList) {
              this.value4 = row.caseJiluIdList.split(",").map(Number);
            }
            if (row.productGuige) {
              this.tableOption = [];
              this.tableData = JSON.parse(row.productGuige);
              let a = [];
              for (let i in this.tableData) {
                for (let j in this.tableData[i].attr_list) {
                  this.tableData[i].specifications =
                    this.tableData[i].attr_list[j].attr_name;
                  if (
                    this.attrGroup_ID.indexOf(
                      this.tableData[i].attr_list[j].attr_group_id
                    ) == -1
                  ) {
                    this.attrGroup_ID.push(
                      this.tableData[i].attr_list[j].attr_group_id
                    );
                  }
                  if (
                    a.indexOf(this.tableData[i].attr_list[j].attr_group_name) ==
                    -1
                  ) {
                    a.push(this.tableData[i].attr_list[j].attr_group_name);
                  }
                }
              }
              for (let i in a) {
                let obj = {
                  label: a[i],
                };
                this.tableOption.push(obj);
              }
              console.log(this.tableOption, "2112112");
            } else {
              this.tableOption = [];

              this.tableData = [
                {
                  no: "",
                  attr_list: [],

                  price: "",
                  num: "",
                  pic: "",
                  seriesRootId: "",
                },
              ];
            }

            if (
              (this.tableData && this.tableData[0].num) ||
              this.tableData[0].price
            ) {
              this.form.caseJiluId = true;
            }

            if (row.series && row.series.length > 0) {
              this.checkList[0] = row.series;
            }
            if (row.style && row.style.length > 0) {
              this.checkList[1] = row.style;
            }
            if (row.space && row.space.length > 0) {
              this.checkList[2] = row.space;
            }

            if (row.colour && row.colour.length > 0) {
              this.checkList[3] = row.colour;
            }

            this.title = "编辑";

            this.editFormVisible = true;
            if (row.productImage) {
              this.bookUrllist = [
                {
                  url: row.productImage,
                },
              ];
            }
            if (row.stylePhoto) {
              this.bookUrllistsjs = [
                {
                  url: row.stylePhoto,
                },
              ];
            }
            if (row.productUrl2) {
              this.bookUrllist6 = [
                {
                  url: row.productUrl2,
                },
              ];
            }
            if (row.qrImage) {
              this.bookUrllist2 = [
                {
                  url: row.qrImage,
                },
              ];
            }

            if (row.productUrl) {
              this.bookUrllist1 = [
                {
                  url: row.productUrl,
                },
              ];
            }

            if (row.productImages == "" || row.productImages == undefined) {
              if (row.productImages == "" || row.productImages == undefined) {
                this.form.productImages = [];
                this.bookUrllist3 = [];
                this.fmtuploadingCount = 0;
              } else {
                this.form.productImages = JSON.parse(row.productImages);
                this.fmtuploadingCount = this.form.productImages.length;

                this.bookUrllist3 = this.form.productImages;
              }
            } else {
              this.form.productImages = JSON.parse(row.productImages);
              this.fmtuploadingCount = this.form.productImages.length;
              this.bookUrllist3 = this.form.productImages;
            }

            this.initDragSort();
          } else {
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    //上传视频之前
    beforeUpload1(file) {
      if (file.type != "" || file.type != null || file.type != undefined) {
        //截取视频的后缀，判断视频类型
        const FileExt = file.name.replace(/.+\./, "").toLowerCase();
        //计算视频的大小
        const isLt5M = file.size / 1024 / 1024 < 500; //这里做视频大小限制
        //如果大于50M
        if (!isLt5M) {
          this.$message.error("上传视频大小不能超过 500MB!");
          return false;
        }
        //如果视频类型不在允许上传的范围内
        if (this.fileType.includes(FileExt)) {
          return true;
        } else {
          this.$message.error("上传视频格式不正确!");
          return false;
        }
      }
    },
    //上传了的视频给移除的事件，由于我没有用到默认的展示，所以没有用到
    handleRemove() {},
    //这是我自定义的移除事件
    handleClose(i) {
      this.fileList.splice(i, 1); //删除上传的视频
      if (this.fileList.length == 0) {
        //如果删完了
        this.fileflag = true; //显示url必填的标识
        this.$set(this.rules.url, 0, {
          required: true,
          validator: this.validatorUrl,
          trigger: "blur",
        }); //然后动态的添加本地方法的校验规则
      }
    },
    //超出视频个数的回调
    handleExceed() {
      this.$message({
        type: "warning",
        message: "超出最大上传视频数量的限制！",
      });
      return;
    },
    //上传视频的事件
    uploadFile(item) {
      this.loading = Loading.service({
        lock: true,
        text: "视频上传中........",
        // background: 'rgba(0, 0, 0, 0.7)',
        target: document.querySelector("#loadingDiv"),
      });
      //上传视频的需要formdata类型;所以要转
      let FormDatas = new FormData();
      FormDatas.append("file", item.file);
      upload(FormDatas).then((res) => {
        if (res.data.code == "0") {
          setTimeout(() => {
            this.loading.close();
          }, 2000);
          this.form.productView = res.data.yunUrl;
          this.$set(this.form, "videoUrl", this.form.productView + "/" + 1);
          console.log(this.form.productView);
          // this.Product.filename=res.data.fileNameOld;
        }
      });

      // 	})
    },
    //上传成功后的回调
    handleSuccess() {},
    // 添加产品
    RechargeRole() {
      // if (!this.form.name) {
      //   this.$message({
      //     showClose: true,
      //     message: '请输入产品名称！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (!this.form.productModel) {
      //   this.$message({
      //     showClose: true,
      //     message: '请输入产品型号！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (!this.form.formNum) {
      //   this.$message({
      //     showClose: true,
      //     message: '请输入产品面数！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (!this.form.productVr) {
      //   this.$message({
      //     showClose: true,
      //     message: '请输入产品vr链接！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (!this.form.productView) {
      //   this.$message({
      //     showClose: true,
      //     message: '请输入产品视频链接！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (!this.form.productSynopsis) {
      //   this.$message({
      //     showClose: true,
      //     message: '请输入产品简介！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (this.checkList && this.checkList[0].length == 0) {
      //   this.$message({
      //     showClose: true,
      //     message: '请选择产品！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (this.checkList && this.checkList[1].length == 0) {
      //   this.$message({
      //     showClose: true,
      //     message: '请选择系列！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (this.checkList && this.checkList[2].length == 0) {
      //   this.$message({
      //     showClose: true,
      //     message: '请选择色彩！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (this.checkList && this.checkList[3].length == 0) {
      //   this.$message({
      //     showClose: true,
      //     message: '请选择规格！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (this.checkList && this.checkList[4].length == 0) {
      //   this.$message({
      //     showClose: true,
      //     message: '请选择系列！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (!this.form.productImage) {
      //   this.$message({
      //     showClose: true,
      //     message: '请上传产品主图！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (!this.form.productUrl) {
      //   this.$message({
      //     showClose: true,
      //     message: '请上传产品单片图！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (!this.form.qrImage) {
      //   this.$message({
      //     showClose: true,
      //     message: '请上传二维码！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (this.form.productImages && this.form.productImages.length == 0) {
      //   this.$message({
      //     showClose: true,
      //     message: '请上传产品展示图！',
      //     type: 'warning'
      //   });
      //   return
      // }

      console.log(this.form.productImages);
      // if(this.title =='添加'){
      if (this.form.productImages && this.form.productImages.length != 0) {
        let obj = {};
        this.form.productImages = this.form.productImages.reduce(function (
          item,
          next
        ) {
          obj[next.url] ? "" : (obj[next.url] = true && item.push(next));
          return item;
        },
        []);
      }
      if (this.form.longpic && this.form.longpic.length != 0) {
        let obj = {};
        this.form.longpic = this.form.longpic.reduce(function (item, next) {
          obj[next.url] ? "" : (obj[next.url] = true && item.push(next));
          return item;
        }, []);
      }
      // if (this.form.productUrl && this.form.productUrl.length != 0) {
      //   let obj = {}
      //   this.form.productUrl = this.form.productUrl.reduce(function (item, next) {
      //     obj[next.url] ? '' : (obj[next.url] = true && item.push(next))
      //     return item
      //   }, [])
      // }
      var cc = this.title == "编辑" ? "修改中..." : "创建中...";

      this.loading = Loading.service({
        lock: true,
        text: cc,
        // background: 'rgba(0, 0, 0, 0.7)',
        target: document.querySelector("#loadingDiv"),
      });
      this.loading.close();

      this.form.caseJiluId = this.form.caseJiluId ? "1" : "0";

      this.tableData.forEach((item) => {
        item.images = JSON.stringify(item.images);
      });
      this.form.series = this.checkList[0]
        .filter((value) => value !== 0)
        .join();
      this.form.style = this.checkList[1].filter((value) => value !== 0).join();
      // this.form.space = this.checkList[2].join()
      // this.form.colour = this.checkList[3].join()

      this.form.productImages =
        this.form.productImages && this.form.productImages.length == 0
          ? ""
          : JSON.stringify(this.form.productImages);
      this.form.longpic =
        this.form.longpic && this.form.longpic.length == 0
          ? ""
          : JSON.stringify(this.form.longpic);
      // this.form.productUrl = this.form.productUrl && this.form.productUrl.length == 0 ? '' : JSON.stringify(this.form.productUrl)
      let productModelList = "";
      if (this.form.caseJiluIdList && this.form.caseJiluIdList.length > 0) {
        const modelArr = this.data1.filter((item) =>
          this.form.caseJiluIdList.includes(item.key)
        );
        productModelList = modelArr.map((item) => item.productModel).join(",");
      }
      var params = new URLSearchParams();
      params.append("dbName", this.dbName);
      params.append("flag", "save");
      params.append("dbList", this.$dbList);

      params.append(
        "productGuige",
        this.form.productGuige ? this.form.productGuige : ""
      );
      params.append("name", this.form.name ? this.form.name : "");
      params.append(
        "productSynopsis",
        this.form.productSynopsis ? this.form.productSynopsis : ""
      );
      params.append("yuliutow", this.form.yuliutow ? this.form.yuliutow : "");
      params.append(
        "productModel",
        this.form.productModel ? this.form.productModel : ""
      );
      params.append("productModelList", productModelList);
      params.append(
        "productImage",
        this.form.productImage ? this.form.productImage : ""
      );
      params.append(
        "productUrl2",
        this.form.productUrl2 ? this.form.productUrl2 : ""
      );
      params.append(
        "productImages",
        this.form.productImages ? this.form.productImages : ""
      );
      params.append(
        "productUrl",
        this.form.productUrl ? this.form.productUrl : ""
      );
      params.append("status", this.form.status);
      params.append("caseJiluId", this.form.caseJiluId);

      params.append("formNum", this.form.formNum ? this.form.formNum : "");
      params.append(
        "productVr",
        this.form.productVr ? this.form.productVr : ""
      );
      params.append("yuliuone", this.form.yuliuone ? this.form.yuliuone : "");
      params.append(
        "productMoney",
        this.form.productMoney ? this.form.productMoney : ""
      );
      params.append(
        "seriesRootId",
        this.form.seriesRootId ? this.form.seriesRootId : ""
      );
      params.append("qrImage", this.form.qrImage ? this.form.qrImage : "");
      params.append("size", this.form.size ? this.form.size : "");
      params.append("craft", this.form.craft ? this.form.craft : "");
      params.append("unit", this.form.unit ? this.form.unit : "");
      params.append("style", this.form.style ? this.form.style : "");
      params.append("space", this.form.space ? this.form.space : "");
      params.append("series", this.form.series ? this.form.series : "");
      params.append("colour", this.form.colour ? this.form.colour : "");
      params.append(
        "productPaixu",
        this.form.productPaixu ? this.form.productPaixu : ""
      );
      params.append("longpic", this.form.longpic ? this.form.longpic : "");
      params.append("yuliutwo", this.form.yuliutwo ? this.form.yuliutwo : "");
      params.append(
        "productView",
        this.form.productView ? this.form.productView : ""
      );
      params.append("productGuige", JSON.stringify(this.tableData));
      params.append("id", this.form.id ? this.form.id : "");
      params.append("formNum", this.form.formNum ? this.form.formNum : "");
      params.append("isOwn", this.form.isOwn ? this.form.isOwn : "");
      params.append(
        "caseJiluIdList",
        this.form.caseJiluIdList ? this.form.caseJiluIdList : ""
      );
      params.append(
        "stylePhoto",
        this.form.stylePhoto ? this.form.stylePhoto : ""
      );
      params.append(
        "styleName",
        this.form.styleName ? this.form.styleName : ""
      );
      params.append(
        "styleCountry",
        this.form.styleCountry ? this.form.styleCountry : ""
      );
      params.append(
        "styleAbout",
        this.form.styleAbout ? this.form.styleAbout : ""
      );

      createProductGD(params)
        .then((res) => {
          if (res.data.code == "1") {
            this.$message({
              type: "success",
              message: this.title == "编辑" ? "修改成功" : "添加成功",
            });

            this.getqueryCaseType();
            //
            this.editFormVisible = false;
            this.form = {
              stylePhoto: "",
              styleName: "",
              styleCountry: "",
              styleAbout: "",
              name: "",
              caseJiluId: "",
              productModel: "",
              productVr: "",
              yuliuone: "",
              productMoney: "",
              seriesRootId: "",
              productSynopsis: "",
              yuliutow: "",
              productImage: "",
              productUrl2: "",
              productImages: [],
              status: 0,
              productView: "",
              productId: [],
              preview: "",
              productUrl: "",
              productPaixu: 0,
              qrImage: "",
              style: "",
              space: "",
              colour: "",
              space: "",
              series: "",
              longpic: [],
              formNum: "",
              formNum: "",
              craft: "",
              unit: "",
              isOwn: "0",
              yuliutwo: 2,
              caseJiluIdList: "",
            };
            this.dbName = "sedysgd_cn";

            this.activeName = "1";
            this.bookUrllist = [];
            this.bookUrllist1 = [];
            this.bookUrllist2 = [];
            this.bookUrllist3 = [];
            this.bookUrllist4 = [];
            this.bookUrllist5 = [];
            this.bookUrllistsjs = [];
            this.value4 = [];
            this.form.status = 0;
            this.form.productPaixu = 0;
            this.form.solongpicrt = "";
            this.activeName = "1";
            this.checkList = [[], [], [], [], [], [], [], [], [], []];
          } else {
          }
        })
        .catch((err) => {
          console.log(err);
          this.loading = false;
        });
    },
    // 删除规格
    handleClick(index, row) {
      this.tableData.splice(index, 1);

      // if (index == '0') {
      //   this.$message({
      //     type: 'warning',
      //     message: '规格中最后一项不可删除！'
      //   })
      // } else {

      // }
    },
    handleUploadAgain() {
      this.isHandlingChange = false;
    },
    handleCheck(index, row) {
      console.log(index);
      this.IMGindex = index;
    },
    handlePreview11(file) {
      this.dialogVisibleImg4 = true;
      this.gg_img = file.url;
    },

    handlePreviewgg(file) {
      this.dialogVisibleImg3 = true;
      this.GJ_img = file.url;
    },
    handlePreviewgg22(file) {
      this.dialogVisibleImg5 = true;
      this.xqt_img = file.url;
    },
    handlePreview(file) {
      this.form.productImage = file.url;
      this.dialogVisibleImg = true;
    },
    handlePreviewsjs(file) {
      this.form.stylePhoto = file.url;
      this.dialogVisibleImgsjs = true;
    },
    handlePreviewcp(file) {
      this.form.productUrl = file.url;
      this.dialogVisibleImg1 = true;
    },
    handlePreview1(file) {
      this.form.qrImage = file.url;
      this.dialogVisibleImg2 = true;
    },
    handlePreview22(file) {
      this.form.productUrl2 = file.url;
      this.dialogVisibleImg211 = true;
    },
    // 点击取消
    formCancal() {
      this.specialShowgg = false;
      // this.$refs.vueEditor.editor.root.innerHTML = '';
      // this.tableOption = [];
      // this.bookUrllist = [];

      // if (this.dialogTitle == '新增商品') {
      //   this.tableData = [{
      //     attr_list: [],
      //     num: '',
      //     price: '',
      //     no: '',
      //     pic: '',
      //     specifications: ''
      //   }]
      // }
    },
    handleCurrentChange() {},
    // 添加一行
    add() {
      let arr = [];
      for (let i = 0; i < this.tableOption.length; i++) {
        let obj = {
          attr_group_id: this.attrGroup_ID[i], //表头id
          attr_group_name: this.tableOption[i].label, //表头
          attr_id: "",
          attr_name: "",
        };
        arr.push(obj);
      }
      let obj = {
        attr_list: arr,
        num: "",
        price: "",
        no: "",
        pic: "",
      };
      this.tableData.push(obj);
    },
    // 关于上传pdf部分 start
    handleSuccess(res, file) {
      // 上传成功的钩子
      this.form.yuliuone = file.response.yunUrl;
    },
    fileChange(file, fileList) {
      //文件状态改变时的钩子，添加文件、上传成功和上传失败时都会被调用

      this.fileList = fileList;
    },
    8(file, fileList) {
      //文件列表移除文件时的钩子
      this.form.yuliuone = "";
    },
    handlePreview1111(file) {
      //点击文件列表中已上传的文件时的钩子
    },
    handleExceed(files, fileList) {
      this.$message.warning(
        `当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${
          files.length + fileList.length
        } 个文件`
      );
    },
    beforeUpload(file) {
      //文件上传之前的钩子函数
      this.file = file;
      this.fileName = file.name;
      // this.fileSize = file.size;
      const extension = file.name.split(".").slice(-1) == "pdf";
      if (!extension) {
        this.$message.warning("上传模板只能是pdf格式!");
        return false;
      }
      // let reader = new FileReader();
      // reader.readAsDataURL(file);
      // console.log(new FileReader().readAsDataURL(file),'reader.readAsDataURL(file)')
      // console.log(reader.result,'reader.result')

      // let that = this;
      // reader.onload = function() {
      //   that.fileData = reader.result;
      // };
      // console.log(that.fileData,'that.fileData')
      // return false; // 返回false不会自动上传
    },
    // 删除文件之前的钩子
    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`);
    },
    formCancal2() {},
    formCancal1() {},
    uploadFileError(res, file, fileList) {
      this.loading = Loading.service({
        lock: true,
        text: "上传中...",
        // background: 'rgba(0, 0, 0, 0.7)',
        target: document.querySelector("#loadingDiv"),
      });
    },
    handleBeforeUpload(file) {
      this.loading = Loading.service({
        lock: true,
        text: "上传中...",
        // background: 'rgba(0, 0, 0, 0.7)',
        target: document.querySelector("#loadingDiv"),
      });
    },
    uploadFileSuccess(res, file, fileList) {
      if (res.code == 0) {
        this.loading.close();

        fileList.forEach((att) => {
          if (att.response) {
            let obj = {
              url: "",
            };
            if (file.response.yunUrl) {
              this.form.productUrl = file.response.yunUrl;
            }
          }
        });
      }
    },
    uploadFileSuccess1(res, file, fileList) {
      // this.form.productImages = [];
      // if (res.code == 0) {
      //   fileList.forEach((att) => {
      //     if (att.response) {
      //       let obj = {
      //         url: ""
      //       }
      //       obj.url = file.response.yunUrl;
      //       this.form.productImages.push(obj)
      //     }
      //   })
      // }
    },
    uploadFileSuccess2(res, file, fileList) {
      if (res.code == 0) {
        fileList.forEach((att) => {
          if (att.response) {
            let obj = {
              url: "",
            };
            obj.url = file.response.yunUrl;
            this.form.longpic.push(obj);
          }
        });
      }
    },
    exceedFile(res, file, fileList) {
      this.loading = Loading.service({
        lock: true,
        text: "上传中...",
        // background: 'rgba(0, 0, 0, 0.7)',
        target: document.querySelector("#loadingDiv"),
      });
      this.$message.error("只能上传" + this.limitnum + "个文件");
    },
    handleSmallPicSuccess(res, file, fileList) {
      if (file && file.length == 2) {
        file.shift();
      }
      this.uploadImg(res, 1);
    },
    handleSmallPicSuccesssjs(res, file, fileList) {
      if (file && file.length == 2) {
        file.shift();
      }
      this.uploadImg(res, 8);
    },
    handleSmallPicSuccess222(res, file, fileList) {
      if (file && file.length == 2) {
        file.shift();
      }
      this.uploadImg(res, 7);
    },
    handleSmallPicSuccessgg(res, file, fileList) {
      // 处理上传成功逻辑
      this.uploadImg(res, 6);
      // ...

      // 标记已经上传过文件
    },
    handleSmallPicSuccess1(res, file, fileList) {
      this.uploadImg(res, 6);
    },
    handleSmallPicSuccess2(res, file, fileList) {
      if (file && file.length == 2) {
        file.shift();
      }
      this.uploadImg(res, 3);
    },
    handleSmallPicSuccess3(res, file, fileList) {
      this.uploadImg(res, 4);
    },
    handleSmallPicSuccess4(res, file, fileList) {
      this.uploadImg(res, 5);
    },
    uploadImg(file, type) {
      let that = this;
      let formData = new FormData();
      formData.append("file", file.raw);
      that.loading = Loading.service({
        lock: true,
        text: "上传中...",
        // background: 'rgba(0, 0, 0, 0.7)',
        target: document.querySelector("#loadingDiv"),
      });
      upload(formData)
        .then((res) => {
          if (res.data.code == 0) {
            // that.loading.close();
            if (type == 1) {
              that.form.productImage = res.data.yunUrl;
              that.loading.close();
            } else if (type == 2) {
              let obj = {
                url: res.data.yunUrl,
              };
              that.form.productUrl = res.data.yunUrl;
              that.loading.close();
            } else if (type == 3) {
              that.form.qrImage = res.data.yunUrl;
              that.loading.close();
            } else if (type == 4) {
              let obj = {
                url: res.data.yunUrl,
              };

              that.form.productImages.push(obj);
              that.bookUrllist3.push(obj);
              //           console.log( that.bookUrllist3);
              //           console.log( that.form.productImages);
              // console.log( that.form.productImages.length);
              // console.log( that.fmtuploadingCount);
              const seenUrls = new Set();
              const uniqueArray = that.bookUrllist3.filter((item) => {
                if (seenUrls.has(item.url)) {
                  return false; // 如果已经遇到过这个 url，就跳过这个对象
                } else {
                  seenUrls.add(item.url); // 否则，添加到 Set 中，并保留这个对象
                  return true;
                }
              });
              that.bookUrllist3 = uniqueArray;
              this.initDragSort();
              if (that.fmtuploadingCount === uniqueArray.length) {
                that.loading.close();
              }
            } else if (type == 5) {
              let obj = {
                url: res.data.yunUrl,
              };
              that.form.longpic.push(obj);
              that.bookUrllist5.push(obj);
              const seenUrls1 = new Set();
              const uniqueArray1 = that.bookUrllist5.filter((item) => {
                if (seenUrls1.has(item.url)) {
                  return false; // 如果已经遇到过这个 url，就跳过这个对象
                } else {
                  seenUrls1.add(item.url); // 否则，添加到 Set 中，并保留这个对象
                  return true;
                }
              });
              that.bookUrllist5 = uniqueArray1;

              this.initDragSort();
              if (that.fmtuploadingCountcpcct === uniqueArray1.length) {
                that.loading.close();
              }
            } else if (type == 6) {
              this.tableData[this.IMGindex].pic = res.data.yunUrl;
              that.loading.close();
            } else if (type == 7) {
              that.form.productUrl2 = res.data.yunUrl;
              that.loading.close();
            } else if (type == 8) {
              that.form.stylePhoto = res.data.yunUrl;
              that.loading.close();
            }
          }
        })
        .catch((err) => {
          if (type == 1) {
          } else if (type == 2) {
          } else if (type == 3) {
          } else if (type == 4) {
            that.fmtuploadingCount--; // 减少正在上传的图片数量计数
            if (that.fmtuploadingCount === 0) {
              that.loading.close();
            }
          } else if (type == 5) {
          } else if (type == 6) {
          } else if (type == 7) {
          } else if (type == 8) {
          }
        });
    },
    tableRowClassName({ row, rowIndex }) {
      if (rowIndex === 1) {
        return "warning-row";
      } else if (rowIndex === 3) {
        return "success-row";
      }
      return "";
    },
    handleRemove1(file) {
      this.form.productImage = "";
    },
    handleRemovesjs() {
      this.form.stylePhoto = "";
    },
    handleRemove22() {
      this.form.productUrl2 = "";
    },
    handleRemove8() {},
    handleRemove2(file) {
      // 1.获取将要删除图片的临时路径
      const filePath = file.url;
      // 2.从pics数组中，找到图片对应的索引值
      // const i = this.form.productUrl.findIndex((x) => x.url === filePath);
      // 3.调用splice方法，移除图片信息
      this.form.productUrl = "";
    },
    handleRemove3(file) {
      const filePath = file.url;

      this.tableData[this.IMGindex].images;
      const i = this.tableData[this.IMGindex].images.findIndex(
        (x) => x.url === filePath
      );
      // 3.调用splice方法，移除图片信息
      this.tableData[this.IMGindex].images.splice(i, 1);
      console.log(this.tableData[this.IMGindex].images);
    },
    handleRemove4(file) {
      // 1.获取将要删除图片的临时路径
      const filePath = file.url;
      // 2.从pics数组中，找到图片对应的索引值
      const i = this.form.productImages.findIndex((x) => x.url === filePath);
      // 3.调用splice方法，移除图片信息
      this.form.productImages.splice(i, 1);
      this.bookUrllist3.splice(i, 1);
      this.fmtuploadingCount = this.form.productImages.length;
      this.initDragSort();
    },
    handleRemove5(file) {
      // 1.获取将要删除图片的临时路径
      const filePath = file.url;
      // 2.从pics数组中，找到图片对应的索引值
      const i = this.form.longpic.findIndex((x) => x.url === filePath);
      // 3.调用splice方法，移除图片信息
      this.form.longpic.splice(i, 1);
      this.bookUrllist5.splice(i, 1);
      this.fmtuploadingCountcpcct = this.form.longpic.length;

      console.log(this.bookUrllist5);
      this.initDragSort();
    },
    closeDialog() {
      this.form = {
        name: "",
        productModel: "",
        productVr: "",
        yuliuone: "",
        productMoney: "",
        seriesRootId: "",
        productSynopsis: "",
        yuliutow: "",
        productImage: "",
        productUrl2: "",
        productImages: [],
        status: 0,
        productView: "",
        productId: [],
        preview: "",
        productUrl: "",
        productPaixu: 0,
        qrImage: "",
        style: "",
        space: "",
        colour: "",
        space: "",
        series: "",
        longpic: [],
        formNum: "",
        craft: "",
        unit: "",
        isOwn: "0",
        caseJiluIdList: "",
        yuliuone: "",
      };
      this.activeName = "1";
      this.bookUrllist = [];
      this.bookUrllist1 = [];
      this.bookUrllist2 = [];
      this.bookUrllist3 = [];
      this.checkList = [[], [], [], [], [], [], [], [], [], []];
      this.tableData = [
        {
          no: "",
          attr_list: [],

          price: "",
          num: "",
          pic: "",
          seriesRootId: "",
        },
      ];
      this.tableOption = [];
      this.dbName = "sedysgd_cn";
      this.activeName = "1";
    },
    // 查询产品分类
    getproductTypeList() {
      var params = new URLSearchParams();
      params.append("dbName", this.dbName);

      getproductTypeList(params)
        .then((res) => {
          if (res.data.code >= 0) {
            this.classification = res.data.data[0].children;
            this.classification1 = res.data.data[0].children[0].children;
            res.data.data[0].children.forEach((item) => {
              if (item.name == "规格") {
                this.GGlist = item.children;
              }
              if (item.name == "系列") {
                this.XLList = item.children;
              }
            });
          } else {
            this.classification = [];
            this.classification1 = [];
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },

    // 查询产品分类
    getqueryCaseType() {
      var params = new URLSearchParams();
      params.append("dbName", this.dbName);

      params.append("curPage", this.curPage);
      params.append("yuliutwo", 2);

      if (this.input1) {
        params.append("status", this.input1);
      }

      if (this.input2) {
        params.append("size", this.input2);
      }
      if (this.input3) {
        params.append("series", this.input3);
      }
      if (this.input4) {
        params.append("keyword", this.input4);
      }
      if (this.input5) {
        params.append("yuliutwo", this.input5);
      }
      getproductList(params)
        .then((res) => {
          this.total = res.data.totalNum;
          if (res.data.code == "1") {
            this.data = res.data.list;
          } else {
            this.data = [];
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    handleCurrentChange1(val) {
      this.curPage = val;
      this.getqueryCaseType();
    },
    handleSelectionChange(val) {
      this.handleSelectionChangeList = val;
    },
    //选中规格
    selectBox(val) {
      this.specialShowgg = true;
      this.Product.caseJiluId = true;
    },

    change6(index, i) {
      const params = {
        dbName: this.dbName,
        attrGroup: this.attrGroup_ID[i], // this.attrGroupID,
        // attrGroup: this.attrGroupID, // this.attrGroupID,
        attrName: this.tableData[index].attr_list[i].attr_name,
      };
      QueryAttr(params).then((response) => {
        if (response.data.code == "1") {
          this.tableData[index].attr_list[i].attr_id = response.data.record.id;
          console.log(this.tableData);
        }
      });
    },
    change5() {
      if (this.input5) {
        this.tableOption.push({
          label: this.input5,
        });

        // this.tableData[0].attr_list = this.tableOption
        // return
        const params = {
          dbName: this.dbName,
          attrGroup: this.input5,
        };
        QueryAttrGroup(params).then((response) => {
          if (response.data.code == "1") {
            this.attrGroup_ID.push(response.data.record.id);
            for (let i in this.tableData) {
              this.tableData[i].attr_list.push({
                attr_group_id: response.data.record.id, //表头id
                attr_group_name: this.input5, //表头
                attr_id: "",
                attr_name: "",
              });
            }
          }
        });
      } else {
        this.$message({
          type: "warning",
          message: "请填写规格",
        });
      }
    },
    generateData() {
      var params = new URLSearchParams();
      // params.append('dbName', 'schende_cns');
      params.append("dbName", "schender");

      this.$store
        .dispatch("CP/getqueryAllProduct", params)
        .then((res) => {
          if (res.code == "1") {
            var a = [];

            res.record.forEach((item, index) => {
              a.push({
                label: item.name + "-" + item.productModel,
                key: item.id,
                // pinyin: res.record[index],
                productModel: item.productModel,
              });
            });
            console.log(a, "a");
            this.data1 = a;
          } else {
            this.data1 = [];
          }
        })
        .catch(() => {});
    },
    handleChange(value, direction, movedKeys) {
      this.form.caseJiluIdList = value.join();
    },
    handleSmallPicSuccessxsf(file, fileList) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const img = new Image();
        img.onload = () => {
          const canvas = document.createElement("canvas");
          canvas.width = img.width;
          canvas.height = img.height;
          const ctx = canvas.getContext("2d");

          // 绘制原始图片
          ctx.drawImage(img, 0, 0);

          // 加载水印图片
          const watermark = new Image();
          watermark.src = this.watermarkImage;
          watermark.onload = () => {
            // 设置水印之间的间隙
            const spacing = 300; // 间隙大小

            // 设置水印缩放比例
            const watermarkScale = 0.2; // 例如，缩小到原图的50%

            // 计算水印图片的宽度和高度
            const watermarkWidth = watermark.width * watermarkScale;
            const watermarkHeight = watermark.height * watermarkScale;

            // 保存当前绘图状态
            ctx.save();

            // 平移到图片中心
            ctx.translate(canvas.width / 2, canvas.height / 2);

            // 旋转绘图上下文
            ctx.rotate(150);

            // 计算水印图片的数量
            const numWatermarksX = Math.ceil(
              canvas.width / (watermarkWidth + spacing)
            );
            const numWatermarksY = Math.ceil(
              canvas.height / (watermarkHeight + spacing)
            );

            // 绘制水印图片
            for (
              let y = -((numWatermarksY * (watermarkHeight + spacing)) / 2);
              y < (numWatermarksY * (watermarkHeight + spacing)) / 2;
              y += watermarkHeight + spacing
            ) {
              for (
                let x = -((numWatermarksX * (watermarkWidth + spacing)) / 2);
                x < (numWatermarksX * (watermarkWidth + spacing)) / 2;
                x += watermarkWidth + spacing
              ) {
                ctx.drawImage(watermark, x, y, watermarkWidth, watermarkHeight);
              }
            }

            // 恢复绘图状态
            ctx.restore();

            // 将处理后的图片转换为DataURL
            const watermarkedImage = canvas.toDataURL("image/png");

            file.url = watermarkedImage;
            this.bookUrllist = fileList;

            const blob = this.dataURLtoBlob(watermarkedImage);
            const file1 = new File([blob], file.name, { type: blob.type });
            let datacc = {
              raw: file1,
            };
            this.uploadImg(datacc, 1);
          };
        };
        img.src = e.target.result;
      };
      reader.readAsDataURL(file.raw);
    },
    dataURLtoBlob(dataUrl) {
      const arr = dataUrl.split(",");
      const mime = arr[0].match(/:(.*?);/)[1];
      const bstr = atob(arr[1]);
      let n = bstr.length;
      const u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      return new Blob([u8arr], { type: mime });
    },
    initDragSort() {
      const el =
        this.$refs.ImageChang3.$el.querySelectorAll(".el-upload-list")[0];

      console.log(el);
      Sortable.create(el, {
        onEnd: ({ oldIndex, newIndex }) => {
          // 交换位置
          const arr = this.form.longpic;
          const page = arr[oldIndex];
          arr.splice(oldIndex, 1);
          arr.splice(newIndex, 0, page);
        },
      });

      const el1 =
        this.$refs.ImageChang2.$el.querySelectorAll(".el-upload-list")[0];

      Sortable.create(el1, {
        onEnd: ({ oldIndex, newIndex }) => {
          // 交换位置
          const arr = this.form.productImages;
          const page = arr[oldIndex];
          arr.splice(oldIndex, 1);
          arr.splice(newIndex, 0, page);
        },
      });
    },
    handleSmallPicSuccessxsf(file, fileList) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const img = new Image();
        img.onload = () => {
          const canvas = document.createElement("canvas");
          canvas.width = img.width;
          canvas.height = img.height;
          const ctx = canvas.getContext("2d");

          // 绘制原始图片
          ctx.drawImage(img, 0, 0);

          // 加载水印图片
          const watermark = new Image();
          watermark.src = this.watermarkImage;
          watermark.onload = () => {
            // 设置水印之间的间隙
            const spacing = 300; // 间隙大小

            // 设置水印缩放比例
            const watermarkScale = 0.2; // 例如，缩小到原图的50%

            // 计算水印图片的宽度和高度
            const watermarkWidth = watermark.width * watermarkScale;
            const watermarkHeight = watermark.height * watermarkScale;

            // 保存当前绘图状态
            ctx.save();

            // 平移到图片中心
            ctx.translate(canvas.width / 2, canvas.height / 2);

            // 旋转绘图上下文
            ctx.rotate(150);

            // 计算水印图片的数量
            const numWatermarksX = Math.ceil(
              canvas.width / (watermarkWidth + spacing)
            );
            const numWatermarksY = Math.ceil(
              canvas.height / (watermarkHeight + spacing)
            );

            // 绘制水印图片
            for (
              let y = -((numWatermarksY * (watermarkHeight + spacing)) / 2);
              y < (numWatermarksY * (watermarkHeight + spacing)) / 2;
              y += watermarkHeight + spacing
            ) {
              for (
                let x = -((numWatermarksX * (watermarkWidth + spacing)) / 2);
                x < (numWatermarksX * (watermarkWidth + spacing)) / 2;
                x += watermarkWidth + spacing
              ) {
                ctx.drawImage(watermark, x, y, watermarkWidth, watermarkHeight);
              }
            }

            // 恢复绘图状态
            ctx.restore();

            // 将处理后的图片转换为DataURL
            const watermarkedImage = canvas.toDataURL("image/png");
            // 将 DataURL 转换为 Blob 对象
            // const blob = this.dataURLtoBlob(watermarkedImage);
            // 更新文件列表

            file.url = watermarkedImage;
            this.bookUrllist = fileList;

            const blob = this.dataURLtoBlob(watermarkedImage);
            const file1 = new File([blob], file.name, { type: blob.type });
            let datacc = {
              raw: file1,
            };

            this.uploadImg(datacc, 1);
          };
        };
        img.src = e.target.result;
      };
      reader.readAsDataURL(file.raw);
    },

    handleSmallPicSuccessfmt(file, fileList) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const img = new Image();
        img.onload = () => {
          const canvas = document.createElement("canvas");
          canvas.width = img.width;
          canvas.height = img.height;
          const ctx = canvas.getContext("2d");

          // 绘制原始图片
          ctx.drawImage(img, 0, 0);

          // 加载水印图片
          const watermark = new Image();
          watermark.src = this.watermarkImage;
          watermark.onload = () => {
            // 设置水印之间的间隙
            const spacing = 300; // 间隙大小

            // 设置水印缩放比例

            const watermarkScale = 0.2; // 例如，缩小到原图的50%

            // 计算水印图片的宽度和高度
            const watermarkWidth = watermark.width * watermarkScale;
            const watermarkHeight = watermark.height * watermarkScale;

            // 保存当前绘图状态
            ctx.save();

            // 平移到图片中心
            ctx.translate(canvas.width / 2, canvas.height / 2);

            // 旋转绘图上下文
            ctx.rotate(150);

            // 计算水印图片的数量
            const numWatermarksX = Math.ceil(
              canvas.width / (watermarkWidth + spacing)
            );
            const numWatermarksY = Math.ceil(
              canvas.height / (watermarkHeight + spacing)
            );

            // 绘制水印图片
            for (
              let y = -((numWatermarksY * (watermarkHeight + spacing)) / 2);
              y < (numWatermarksY * (watermarkHeight + spacing)) / 2;
              y += watermarkHeight + spacing
            ) {
              for (
                let x = -((numWatermarksX * (watermarkWidth + spacing)) / 2);
                x < (numWatermarksX * (watermarkWidth + spacing)) / 2;
                x += watermarkWidth + spacing
              ) {
                ctx.drawImage(watermark, x, y, watermarkWidth, watermarkHeight);
              }
            }
            // 恢复绘图状态
            ctx.restore();

            // 将处理后的图片转换为DataURL
            const watermarkedImage = canvas.toDataURL("image/png");

            file.url = watermarkedImage;
            this.bookUrllist1 = fileList;

            const blob = this.dataURLtoBlob(watermarkedImage);
            const file1 = new File([blob], file.name, { type: blob.type });
            let datacc = {
              raw: file1,
            };
            this.uploadImg(datacc, 2);
          };
        };
        img.src = e.target.result;
      };
      reader.readAsDataURL(file.raw);
    },
    handleSmallPicSuccesskjxgt(file, fileList) {
      this.fmtuploadingCount++; // 增加正在上传的图片数量计数
      const reader = new FileReader();
      reader.onload = (e) => {
        const img = new Image();
        img.onload = () => {
          const canvas = document.createElement("canvas");
          canvas.width = img.width;
          canvas.height = img.height;
          const ctx = canvas.getContext("2d");

          // 绘制原始图片
          ctx.drawImage(img, 0, 0);

          // 加载水印图片
          const watermark = new Image();
          watermark.src = this.watermarkImage;
          watermark.onload = () => {
            // 设置水印之间的间隙
            const spacing = 300; // 间隙大小

            // 设置水印缩放比例

            const watermarkScale = 0.2; // 例如，缩小到原图的50%

            // 计算水印图片的宽度和高度
            const watermarkWidth = watermark.width * watermarkScale;
            const watermarkHeight = watermark.height * watermarkScale;

            // 保存当前绘图状态
            ctx.save();

            // 平移到图片中心
            ctx.translate(canvas.width / 2, canvas.height / 2);

            // 旋转绘图上下文
            ctx.rotate(150);

            // 计算水印图片的数量
            const numWatermarksX = Math.ceil(
              canvas.width / (watermarkWidth + spacing)
            );
            const numWatermarksY = Math.ceil(
              canvas.height / (watermarkHeight + spacing)
            );

            // 绘制水印图片
            for (
              let y = -((numWatermarksY * (watermarkHeight + spacing)) / 2);
              y < (numWatermarksY * (watermarkHeight + spacing)) / 2;
              y += watermarkHeight + spacing
            ) {
              for (
                let x = -((numWatermarksX * (watermarkWidth + spacing)) / 2);
                x < (numWatermarksX * (watermarkWidth + spacing)) / 2;
                x += watermarkWidth + spacing
              ) {
                ctx.drawImage(watermark, x, y, watermarkWidth, watermarkHeight);
              }
            }
            // 恢复绘图状态
            ctx.restore();

            // 将处理后的图片转换为DataURL
            const watermarkedImage = canvas.toDataURL("image/png");

            const blob = this.dataURLtoBlob(watermarkedImage);
            const file1 = new File([blob], file.name, { type: blob.type });
            let datacc = {
              raw: file1,
            };
            this.uploadImg(datacc, 4, fileList.length);
          };
        };
        img.src = e.target.result;
      };
      reader.readAsDataURL(file.raw);
    },
    handleSmallPicSuccesscpcct(file, fileList) {
      this.fmtuploadingCountcpcct++;
      const reader = new FileReader();
      reader.onload = (e) => {
        const img = new Image();
        img.onload = () => {
          const canvas = document.createElement("canvas");
          canvas.width = img.width;
          canvas.height = img.height;
          const ctx = canvas.getContext("2d");

          // 绘制原始图片
          ctx.drawImage(img, 0, 0);

          // 加载水印图片
          const watermark = new Image();
          watermark.src = this.watermarkImage;
          watermark.onload = () => {
            // 设置水印之间的间隙
            const spacing = 300; // 间隙大小

            // 设置水印缩放比例

            const watermarkScale = 0.2; // 例如，缩小到原图的50%

            // 计算水印图片的宽度和高度
            const watermarkWidth = watermark.width * watermarkScale;
            const watermarkHeight = watermark.height * watermarkScale;

            // 保存当前绘图状态
            ctx.save();

            // 平移到图片中心
            ctx.translate(canvas.width / 2, canvas.height / 2);

            // 旋转绘图上下文
            ctx.rotate(150);

            // 计算水印图片的数量
            const numWatermarksX = Math.ceil(
              canvas.width / (watermarkWidth + spacing)
            );
            const numWatermarksY = Math.ceil(
              canvas.height / (watermarkHeight + spacing)
            );

            // 绘制水印图片
            for (
              let y = -((numWatermarksY * (watermarkHeight + spacing)) / 2);
              y < (numWatermarksY * (watermarkHeight + spacing)) / 2;
              y += watermarkHeight + spacing
            ) {
              for (
                let x = -((numWatermarksX * (watermarkWidth + spacing)) / 2);
                x < (numWatermarksX * (watermarkWidth + spacing)) / 2;
                x += watermarkWidth + spacing
              ) {
                ctx.drawImage(watermark, x, y, watermarkWidth, watermarkHeight);
              }
            }

            // 恢复绘图状态
            ctx.restore();

            // 将处理后的图片转换为DataURL
            const watermarkedImage = canvas.toDataURL("image/png");

            const blob = this.dataURLtoBlob(watermarkedImage);
            const file1 = new File([blob], file.name, { type: blob.type });
            let datacc = {
              raw: file1,
            };
            this.uploadImg(datacc, 5, fileList.length);
          };
        };
        img.src = e.target.result;
      };
      reader.readAsDataURL(file.raw);
    },
  },
  created() {
    const SEDJJXCX_LHFW_UserInformation_HT = JSON.parse(
      decodeURIComponent(
        window.localStorage.getItem("SEDJJXCX_LHFW_UserInformation_HT")
      )
    );
    this.SEDJJXCX_LHFW_UserInformation_HT = SEDJJXCX_LHFW_UserInformation_HT;

    this.getqueryCaseType();
    this.getproductTypeList();
    this.generateData();
    console.log(this.$dbList);
  },
  mounted() {},
};
</script>

<style lang="scss">
.el-table thead {
  color: #000000 !important;
}

.el-tree-node__label {
  font-weight: 900 !important;
}

.el-tree-node__content {
  font-weight: 900 !important;
}

.dashboard {
  &-container {
    margin: 30px;
  }

  &-text {
    font-size: 30px;
    line-height: 46px;
  }
}

.item {
  margin-bottom: 18px;
}

.el-input_ss1 {
  width: 200px;
  margin-top: 4px;
}

.pagdw {
  float: right;
  margin-top: 46px;
}

.el-input_ss {
  width: 200px;
}

.el-input_ss1 {
  width: 200px;
  margin-top: 3px;
}

.float_rigth {
  float: right;
}

.el-transfer-panel {
  width: 550px;
}

.tsy {
  color: #3d3d3d;
  font-family: "PingFang SC";
  font-size: 10px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px;
  /* 160% */
}
</style>
