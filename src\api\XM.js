import request from '@/utils/request'

export function projectList(params) {//项目列表
  return request({
    url: 'oe_projectListPc_.csp',
    method: 'get',
    params
  })
}

export function useProduct(params) {//产品列表
  return request({
    url: 'oe_useProduct_.csp',
    method: 'get',
    params
  })
}

export function createStandard(params) {//添加标准
  return request({
    url: 'oe_createStandard_.csp',
    method: 'get',
    params
  })
}

export function workStandards(params) {//获取标准
  return request({
    url: 'oe_workStandards_.csp',
    method: 'get',
    params
  })
}

export function delStandard(params) {//删除标准
  return request({
    url: 'oe_delStandard_.csp',
    method: 'get',
    params
  })
}

export function hqComfirm(params) {//审核
  return request({
    url: 'oe_hqComfirm_.csp',
    method: 'get',
    params
  })
}
export function updateProjectUser(params) {//审核
  return request({
    url: 'oe_updateProjectUser_.csp',
    method: 'get',
    params
  })
}
export function projectTeams(params) {//团队
  return request({
    url: 'oe_projectTeams_.csp',
    method: 'get',
    params
  })
}
export function projectSchedule(params) {//打卡记录
  return request({
    url: 'oe_projectSchedule_.csp',
    method: 'get',
    params
  })
}

export function getWorkClient(params) {//订单客户
  return request({
    url: 'oe_getWorkClient_.csp',
    method: 'get',
    params
  })
}
export function updateOwner(params) {//修改订单客户
  return request({
    url: 'oe_updateOwner_.csp',
    method: 'get',
    params
  })
}

export function getWorkMainTain(params) {//查看维保记录
  return request({
    url: 'oe_getWorkMainTain_.csp',
    method: 'get',
    params
  })
}
export function addAwards(params) {//添加奖项列表
  return request({
    url: 'oe_addAwards_.csp',
    method: 'get',
    params
  })
}
export function getAwards(params) {//查询奖项列表
  return request({
    url: 'oe_getAwards_.csp',
    method: 'get',
    params
  })
}
export function delAward(params) {//删除奖项列表
  return request({
    url: 'oe_delAward_.csp',
    method: 'get',
    params
  })
}

export function getAwardApply(params) {//获取奖项申请记录
  return request({
    url: 'oe_getAwardApply_.csp',
    method: 'get',
    params
  })
}
export function comfirmApply(params) {//确定奖项申请
  return request({
    url: 'oe_comfirmApply_.csp',
    method: 'get',
    params
  })
}

export function getInsurances(params) {//工程师保单记录
  return request({
    url: 'oe_getInsurances_.csp',
    method: 'get',
    params
  })
}

export function updateInsurance(params) {//修改
  return request({
    url: 'oe_updateInsurance_.csp',
    method: 'get',
    params
  })
}
export function soldOutProject(params) {//上下架
  return request({
    url: 'oe_soldOutProject_.csp',
    method: 'get',
    params
  })
}
export function getEvaluates(params) {//评价
  return request({
    url: 'oe_getEvaluates_.csp',
    method: 'get',
    params
  })
}
export function checkAccept(params) {//评价
  return request({
    url: 'oe_checkAccept_.csp',
    method: 'get',
    params
  })
}

export function getMaterialsStandards(params) {//评价
  return request({
    url: 'oe_getMaterialsStandards_.csp',
    method: 'get',
    params
  })
}

export function addStandard(params) {//评价
  return request({
    url: 'oe_addStandard_.csp',
    method: 'get',
    params
  })
}

export function delStandard2(params) {//评价
  return request({
    url: 'oe_delStandard2_.csp',
    method: 'get',
    params
  })
}
export function delSchedule(params) {//删除打开记录
  return request({
    url: 'oe_delSchedule_.csp',
    method: 'get',
    params
  })
}
export function exportAwardsList(params) {//导出
  return request({
    url: 'oe_exportAwardsList_.csp',
    method: 'get',
    params
  })
}


export function submitMainTain(params) {//导出
  return request({
    url: 'oe_submitMainTain_.csp',
    method: 'get',
    params
  })
}
export function toReject(params) {//驳回
  return request({
    url: 'oe_toReject_.csp',
    method: 'get',
    params
  })
}

export function processReportList(params) {//加工报备
  return request({
    url: 'oe_processReportList_.csp',
    method: 'get',
    params
  })
}
export function checkProcessReport(params) {//加工报备
  return request({
    url: 'oe_checkProcessReport_.csp',
    method: 'get',
    params
  })
}

export function postSaleList(params) {//加工报备
  return request({
    url: 'oe_postSaleList_.csp',
    method: 'get',
    params
  })
}
export function checkPostSale(params) {//加工报备
  return request({
    url: 'oe_checkPostSale_.csp',
    method: 'get',
    params
  })
}

export function updateAllStore(params) {//编辑门店信息
  return request({
    url: 'oe_updateAllStore_.csp',
    method: 'get',
    params
  })
}


// 通过
export function checkStoreApply(params) {//编辑门店信息
  return request({
    url: 'oe_checkStoreApply_.csp',
    method: 'get',
    params
  })
}

// 删除
export function delAllStore(params) {//编辑门店信息
  return request({
    url: 'oe_delAllStore_.csp',
    method: 'get',
    params
  })
}