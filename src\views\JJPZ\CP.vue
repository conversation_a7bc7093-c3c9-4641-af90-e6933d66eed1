<template>
  <div class="dashboard-container">
    <el-form :inline="true" class="user-search">
      <el-form-item label="上线状态:">
        <el-select
          size="small"
          placeholder="上线状态"
          @change="change1"
          v-model="input1"
        >
          <el-option label="上线" value="1"></el-option>
          <el-option label="下线" value="0"></el-option>
        </el-select>
      </el-form-item>

      <!-- <el-form-item label="产品分类:">
        <el-select size="small" placeholder="产品分类" @change="change1" v-model="input5">
          <el-option label="定制" value="1"></el-option>
          <el-option label="成品" value="2"></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-input
          size="small"
          placeholder="请输入内容"
          v-model="input4"
          class="el-input_ss1"
        >
          <el-button
            size="small"
            slot="append"
            icon="el-icon-search"
            @click="change1"
          ></el-button>
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-button size="mini" type="primary" @click="addincrease('ruleForm')"
          >添加</el-button
        >

        <el-button size="mini" type="info" @click="getgetselectCase1('1')"
          >重新加载</el-button
        >

        <!-- 搜索筛选 -->
      </el-form-item>
    </el-form>

    <el-table
      border
      :data="data"
      fit
      highlight-current-row
      @selection-change="handleSelectionChange"
    >
      <el-table-column align="center" type="selection" width="60">
      </el-table-column>
      <el-table-column align="center" type="index" label="序号" width="100" />

      <el-table-column label="封面图" align="center" width="100">
        <template slot-scope="scope">
          <!-- <el-popover placement="bottom" trigger="hover" width="300"> -->
          <!-- <img :src="scope.row.coverImage" width="100%" /> -->
          <img
            slot="reference"
            :src="scope.row.coverImage"
            :alt="scope.row.coverImage"
            style="max-height: 30px; max-width: 30px"
          />
          <!-- </el-popover> -->
        </template>
      </el-table-column>

      <el-table-column align="center" label="名称">
        <template slot-scope="scope">{{ scope.row.title }}</template>
      </el-table-column>

      <el-table-column align="center" label="上线状态" width="100">
        <template slot-scope="scope">
          <el-tag size="medium" effect="success" v-if="scope.row.status == '1'">
            上线
          </el-tag>

          <el-tag size="medium" v-if="scope.row.status == '0'" effect="danger">
            未上线
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column align="center" label="创建时间">
        <template slot-scope="scope">{{ scope.row.createTime }}</template>
      </el-table-column>

      <el-table-column fixed="right" label="操作" width="250" align="center">
        <template slot-scope="scope">
          <el-button
            size="mini"
            icon="el-icon-edit"
            type="primary"
            @click="handleEditzt(scope.row)"
            >编辑</el-button
          >
          <el-button
            size="mini"
            icon="el-icon-s-grid"
            type="warning"
            @click="selectBox(scope.row)"
            >场景配置</el-button
          >
          <!-- <el-button type="warning" icon="el-icon-delete" size="mini"
            @click="deleteclassification(scope.row)">删除</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <el-row>
      <el-pagination
        class="pagdw"
        :current-page.sync="currentPage"
        background
        :page-size="pageSize"
        layout="total,prev,pager,next,jumper"
        :total="total"
        @current-change="handleCurrentChange1"
      />
    </el-row>

    <!-- 编辑界面 -->
    <el-dialog
      @close="closeDialog"
      :title="title"
      :visible.sync="editFormVisible"
      width="80%"
      top="6vh"
    >
      <div class="import-dialog">
        <el-row>
          <el-col :span="24">
            <el-form label-width="120px">
              <el-form-item label="标题:" prop="form.title">
                <el-input
                  size="small"
                  v-model="form.title"
                  auto-complete="off"
                  placeholder="请输入标题"
                ></el-input>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form label-width="120px">
              <el-form-item label="场景宽:" prop="form.sceneWidth">
                <el-input
                  size="small"
                  v-model="form.sceneWidth"
                  auto-complete="off"
                  placeholder="请输入场景宽"
                ></el-input>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form label-width="120px">
              <el-form-item label="场景高:">
                <el-input
                  size="small"
                  v-model="form.sceneHeight"
                  auto-complete="off"
                  placeholder="请输入场景高"
                ></el-input>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form label-width="120px">
              <el-form-item label="倍数:" prop="form.productMoney">
                <el-input
                  size="small"
                  v-model="form.minification"
                  auto-complete="off"
                  placeholder="请输入倍数"
                ></el-input>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>

        <!-- <el-row>
              <el-col :span="24">
                <el-form label-width="120px" >
                  <el-form-item label="产品视频:" prop="form.productView">
                    <el-input size="small" v-model="form.productView" auto-complete="off"
                      placeholder="请输入产品名称"></el-input>
                  </el-form-item>

                </el-form>
              </el-col>

            </el-row> -->
        <el-row>
          <el-col :span="24"
            ><el-form label-width="120px">
              <el-form-item label="描述:" prop="form.productSynopsis">
                <el-input
                  type="textarea"
                  size="small"
                  v-model="form.context"
                  auto-complete="off"
                  placeholder="请输入描述"
                ></el-input>
              </el-form-item> </el-form
          ></el-col>
        </el-row>
        <el-row>
          <!-- <el-col :span="24"><el-form label-width="120px">
              <el-form-item label="产品分类:" prop="form.yuliutow">
                <el-radio-group v-model="form.yuliutwo">
                  <el-radio :label="'1'">定制</el-radio>
                  <el-radio :label="'2'">成品</el-radio>
                </el-radio-group>
              </el-form-item>

            </el-form>
          </el-col> -->
          <el-col :span="24"
            ><el-form label-width="120px">
              <el-form-item label="是否上线:" prop="form.status">
                <el-radio-group v-model="form.status">
                  <el-radio :label="'1'">是</el-radio>
                  <el-radio :label="'0'">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24"
            ><el-form label-width="120px">
              <el-form-item label="排序:" prop="form.sort">
                <el-input
                  size="small"
                  v-model="form.sort"
                  auto-complete="off"
                  placeholder="请输入排序"
                ></el-input>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>

        <el-row>
          <el-col
            :span="24"
            v-for="(item, index) in classification"
            :key="item.id"
          >
            <el-form label-width="120px">
              <el-form-item :label="item.name">
                <el-checkbox-group
                  v-model="checkList[index]"
                  @change="getshow(index)"
                >
                  <!-- <el-checkbox :value=items.id :label=items.name v-for="(items,indexs) in item.children" :key="items.id"></el-checkbox> -->
                  <el-checkbox
                    :label="items.id"
                    v-for="(items, indexs) in item.children"
                    :key="items.id"
                    >{{ items.name }}</el-checkbox
                  >
                </el-checkbox-group>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form label-width="120px">
              <el-form-item label="封面图:" prop="form.coverImage">
                <el-upload
                  :on-preview="handlePreviewcp"
                  class="el_upload_above"
                  multiple
                  :limit="limitnum"
                  :action="action"
                  ref="upload"
                  list-type="picture-card"
                  :http-request="uploadSectionFile"
                  :auto-upload="true"
                  :file-list="bookUrllist1"
                  :on-error="uploadFileError"
                  :on-success="uploadFileSuccess"
                  :on-exceed="exceedFile"
                  :before-upload="handleBeforeUpload"
                  :on-remove="handleRemove2"
                >
                  <i class="el-icon-plus" />
                </el-upload>
                <el-dialog
                  :visible.sync="dialogVisibleImg1"
                  top="0"
                  center
                  :modal="false"
                >
                  <img width="100%" :src="form.coverImage" alt />
                </el-dialog>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="RechargeRole('form')"
          >确 定</el-button
        >
      </div>
    </el-dialog>
    <!-- 二维码 -->
    <el-dialog
      width="20%"
      title="产品二维码"
      :visible.sync="ewmShowgg"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <img
        style="margin-left: 20px"
        class="img"
        width="300px"
        height="300px"
        :src="qrImage"
      />

      <div slot="footer" class="dialog-footer">
        <el-button @click="download" icon="el-icon-download"></el-button>
      </div>
    </el-dialog>
    <!-- 选择规格 -->

    <el-dialog
      top="2vh"
      width="90%"
      title="场景配置"
      :visible.sync="specialShowgg"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <template>
        <el-form label-position="top">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="产品选择">
                <el-select
                  v-model="tableData[0].productId"
                  filterable
                  style="width: 400px"
                  placeholder="请选择产品"
                >
                  <el-option
                    v-for="(item, index) in data1"
                    :key="item.key"
                    style="width: 400px"
                    :label="item.label"
                    :value="item.key"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="倾斜角度">
                <el-input
                  @input="handleBlur1"
                  clearable
                  v-model="tableData[0].angle"
                  placeholder="请输入"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="家具图">
                <el-upload
                  action="#"
                  list-type="picture-card"
                  :on-preview="handlePreview1"
                  :on-remove="handleRemove1"
                  :auto-upload="false"
                  :file-list="bookUrllist"
                  multiple
                  :on-change="handleSmallPicSuccess1"
                >
                  <i class="el-icon-plus" />
                </el-upload>
                <el-dialog
                  :visible.sync="dialogVisibleImg"
                  top="0"
                  center
                  :modal="false"
                >
                  <img width="100%" :src="tableData[0].furnitureImage" alt />
                </el-dialog>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="表面空图">
                <el-upload
                  action="#"
                  list-type="picture-card"
                  :on-remove="handleRemove3"
                  :auto-upload="false"
                  :file-list="bookUrllist1"
                  multiple
                  :on-change="handleSuccess11"
                >
                  <i class="el-icon-plus" />
                </el-upload>
              </el-form-item>
            </el-col>
          </el-row>

          <div class="bm">表面1</div>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="x轴">
                <el-input
                  @input="handleBlur1"
                  clearable
                  v-model="tableData[0].xAxis"
                  placeholder="请输入"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="y轴">
                <el-input
                  @input="handleBlur1"
                  clearable
                  v-model="tableData[0].yAxis"
                  placeholder="请输入"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="家具宽">
                <el-input
                  @input="handleBlur1"
                  clearable
                  v-model="tableData[0].furnitureWidth"
                  placeholder="请输入"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="家具高">
                <el-input
                  @input="handleBlur1"
                  clearable
                  v-model="tableData[0].furnitureHeight"
                  placeholder="请输入"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="默认表面砖">
                <el-select
                  @change="$forceUpdate()"
                  v-model="tableData[0].acquiesceBrick"
                  filterable
                  style="width: 400px"
                  placeholder="请选择产品"
                >
                  <el-option
                    v-for="(item, index) in data12"
                    :key="item.key"
                    style="width: 400px"
                    :label="item.label"
                    :value="item.key"
                  />
                </el-select>
                <!-- <el-upload action="#" list-type="picture-card" :on-preview="handlePreview2313111" :on-remove="handleRemove111"
                  :auto-upload="false" :file-list="bookUrllist3" multiple :on-change="handleSmallPicSuccess1122">
                  <i class="el-icon-plus" />
                </el-upload>
                <el-dialog :visible.sync="dialogVisibleImg2" top="0" center :modal="false">
                  <img width="100%" :src="tableData[0].acquiesceBrick" alt>
                </el-dialog> -->
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <div class="bm">表面2</div>
        <el-form label-position="top">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="x轴">
                <el-input
                  @input="handleBlur1"
                  clearable
                  v-model="tableData1[0].xAxis2"
                  placeholder="请输入"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="y轴">
                <el-input
                  @input="handleBlur1"
                  clearable
                  v-model="tableData1[0].yAxis2"
                  placeholder="请输入"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="家具宽">
                <el-input
                  @input="handleBlur1"
                  clearable
                  v-model="tableData1[0].furnitureWidth2"
                  placeholder="请输入"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="家具高">
                <el-input
                  @input="handleBlur1"
                  clearable
                  v-model="tableData1[0].furnitureHeight2"
                  placeholder="请输入"
                ></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item label="默认表面砖2">
                <!-- <el-upload action="#" list-type="picture-card" :on-preview="handlePreview2313111" :on-remove="handleRemove111"
                  :auto-upload="false" :file-list="bookUrllist5" multiple :on-change="handleSmallPicSuccess1122">
                  <i class="el-icon-plus" />
                </el-upload>
                <el-dialog :visible.sync="dialogVisibleImg6" top="0" center :modal="false">
                  <img width="100%" :src="tableData1[0].acquiesceBrick" alt>
                </el-dialog> -->
                <el-select
                  @change="$forceUpdate()"
                  v-model="tableData[0].acquiesceBrick2"
                  filterable
                  style="width: 400px"
                  placeholder="请选择产品"
                >
                  <el-option
                    v-for="(item, index) in data12"
                    :key="item.key"
                    style="width: 400px"
                    :label="item.label"
                    :value="item.key"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <div class="bm">多面配置</div>

            <el-col :span="12">
              <el-form-item label="图片">
                <el-upload
                  action="#"
                  list-type="picture-card"
                  :on-preview="handlePreview23131"
                  :on-remove="handleRemove111"
                  :auto-upload="false"
                  :file-list="bookUrllist2"
                  multiple
                  :on-change="handleSmallPicSuccess11"
                >
                  <i class="el-icon-plus" />
                </el-upload>
                <el-dialog
                  :visible.sync="dialogVisibleImg1"
                  top="0"
                  center
                  :modal="false"
                >
                  <img width="100%" :src="tableData[0].yuliuone" alt />
                </el-dialog>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="置顶图">
                <el-upload
                  action="#"
                  list-type="picture-card"
                  :on-preview="handlePreview231311112313"
                  :on-remove="handleRemove1113232"
                  :auto-upload="false"
                  :file-list="bookUrllist4"
                  multiple
                  :on-change="handleSmallPicSuccess11222223"
                >
                  <i class="el-icon-plus" />
                </el-upload>
                <el-dialog
                  :visible.sync="dialogVisibleImg6"
                  top="0"
                  center
                  :modal="false"
                >
                  <img width="100%" :src="tableData[0].blankImage2" alt />
                </el-dialog>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <div slot="footer" class="dialog-footer">
          <el-button @click="formCancal">取 消</el-button>
          <el-button type="primary" @click="handleClic1k('form')"
            >确 定</el-button
          >
        </div>
      </template>
    </el-dialog>
    <el-dialog
      @close="closeDialog222"
      title="场景列表"
      :visible.sync="editFormVisible11"
      width="80%"
      top="6vh"
    >
      <el-form>
        <el-form-item label="">
          <template slot-scope="scope">
            <el-button size="mini" type="primary" @click="add('ruleForm')"
              >添加</el-button
            >
          </template>
        </el-form-item>
      </el-form>
      <el-table border :data="cplistxs" fit highlight-current-row>
        <el-table-column align="center" type="index" label="序号" width="100" />

        <el-table-column label="封面图" align="center" width="100">
          <template slot-scope="scope">
            <!-- <el-popover placement="bottom" trigger="hover" width="300"> -->
            <img :src="scope.row.furnitureImage" width="100%" />
            <!-- <img slot="reference" :src="scope.row.furnitureImage" :alt="scope.row.furnitureImage" -->
            <!-- style="max-height: 30px;max-width: 30px; " /> -->
            <!-- </el-popover> -->
          </template>
        </el-table-column>

        <el-table-column align="center" label="名称">
          <template slot-scope="scope">{{ scope.row.name }}</template>
        </el-table-column>

        <el-table-column align="center" label="型号">
          <template slot-scope="scope">{{ scope.row.productModel }}</template>
        </el-table-column>

        <el-table-column align="center" label="x轴">
          <template slot-scope="scope">{{ scope.row.xAxis }}</template>
        </el-table-column>
        <el-table-column align="center" label="y轴">
          <template slot-scope="scope">{{ scope.row.yAxis }}</template>
        </el-table-column>

        <el-table-column align="center" label="家具宽">
          <template slot-scope="scope">{{ scope.row.furnitureWidth }}</template>
        </el-table-column>
        <el-table-column align="center" label="家具高">
          <template slot-scope="scope">{{
            scope.row.furnitureHeight
          }}</template>
        </el-table-column>

        <el-table-column align="center" label="创建时间">
          <template slot-scope="scope">{{ scope.row.createTime }}</template>
        </el-table-column>

        <el-table-column fixed="right" label="操作" width="250" align="center">
          <template slot-scope="scope">
            <el-button
              size="mini"
              icon="el-icon-edit"
              type="primary"
              @click="handleEditzt11(scope.row)"
              >编辑</el-button
            >
            <el-button
              type="danger"
              icon="el-icon-delete"
              size="mini"
              @click="handleClick(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <el-row>
        <el-pagination
          class="pagdw"
          :current-page.sync="currentPage1"
          background
          :page-size="pageSize"
          layout="total,prev,pager,next,jumper"
          :total="total1"
          @current-change="handleCurrentChange122"
        />
      </el-row>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { Loading } from "element-ui";
import { upload } from "@/api/upload";
import {
  delProduct,
  changeScene,
  createProduct,
  getproductTypeList,
  QueryAttrGroup,
  QueryAttr,
  changeType,
  changeConfig,
  queryAllSndysgd,
} from "@/api/CP";

export default {
  data() {
    return {
      data12: [],
      currentPage1: 1,
      total1: 1,
      fwxllst: [],
      xstpss: "",
      data1: [],
      value: [],
      value4: [],
      tableOption: [],
      input5: "",
      specialShowgg: false,
      isClear: false, //设置为true的时候，这个可以用this.wangEditorDetail=''来替代
      wangEditorDetail: "",
      pickerOptions: {
        shortcuts: [
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      dialogTitle: "新增商品",
      ewmShowgg: false,
      specialShow: false,
      formList: {},
      data: [],
      pageSize: 10,
      value1: "",
      value2: "",
      input2: "",
      input3: "",
      input4: "",
      input5: "",
      currentPage: 1,
      handleSelectionChangeList: [],
      bookUrllist: [],
      dialogVisibleImg: false,
      formData: {
        startTime: "",
        endTime: "",
      },
      Product: {
        catId: "",
        name: "",
        subTitle: "",
        unit: "",
        sort: "",
        virtualSales: "",
        confineCount: 1,
        weight: "",
        coverPic: "",
        price: "",
        costPrice: "",
        originalPrice: "",
        service: "",
        isNegotiable: "",
        goodsNum: "",
        caseJiluId: "",
        attr: "",
        integral: "",
        detail: "",
        hotCakes: 0,
      },
      specifications: {
        price: "",
        assort: "",
        stock: "",
        price: "",
        Article: "",
        url: "",
      },
      total: 1,
      // 富文本器的配置
      config: {
        // 上传图片的配置
        uploadImage: {
          // url: "/api/upload/fileUpload", //服务器地址
          url: `${process.env.VUE_APP_BASE_API}/uploadCloud?filename=11111`,
          name: "file", // 参考接口文档的文件上传的参数
          // headers: { Authorization: localStorage.getItem("mytoken") }, //配置token
          // res是结果，insert方法会把内容注入到编辑器中，res.data.url是资源地址
          uploadSuccess(res, insert) {
            console.log(res); // 是否上传成功的响应结果和url地址
            insert(res.data.yunUrl);
          },
        },
        // 上传视频的配置
        // uploadVideo: {
        //     url: `${process.env.VUE_APP_BASE_API}upload/fileUpload`,
        //     name: 'file',
        //     uploadSuccess(res, insert) {
        //         insert(res.data.data)
        //     }
        // }
      },
      classify: [],
      obj: {
        num: "",
        price: "",
        no: "",
        pic: "",
      },

      attrGroup_ID: [],
      tableData: [
        {
          sceneId: "",
          productId: "",
          angle: "",
          furnitureImage: "",
          blankImage: [],
          xAxis: "",
          yAxis: "",
          furnitureWidth: "",
          furnitureHeight: "",
          yuliuone: "",
          acquiesceBrick: "",
          blankImage2: "",
        },
      ],
      tableData1: [
        {
          sceneId: "",
          productId: "",
          angle: "",
          furnitureImage: "",
          blankImage: [],
          xAxis: "",
          yAxis: "",
          furnitureWidth: "",
          furnitureHeight: "",
          yuliuone: "",
          acquiesceBrick: "",
        },
      ],
      SEDJJXCX_LHFW_UserInformation_HT: {},
      title: "",
      editFormVisible: false,
      editFormVisible11: false,
      classification: [],
      cplistxs: [],

      data: [],
      defaultProps: {
        children: "children",
        label: "name",
      },
      MT_LHFW_UserInformation_HT: {},
      gg: {
        name: "",
        displayOrder: "",
        status: 1,
      },
      AL: {
        name: "",
        displayOrder: "",
        level: "",
        pId: "",
        status: 1,
      },
      form: {
        title: "",
        coverImage: "",
        sceneWidth: "",
        sceneHeight: "",
        minification: "",
        context: "",
        status: "0",
        sort: "",
      },
      bookUrllist2: [],
      bookUrllist1: [],
      Subitemshow: false,
      //上传后的视频列表
      fileList: [],
      // 允许的视频类型
      fileType: [
        "pdf",
        "doc",
        "docx",
        "xls",
        "xlsx",
        "txt",
        "png",
        "jpg",
        "bmp",
        "jpeg",
        "mp4",
        "ogg",
        "flv",
        "avi",
        "wmv",
        "rmvb",
        "mov",
      ],
      // 运行上传视频大小，单位 M
      fileSize: 50,
      // 附件数量限制
      fileLimit: 1,
      //请求头
      headers: { "Content-Type": "multipart/form-data" },
      dialogVisibleImg: false,
      dialogVisibleImg211: false,
      curPage: 1,
      currentPage: 1,
      total: 1,
      pageSize: 10,
      bookUrllist: [],
      bookUrllist1: [],
      bookUrllist2: [],
      bookUrllist3: [],
      bookUrllist4: [],
      bookUrllist5: [],
      bookUrllist6: [],
      form: {
        name: "",
        productGuige: [],
        productModel: "",
        productVr: "",
        yuliuone: "",
        productMoney: "",
        seriesRootId: "",
        productSynopsis: "",
        yuliutow: "",
        productImage: "",
        productUrl2: "",
        productImages: [],
        status: 0,
        yuliutwo: 2,
        productView: "",
        productId: [],
        preview: "",
        coverImage: "",
        productPaixu: "",
        qrImage: "",
        style: "",
        space: "",
        colour: "",
        series: "",
        longpic: [],
        formNum: "",
      },
      productUrlindex: 1,
      productImageslindex: 1,
      curPage: 1,
      limitnum: 10,
      checkList: [[], [], [], [], [], [], [], [], [], []],
      action: process.env.VUE_APP_BASE_API + "/uploadCloud?filename=11111",
      tableData: [
        {
          sceneId: "",
          productId: "",
          angle: "",
          furnitureImage: "",
          blankImage: [],
          xAxis: "",
          yAxis: "",
          furnitureWidth: "",
          furnitureHeight: "",
          yuliuone: "",
          acquiesceBrick: "",
          blankImage2: "",
        },
      ],
      tableData1: [
        {
          sceneId: "",
          productId: "",
          angle: "",
          furnitureImage: "",
          blankImage: [],
          xAxis: "",
          yAxis: "",
          furnitureWidth: "",
          furnitureHeight: "",
          yuliuone: "",
          acquiesceBrick: "",
        },
      ],
      IMGindex: 0,
      GJ_img: "",
      gg_img: "",
      xqt_img: "",
      input1: "",
      input2: "",
      input3: "",
      input4: "",
      GGlist: [],
      XLList: [],
      dialogVisibleImg1: false,
      dialogVisibleImg2: false,
      dialogVisibleImg3: false,
      dialogVisibleImg4: false,
      dialogVisibleImg5: false,
      dialogVisibleImg6: false,
      isHandlingChange: false, // 添加一个标识来判断是否正在处理change事件
      qrImage: "",
      ewmShowgg: false,
      projectName: "",
      handleSelectionChangeList: [],
      ggxsid: "",
    };
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
  },
  methods: {
    handleBlur1() {
      this.$forceUpdate();
    },
    // 添加规格确定
    specificationsff() {
      if (this.tableData && this.tableData[0] && this.tableData[0].num == "") {
        this.$message({
          type: "warning",
          message: "请输入规格组和规格值",
        });
        return;
      }
      for (let a in this.tableData) {
        if (
          this.tableData[a].attr_list &&
          this.tableData[a].attr_list.length == 0
        ) {
          let obj = {
            attr_group_id: 1, //表头id
            attr_group_name: "规格", //表头
            attr_id: 1,
            attr_name: "默认",
          };
          this.tableData[a].attr_list.push(obj);
        }
      }
      this.form.productGuige = JSON.stringify(this.tableData);
      this.specialShowgg = false;
      this.input5 = "";
    },
    // 商品二维码
    QRcode(row) {
      this.qrImage = row.qrImage;
      this.ewmShowgg = true;
      this.projectName = row.name + "-" + row.productModel;
    },
    // 下载二维码
    download(name, url) {
      var image = new Image();
      image.setAttribute("crossOrigin", "anonymous");
      var _this = this;
      image.onload = function () {
        var canvas = document.createElement("canvas");
        canvas.width = image.width;
        canvas.height = image.height;
        var context = canvas.getContext("2d");
        context.drawImage(image, 0, 0, image.width, image.height);
        var url = canvas.toDataURL("image/png"); //得到图片的base64编码数据
        var a = document.createElement("a"); // 生成一个a元素
        var event = new MouseEvent("click"); // 创建一个单击事件
        a.download = _this.projectName || name; // 设置图片名称
        a.href = url; // 将生成的URL设置为a.href属性
        a.dispatchEvent(event); // 触发a的单击事件
      };
      image.src = this.qrImage ? this.qrImage : url;
    },
    leading() {
      if (
        this.handleSelectionChangeList &&
        this.handleSelectionChangeList.length
      ) {
        this.handleSelectionChangeList.forEach((item) => {
          let bb = item.name + "-" + item.productModel;
          this.download(bb, item.qrImage);
        });
      } else {
        this.$message({
          type: "warning",
          message: "请选择你下载的产品",
        });
      }
    },
    // 删除产品
    deleteclassification(row) {
      this.$confirm("此操作将永久删除场景, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          var params = new URLSearchParams();
          params.append("id", row.id);
          params.append("dbName", window.localStorage.getItem("JJHTDBnmame"));
          params.append("flag", "save");

          delProduct(params)
            .then((res) => {
              if (res.data.code == "1") {
                this.$message({
                  type: "success",
                  message: "删除成功！",
                });
                this.getqueryCaseType();
              } else {
              }
            })
            .catch(() => {});
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    getgetselectCase1() {
      this.input1 = "";
      this.input2 = "";
      this.input3 = "";
      this.input5 = "";
      this.input4 = "";
      this.curPage = 1;
      this.getqueryCaseType();
    },
    // 上下架筛选
    change1(row) {
      this.getqueryCaseType();
      return;
      var params = new URLSearchParams();
      params.append("dbName", window.localStorage.getItem("JJHTDBnmame"));

      params.append("curPage", this.curPage);
      if (this.input1) {
        params.append("status", this.input1);
      }

      if (this.input2) {
        params.append("size", this.input2);
      }
      if (this.input3) {
        params.append("series", this.input3);
      }
      if (this.input4) {
        params.append("keyword", this.input4);
      }
      if (this.input5) {
        params.append("yuliutwo", this.input5);
      }

      changeScene(params)
        .then((res) => {
          this.total = res.data.sumcount;
          this.currentPage = 1;

          if (res.data.code == "1") {
            this.data = res.data.list;
          } else {
            this.data = [];
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },

    onPauser() {
      this.playing = false;
    },
    onPaly() {
      this.playing = true;
    },
    getshow(e) {
      console.log(this.checkList);
      // if (this.checkList[e].length > 1) {
      //   this.checkList[e].splice(0, 1)
      // }
    },
    addincrease() {
      this.editFormVisible = true;
      this.title = "添加";
      this.form = {
        title: "",
        coverImage: "",
        sceneWidth: "",
        sceneHeight: "",
        minification: "",
        context: "",
        status: "0",
        sort: "",
      };
      this.activeName = "1";
      this.bookUrllist = [];
      this.bookUrllist1 = [];
      this.bookUrllist2 = [];
      this.bookUrllist3 = [];

      this.checkList = [[], [], [], [], [], [], [], [], [], []];
      this.tableData = [
        {
          sceneId: "",
          productId: "",
          angle: "",
          furnitureImage: "",
          blankImage: [],
          xAxis: "",
          yAxis: "",
          furnitureWidth: "",
          furnitureHeight: "",
          yuliuone: "",
        },
      ];
    },
    // 编辑
    handleEditzt(e) {
      var params = new URLSearchParams();
      params.append("id", e.id);
      params.append("flag", "queryById");
      params.append("dbName", window.localStorage.getItem("JJHTDBnmame"));
      changeScene(params)
        .then((res) => {
          if (res.data.code == "1") {
            this.editFormVisible = true;

            let row = res.data.record;

            this.form = Object.assign({}, row); // copy obj

            if (row.space || row.style) {
              row.space = row.space.split(",").map(Number);
              row.style = row.style.split(",").map(Number);
            }

            if (row.space && row.space.length > 0) {
              this.checkList[0] = row.space;
            }
            if (row.style && row.style.length > 0) {
              this.checkList[1] = row.style;
            }

            this.title = "编辑";

            if (row.coverImage) {
              this.bookUrllist1 = [
                {
                  url: row.coverImage,
                },
              ];
            }
          } else {
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 编辑
    handleEditzt11(e) {
      this.tableData = [
        {
          sceneId: "",
          productId: "",
          angle: "",
          furnitureImage: "",
          blankImage: [],
          xAxis: "",
          yAxis: "",
          furnitureWidth: "",
          furnitureHeight: "",
          yuliuone: "",
          acquiesceBrick: "",
          blankImage2: "",
        },
      ];
      this.tableData1 = [
        {
          sceneId: "",
          productId: "",
          angle: "",
          furnitureImage: "",
          blankImage: [],
          xAxis: "",
          yAxis: "",
          furnitureWidth: "",
          furnitureHeight: "",
          yuliuone: "",
          acquiesceBrick: "",
        },
      ];
      this.bookUrllist = [];
      this.bookUrllist1 = [];
      this.bookUrllist2 = [];
      this.bookUrllist3 = [];
      this.bookUrllist4 = [];
      this.bookUrllist5 = [];
      this.bookUrllist6 = [];
      var params = new URLSearchParams();
      params.append("id", e.id);
      params.append("flag", "queryById");
      params.append("dbName", window.localStorage.getItem("JJHTDBnmame"));
      changeConfig(params)
        .then((res) => {
          if (res.data.code == "1") {
            this.specialShowgg = true;

            this.tableData[0] = res.data.record;
            this.tableData[0].acquiesceBrick = Number(
              res.data.record.acquiesceBrick
            );
            this.tableData[0].acquiesceBrick2 = Number(
              res.data.record.acquiesceBrick2
            );
            this.tableData1[0] = res.data.record;
            if (res.data.record.furnitureImage) {
              this.bookUrllist = [
                {
                  url: res.data.record.furnitureImage,
                },
              ];
            }

            if (res.data.record.blankImage) {
              this.bookUrllist1 = JSON.parse(res.data.record.blankImage);
              this.tableData[0].blankImage = JSON.parse(
                res.data.record.blankImage
              );
            }

            if (res.data.record.yuliuone) {
              this.bookUrllist2 = [
                {
                  url: res.data.record.yuliuone,
                },
              ];
            }

            if (res.data.record.blankImage2) {
              this.bookUrllist4 = [
                {
                  url: res.data.record.blankImage2,
                },
              ];
            }
          } else {
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    //上传视频之前
    beforeUpload1(file) {
      if (file.type != "" || file.type != null || file.type != undefined) {
        //截取视频的后缀，判断视频类型
        const FileExt = file.name.replace(/.+\./, "").toLowerCase();
        //计算视频的大小
        const isLt5M = file.size / 1024 / 1024 < 500; //这里做视频大小限制
        //如果大于50M
        if (!isLt5M) {
          this.$message.error("上传视频大小不能超过 500MB!");
          return false;
        }
        //如果视频类型不在允许上传的范围内
        if (this.fileType.includes(FileExt)) {
          return true;
        } else {
          this.$message.error("上传视频格式不正确!");
          return false;
        }
      }
    },
    //上传了的视频给移除的事件，由于我没有用到默认的展示，所以没有用到
    handleRemove() {},
    //这是我自定义的移除事件
    handleClose(i) {
      this.fileList.splice(i, 1); //删除上传的视频
      if (this.fileList.length == 0) {
        //如果删完了
        this.fileflag = true; //显示url必填的标识
        this.$set(this.rules.url, 0, {
          required: true,
          validator: this.validatorUrl,
          trigger: "blur",
        }); //然后动态的添加本地方法的校验规则
      }
    },
    //超出视频个数的回调
    handleExceed() {
      this.$message({
        type: "warning",
        message: "超出最大上传视频数量的限制！",
      });
      return;
    },
    //上传视频的事件
    uploadFile(item) {
      this.loading = Loading.service({
        lock: true,
        text: "视频上传中........",
        // background: 'rgba(0, 0, 0, 0.7)',
        target: document.querySelector("#loadingDiv"),
      });
      //上传视频的需要formdata类型;所以要转
      let FormDatas = new FormData();
      FormDatas.append("file", item.file);
      upload(FormDatas).then((res) => {
        if (res.data.code == "0") {
          setTimeout(() => {
            this.loading.close();
          }, 2000);
          this.form.productView = res.data.yunUrl;
          this.$set(this.form, "videoUrl", this.form.productView + "/" + 1);
          console.log(this.form.productView);
          // this.Product.filename=res.data.fileNameOld;
        }
      });

      // 	})
    },
    //上传成功后的回调
    handleSuccess() {},
    // 添加产品
    RechargeRole() {
      // if (!this.form.title) {
      //   this.$message({
      //     showClose: true,
      //     message: '请输入产品名称！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (!this.form.productModel) {
      //   this.$message({
      //     showClose: true,
      //     message: '请输入产品型号！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (!this.form.formNum) {
      //   this.$message({
      //     showClose: true,
      //     message: '请输入产品面数！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (!this.form.productVr) {
      //   this.$message({
      //     showClose: true,
      //     message: '请输入产品vr链接！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (!this.form.productView) {
      //   this.$message({
      //     showClose: true,
      //     message: '请输入产品视频链接！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (!this.form.productSynopsis) {
      //   this.$message({
      //     showClose: true,
      //     message: '请输入产品简介！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (this.checkList && this.checkList[0].length == 0) {
      //   this.$message({
      //     showClose: true,
      //     message: '请选择产品！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (this.checkList && this.checkList[1].length == 0) {
      //   this.$message({
      //     showClose: true,
      //     message: '请选择系列！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (this.checkList && this.checkList[2].length == 0) {
      //   this.$message({
      //     showClose: true,
      //     message: '请选择色彩！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (this.checkList && this.checkList[3].length == 0) {
      //   this.$message({
      //     showClose: true,
      //     message: '请选择规格！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (this.checkList && this.checkList[4].length == 0) {
      //   this.$message({
      //     showClose: true,
      //     message: '请选择系列！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (!this.form.productImage) {
      //   this.$message({
      //     showClose: true,
      //     message: '请上传产品主图！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (!this.form.coverImage) {
      //   this.$message({
      //     showClose: true,
      //     message: '请上传产品单片图！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (!this.form.qrImage) {
      //   this.$message({
      //     showClose: true,
      //     message: '请上传二维码！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (this.form.productImages && this.form.productImages.length == 0) {
      //   this.$message({
      //     showClose: true,
      //     message: '请上传产品展示图！',
      //     type: 'warning'
      //   });
      //   return
      // }

      this.form.space = this.checkList[0].filter((value) => value !== 0).join();
      this.form.style = this.checkList[1].filter((value) => value !== 0).join();
      // this.form.space = this.checkList[2].join()
      // this.form.colour = this.checkList[3].join()

      // this.form.coverImage = this.form.coverImage && this.form.coverImage.length == 0 ? '' : JSON.stringify(this.form.coverImage)
      var params = new URLSearchParams();
      params.append("dbName", window.localStorage.getItem("JJHTDBnmame"));

      params.append("title", this.form.title ? this.form.title : "");
      params.append(
        "coverImage",
        this.form.coverImage ? this.form.coverImage : ""
      );
      params.append(
        "sceneWidth",
        this.form.sceneWidth ? this.form.sceneWidth : ""
      );
      params.append(
        "sceneHeight",
        this.form.sceneHeight ? this.form.sceneHeight : ""
      );
      params.append(
        "minification",
        this.form.minification ? this.form.minification : ""
      );
      params.append("context", this.form.context ? this.form.context : "");
      params.append("status", this.form.status);
      params.append("sort", this.form.sort ? this.form.sort : "");
      params.append("flag", "save");

      params.append("style", this.form.style ? this.form.style : "");
      params.append("space", this.form.space ? this.form.space : "");
      params.append("id", this.form.id ? this.form.id : "");

      changeScene(params)
        .then((res) => {
          if (res.data.code == "1") {
            this.$message({
              type: "success",
              message: this.title == "编辑" ? "修改成功" : "添加成功",
            });
            this.getqueryCaseType();
            location.reload();
            this.editFormVisible = false;
            this.form = {
              title: "",
              coverImage: "",
              sceneWidth: "",
              sceneHeight: "",
              minification: "",
              context: "",
              status: "0",
              sort: "",
            };
            this.form.status = 0;
            this.form.productPaixu = 0;
            this.form.solongpicrt = "";
            this.activeName = "1";
            this.checkList = [[], [], [], [], [], [], [], [], [], []];
          } else {
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 删除规格
    handleClick(row) {
      const params = {
        dbName: window.localStorage.getItem("JJHTDBnmame"),
        flag: "delete",
        id: row.id,
      };
      changeConfig(params).then((response) => {
        if (response.data.code == "1") {
          this.$message({
            type: "success",
            message: "删除成功！",
          });
          let a = {
            id: this.ggxsid,
          };
          this.selectBox(a);
        } else {
        }
      });
    },
    handleClic1k(index = 0, row) {
      if (this.tableData[0].productId == "") {
        this.$message({
          type: "warning",
          message: "请选择产品！",
        });
        return;
      }
      if (this.tableData[0].furnitureImage == "") {
        this.$message({
          type: "warning",
          message: "请上传家具图片",
        });
        return;
      }
      if (
        this.tableData[0].blankImage &&
        this.tableData[0].blankImage.length == 0
      ) {
        this.$message({
          type: "warning",
          message: "请上传表面空图",
        });
        return;
      }
      if (this.tableData[0].xAxis == "") {
        this.$message({
          type: "warning",
          message: "请输入x轴",
        });
        return;
      }
      if (this.tableData[0].yAxis == "") {
        this.$message({
          type: "warning",
          message: "请输入y轴",
        });
        return;
      }
      if (this.tableData[0].furnitureWidth == "") {
        this.$message({
          type: "warning",
          message: "请输入家具宽",
        });
        return;
      }
      if (this.tableData[0].furnitureHeight == "") {
        this.$message({
          type: "warning",
          message: "请输入家具高",
        });
        return;
      }
      // console.log(
      //   this.tableData[0].blankImage && this.tableData[0].blankImage.length > 0
      //     ? JSON.stringify(this.tableData[0].blankImage)
      //     : ""
      // );
      const productModelList1 = this.data12.find(
        (item) => item.key == this.tableData[0].acquiesceBrick
      );
      const productModelList2 = this.data12.find(
        (item) => item.key == this.tableData[0].acquiesceBrick2
      );
      var params = new URLSearchParams();
      params.append("flag", "save");
      params.append("dbName", window.localStorage.getItem("JJHTDBnmame"));
      params.append("id", this.tableData[0].id ? this.tableData[0].id : "");
      params.append("sceneId", this.ggxsid);
      params.append(
        "productId",
        this.tableData[0].productId ? this.tableData[0].productId : ""
      );
      params.append(
        "furnitureImage",
        this.tableData[0].furnitureImage ? this.tableData[0].furnitureImage : ""
      );
      params.append(
        "angle",
        this.tableData[0].angle ? this.tableData[0].angle : ""
      );
      params.append(
        "blankImage",
        this.tableData[0].blankImage && this.tableData[0].blankImage.length > 0
          ? JSON.stringify(this.tableData[0].blankImage)
          : ""
      );
      params.append(
        "yuliuone",
        this.tableData[0].yuliuone ? this.tableData[0].yuliuone : ""
      );
      params.append(
        "acquiesceBrick",
        this.tableData[0].acquiesceBrick ? this.tableData[0].acquiesceBrick : ""
      );
      params.append(
        "acquiesceBrick2",
        this.tableData[0].acquiesceBrick2
          ? this.tableData[0].acquiesceBrick2
          : ""
      );
      params.append('productModelList1', productModelList1 ? productModelList1.productModel : '');
      params.append('productModelList2', productModelList2 ? productModelList2.productModel : '');
      params.append(
        "xAxis",
        this.tableData[0].xAxis ? this.tableData[0].xAxis : ""
      );
      params.append(
        "yAxis",
        this.tableData[0].yAxis ? this.tableData[0].yAxis : ""
      );
      params.append(
        "furnitureWidth",
        this.tableData[0].furnitureWidth ? this.tableData[0].furnitureWidth : ""
      );
      params.append(
        "furnitureHeight",
        this.tableData[0].furnitureHeight
          ? this.tableData[0].furnitureHeight
          : ""
      );

      params.append(
        "xAxis2",
        this.tableData1[0].xAxis2 ? this.tableData1[0].xAxis2 : ""
      );
      params.append(
        "yAxis2",
        this.tableData1[0].yAxis2 ? this.tableData1[0].yAxis2 : ""
      );
      params.append(
        "furnitureWidth2",
        this.tableData1[0].furnitureWidth2
          ? this.tableData1[0].furnitureWidth2
          : ""
      );
      params.append(
        "furnitureHeight2",
        this.tableData1[0].furnitureHeight2
          ? this.tableData1[0].furnitureHeight2
          : ""
      );
      params.append(
        "blankImage2",
        this.tableData[0].blankImage2 ? this.tableData[0].blankImage2 : ""
      );
      changeConfig(params).then((response) => {
        if (response.data.code == "1") {
          this.specialShowgg = false;
          this.$message({
            type: "success",
            message: response.data.msg,
          });
          let a = {
            id: this.ggxsid,
          };
          this.selectBox(a);
          this.tableData = [
            {
              sceneId: "",
              productId: "",
              angle: "",
              furnitureImage: "",
              blankImage: [],
              xAxis: "",
              yAxis: "",
              furnitureWidth: "",
              furnitureHeight: "",
              yuliuone: "",
              acquiesceBrick: "",
              blankImage2: "",
            },
          ];
          this.tableData1 = [
            {
              sceneId: "",
              productId: "",
              angle: "",
              furnitureImage: "",
              blankImage: [],
              xAxis: "",
              yAxis: "",
              furnitureWidth: "",
              furnitureHeight: "",
              yuliuone: "",
              acquiesceBrick: "",
            },
          ];
          this.bookUrllist = [];
          this.bookUrllist1 = [];
          this.bookUrllist2 = [];
          this.bookUrllist3 = [];
          this.bookUrllist4 = [];
          this.bookUrllist5 = [];
          this.bookUrllist6 = [];
        }
      });
    },
    handleUploadAgain() {
      this.isHandlingChange = false;
    },
    handleCheck(index, row) {
      this.IMGindex = index;
    },
    handlePreview11(file) {
      this.dialogVisibleImg4 = true;
      this.gg_img = file.url;
    },

    handlePreviewgg(file) {
      this.dialogVisibleImg3 = true;
      this.GJ_img = file.url;
    },
    handlePreviewgg22(file) {
      this.dialogVisibleImg5 = true;
      this.xqt_img = file.url;
    },
    handlePreview(file) {
      this.form.productImage = file.url;
      this.dialogVisibleImg = true;
    },
    handlePreviewcp(file) {
      this.form.coverImage = file.url;
      this.dialogVisibleImg1 = true;
    },
    handlePreview1(file) {
      this.tableData[this.IMGindex].furnitureImage = file.url;
      this.dialogVisibleImg = true;
    },
    handlePreview23131(file) {
      this.tableData[this.IMGindex].yuliuone = file.url;
      this.dialogVisibleImg1 = true;
    },
    handlePreview2313111(file) {
      this.tableData[this.IMGindex].yuliuone = file.url;
      this.dialogVisibleImg2 = true;
    },
    handlePreview231311112313(file) {
      this.tableData[this.IMGindex].blankImage2 = file.url;
      this.dialogVisibleImg6 = true;
    },
    handlePreview11(file) {
      this.xstpss = file.url;
      this.dialogVisibleImg2 = true;
    },
    handlePreview22(file) {
      this.form.productUrl2 = file.url;
      this.dialogVisibleImg211 = true;
    },
    // 点击取消
    formCancal() {
      this.specialShowgg = false;
      // this.$refs.vueEditor.editor.root.innerHTML = '';
      // this.tableOption = [];
      // this.bookUrllist = [];

      // if (this.dialogTitle == '新增商品') {
      //   this.tableData = [{
      //     attr_list: [],
      //     num: '',
      //     price: '',
      //     no: '',
      //     pic: '',
      //     specifications: ''
      //   }]
      // }
    },
    handleCurrentChange() {},
    // 添加一行
    add() {
      this.specialShowgg = true;
      this.tableData = [
        {
          sceneId: "",
          productId: "",
          angle: "",
          furnitureImage: "",
          blankImage: [],
          xAxis: "",
          yAxis: "",
          furnitureWidth: "",
          furnitureHeight: "",
          yuliuone: "",
          acquiesceBrick: "",
          blankImage2: "",
        },
      ];
      this.tableData1 = [
        {
          sceneId: "",
          productId: "",
          angle: "",
          furnitureImage: "",
          blankImage: [],
          xAxis: "",
          yAxis: "",
          furnitureWidth: "",
          furnitureHeight: "",
          yuliuone: "",
          acquiesceBrick: "",
        },
      ];
      this.bookUrllist = [];
      this.bookUrllist1 = [];
      this.bookUrllist2 = [];
      this.bookUrllist3 = [];
      this.bookUrllist4 = [];
      this.bookUrllist5 = [];
      this.bookUrllist6 = [];
    },
    // 关于上传pdf部分 start
    handleSuccess(res, file) {
      // 上传成功的钩子
      this.form.yuliuone = file.response.yunUrl;
    },
    fileChange(file, fileList) {
      //文件状态改变时的钩子，添加文件、上传成功和上传失败时都会被调用

      this.fileList = fileList;
    },
    8(file, fileList) {
      //文件列表移除文件时的钩子
      this.form.yuliuone = "";
    },
    handlePreview1111(file) {
      //点击文件列表中已上传的文件时的钩子
    },
    handleExceed(files, fileList) {
      this.$message.warning(
        `当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${
          files.length + fileList.length
        } 个文件`
      );
    },
    beforeUpload(file) {
      //文件上传之前的钩子函数
      this.file = file;
      this.fileName = file.name;
      // this.fileSize = file.size;
      const extension = file.name.split(".").slice(-1) == "pdf";
      if (!extension) {
        this.$message.warning("上传模板只能是pdf格式!");
        return false;
      }
      // let reader = new FileReader();
      // reader.readAsDataURL(file);
      // console.log(new FileReader().readAsDataURL(file),'reader.readAsDataURL(file)')
      // console.log(reader.result,'reader.result')

      // let that = this;
      // reader.onload = function() {
      //   that.fileData = reader.result;
      // };
      // console.log(that.fileData,'that.fileData')
      // return false; // 返回false不会自动上传
    },
    // 删除文件之前的钩子
    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`);
    },
    formCancal2() {},
    formCancal1() {},
    uploadFileError(res, file, fileList) {},
    handleBeforeUpload(file) {},
    uploadFileSuccess(res, file, fileList) {
      if (res.code == 0) {
        fileList.forEach((att) => {
          if (att.response) {
            let obj = {
              url: "",
            };
            if (file.response.yunUrl) {
              this.form.coverImage = file.response.yunUrl;
            }
          }
        });
      }
    },
    uploadFileSuccess1(res, file, fileList) {
      // this.form.productImages = [];
      if (res.code == 0) {
        fileList.forEach((att) => {
          if (att.response) {
            let obj = {
              url: "",
            };
            obj.url = file.response.yunUrl;
            this.form.productImages.push(obj);
          }
        });
      }
    },
    uploadFileSuccess2(res, file, fileList) {
      if (res.code == 0) {
        fileList.forEach((att) => {
          if (att.response) {
            let obj = {
              url: "",
            };
            obj.url = file.response.yunUrl;
            this.form.longpic.push(obj);
          }
        });
      }
    },
    exceedFile(res, file, fileList) {
      this.$message.error("只能上传" + this.limitnum + "个文件");
    },
    handleSmallPicSuccess(res, file, fileList) {
      if (file && file.length == 2) {
        file.shift();
      }
      this.uploadImg(res, 1);
    },
    handleSmallPicSuccess222(res, file, fileList) {
      if (file && file.length == 2) {
        file.shift();
      }
      this.uploadImg(res, 7);
    },
    handleSmallPicSuccessgg(res, file, fileList) {
      // 处理上传成功逻辑
      this.uploadImg(res, 6);
      // ...

      // 标记已经上传过文件
    },
    handleSmallPicSuccess11(res, file, fileList) {
      this.uploadImg(res, 8);
    },
    handleSmallPicSuccess1122(res, file, fileList) {
      this.uploadImg(res, 9);
    },
    handleSmallPicSuccess11222223(res, file, fileList) {
      this.uploadImg(res, 10);
    },
    handleSuccess11(res, file, fileList) {
      // console.log(res);
      // let obj = {
      //   url: res.yunUrl
      // }

      // this.tableData[this.IMGindex].blankImage.push(obj);
      // console.log(this.tableData[this.IMGindex].blankImage);
      this.uploadImg(res, 11);
    },
    handleSmallPicSuccess1(res, file, fileList) {
      this.uploadImg(res, 6);
    },
    handleSmallPicSuccess2(res, file, fileList) {
      if (file && file.length == 2) {
        file.shift();
      }
      this.uploadImg(res, 3);
    },
    handleSmallPicSuccess3(res, file, fileList) {
      this.uploadImg(res, 4);
    },
    handleSmallPicSuccess4(res, file, fileList) {
      this.uploadImg(res, 5);
    },
    uploadImg(file, type) {
      let that = this;
      let formData = new FormData();
      formData.append("file", file.raw);
      that.loading = Loading.service({
        lock: true,
        text: "上传中...",
        // background: 'rgba(0, 0, 0, 0.7)',
        target: document.querySelector("#loadingDiv"),
      });
      upload(formData)
        .then((res) => {
          if (res.data.code == 0) {
            that.loading.close();
            if (type == 1) {
              that.form.productImage = res.data.yunUrl;
            } else if (type == 2) {
              let obj = {
                url: res.data.yunUrl,
              };
              that.form.coverImage = res.data.yunUrl;
            } else if (type == 3) {
              that.form.qrImage = res.data.yunUrl;
            } else if (type == 4) {
              let obj = {
                url: res.data.yunUrl,
              };

              that.form.productImages.push(obj);
            } else if (type == 5) {
              that.form.longpic = res.data.yunUrl;
            } else if (type == 6) {
              this.tableData[this.IMGindex].furnitureImage = res.data.yunUrl;
            } else if (type == 7) {
              that.form.productUrl2 = res.data.yunUrl;
            } else if (type == 8) {
              this.tableData[this.IMGindex].yuliuone = res.data.yunUrl;
            } else if (type == 9) {
              this.tableData[this.IMGindex].acquiesceBrick = res.data.yunUrl;
            } else if (type == 10) {
              this.tableData[this.IMGindex].blankImage2 = res.data.yunUrl;
            } else if (type == 11) {
              let obj = {
                url: res.data.yunUrl,
              };
              this.tableData[this.IMGindex].blankImage.push(obj);
            }
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    tableRowClassName({ row, rowIndex }) {
      if (rowIndex === 1) {
        return "warning-row";
      } else if (rowIndex === 3) {
        return "success-row";
      }
      return "";
    },
    handleRemove1(file) {
      this.tableData[this.IMGindex].furnitureImage = "";
    },
    handleRemove111(file) {
      this.tableData[this.IMGindex].yuliuone = "";
    },
    handleRemove1113232(file) {
      this.tableData[this.IMGindex].blankImage2 = "";
    },
    handleRemove22() {
      this.form.productUrl2 = "";
    },
    handleRemove8() {},
    handleRemove2(file) {
      // 1.获取将要删除图片的临时路径
      const filePath = file.url;
      // 2.从pics数组中，找到图片对应的索引值
      // const i = this.form.coverImage.findIndex((x) => x.url === filePath);
      // 3.调用splice方法，移除图片信息
      this.form.coverImage = "";
    },
    handleRemove3(file) {
      const filePath = file.url;

      const i = this.tableData[0].blankImage.findIndex(
        (x) => x.url === filePath
      );
      // 3.调用splice方法，移除图片信息
      this.tableData[0].blankImage.splice(i, 1);
      console.log(this.tableData[0].blankImage);
    },
    handleRemove4(file) {
      // 1.获取将要删除图片的临时路径
      const filePath = file.url;
      // 2.从pics数组中，找到图片对应的索引值
      const i = this.form.productImages.findIndex((x) => x.url === filePath);
      // 3.调用splice方法，移除图片信息
      this.form.productImages.splice(i, 1);
    },
    handleRemove5(file) {
      // 1.获取将要删除图片的临时路径
      const filePath = file.url;
      // 2.从pics数组中，找到图片对应的索引值
      const i = this.form.longpic.findIndex((x) => x.url === filePath);
      // 3.调用splice方法，移除图片信息
      this.form.longpic.splice(i, 1);
    },
    closeDialog() {
      this.form = {
        name: "",
        productModel: "",
        productVr: "",
        yuliuone: "",
        productMoney: "",
        seriesRootId: "",
        productSynopsis: "",
        yuliutow: "",
        productImage: "",
        productUrl2: "",
        productImages: [],
        status: 0,
        productView: "",
        productId: [],
        preview: "",
        coverImage: "",
        productPaixu: 0,
        qrImage: "",
        style: "",
        space: "",
        colour: "",
        space: "",
        series: "",
        longpic: [],
        formNum: "",
        craft: "",
        unit: "",
        isOwn: "0",
        caseJiluIdList: "",
        yuliuone: "",
      };
      this.activeName = "1";
      this.bookUrllist = [];
      this.bookUrllist1 = [];
      this.bookUrllist2 = [];
      this.bookUrllist3 = [];
      this.bookUrllist4 = [];
      this.bookUrllist5 = [];
      this.checkList = [[], [], [], [], [], [], [], [], [], []];
      this.tableData = [
        {
          no: "",
          attr_list: [],

          price: "",
          num: "",
          pic: "",
          seriesRootId: "",
        },
      ];
      location.reload();
    },
    closeDialog222() {
      this.form = {
        name: "",
        productModel: "",
        productVr: "",
        yuliuone: "",
        productMoney: "",
        seriesRootId: "",
        productSynopsis: "",
        yuliutow: "",
        productImage: "",
        productUrl2: "",
        productImages: [],
        status: 0,
        productView: "",
        productId: [],
        preview: "",
        coverImage: "",
        productPaixu: 0,
        qrImage: "",
        style: "",
        space: "",
        colour: "",
        space: "",
        series: "",
        longpic: [],
        formNum: "",
        craft: "",
        unit: "",
        isOwn: "0",
        caseJiluIdList: "",
        yuliuone: "",
      };
      this.activeName = "1";
      this.bookUrllist = [];
      this.bookUrllist1 = [];
      this.bookUrllist2 = [];
      this.bookUrllist3 = [];
      this.checkList = [[], [], [], [], [], [], [], [], [], []];
      this.tableData = [
        {
          no: "",
          attr_list: [],

          price: "",
          num: "",
          pic: "",
          seriesRootId: "",
        },
      ];
      location.reload();
    },
    // 查询产品分类
    getproductTypeList() {
      var params = new URLSearchParams();
      params.append("dbName", window.localStorage.getItem("JJHTDBnmame"));

      params.append("flag", "query");

      changeType(params)
        .then((res) => {
          if (res.data.code >= 0) {
            this.classification = res.data.data[0].children;
            this.classification1 = res.data.data[0].children[0].children;
            res.data.data[0].children.forEach((item) => {
              if (item.name == "规格") {
                this.GGlist = item.children;
              }
              if (item.name == "系列") {
                this.XLList = item.children;
              }
            });
          } else {
            this.classification = [];
            this.classification1 = [];
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },

    // 查询产品分类
    getqueryCaseType() {
      var params = new URLSearchParams();
      params.append("dbName", window.localStorage.getItem("JJHTDBnmame"));

      params.append("curPage", this.curPage);
      params.append("number", 10);
      params.append("flag", "query");

      if (this.input1) {
        params.append("status", this.input1);
      }

      if (this.input2) {
        params.append("size", this.input2);
      }
      if (this.input3) {
        params.append("series", this.input3);
      }
      if (this.input4) {
        params.append("keyword", this.input4);
      }
      if (this.input5) {
        params.append("yuliutwo", this.input5);
      }
      changeScene(params)
        .then((res) => {
          this.total = res.data.sumcount;
          if (res.data.code == "1") {
            this.data = res.data.list;
          } else {
            this.data = [];
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    handleCurrentChange122(val) {
      this.currentPage1 = val;
      this.selectBox();
    },
    handleCurrentChange1(val) {
      this.curPage = val;
      this.getqueryCaseType();
    },
    handleSelectionChange(val) {
      this.handleSelectionChangeList = val;
    },
    //选中规格
    selectBox(val) {
      this.ggxsid = val.id;

      const params = {
        dbName: window.localStorage.getItem("JJHTDBnmame"),
        flag: "query",
        sceneId: val.id,
        curPage: this.currentPage1,
        number: 10,
      };
      changeConfig(params).then((response) => {
        if (response.data.code == "1") {
          this.editFormVisible11 = true;
          this.cplistxs = response.data.list;
          // response.data.list.forEach((item) => {
          //   item.sceneId = item.sceneId ? item.sceneId : item.id

          // })
          // let itemcc=response.data.list[0]
          // if(itemcc.blankImage){
          //   this.bookUrllist3=JSON.parse(itemcc.blankImage)

          // }else {
          //     item.blankImage = []
          //   }
          // if(itemcc.furnitureImage){
          //   this.bookUrllist=[
          //     {
          //       url:itemcc.furnitureImage
          //     }
          //   ]
          // } if(itemcc.yuliuone){
          //   this.bookUrllist1=[
          //     {
          //       url:itemcc.yuliuone
          //     }
          //   ]
          // }
          // if(itemcc.acquiesceBrick){
          //   this.bookUrllist2=[
          //     {
          //       url:itemcc.acquiesceBrick
          //     }
          //   ]
          // }
          // this.tableData = response.data.list
        } else {
          this.cplistxs = [];
        }
      });
    },

    change6(index, i) {
      const params = {
        dbName: window.localStorage.getItem("JJHTDBnmame"),
        attrGroup: this.attrGroup_ID[i], // this.attrGroupID,
        // attrGroup: this.attrGroupID, // this.attrGroupID,
        attrName: this.tableData[index].attr_list[i].attr_name,
      };
      QueryAttr(params).then((response) => {
        if (response.data.code == "1") {
          this.tableData[index].attr_list[i].attr_id = response.data.record.id;
          console.log(this.tableData);
        }
      });
    },
    change5() {
      if (this.input5) {
        this.tableOption.push({
          label: this.input5,
        });

        // this.tableData[0].attr_list = this.tableOption
        // return
        const params = {
          dbName: window.localStorage.getItem("JJHTDBnmame"),
          attrGroup: this.input5,
        };
        QueryAttrGroup(params).then((response) => {
          if (response.data.code == "1") {
            this.attrGroup_ID.push(response.data.record.id);
            for (let i in this.tableData) {
              this.tableData[i].attr_list.push({
                attr_group_id: response.data.record.id, //表头id
                attr_group_name: this.input5, //表头
                attr_id: "",
                attr_name: "",
              });
            }
          }
        });
      } else {
        this.$message({
          type: "warning",
          message: "请填写规格",
        });
      }
    },
    generateData() {
      var params = new URLSearchParams();
      params.append("dbName", "sedysgd_cn");

      this.$store
        .dispatch("CP/queryAllSndysgd", params)
        .then((res) => {
          if (res.code == "1") {
            var a = [];
            console.log(res.record);
            res.record.forEach((item, index) => {
              a.push({
                label: item.name + "-" + item.productModel,
                key: item.id,
                pinyin: res.record[index],
              });
            });
            this.data1 = a;
          } else {
            this.data1 = [];
          }
        })
        .catch(() => {});
    },
    forceUpdate() {},
    handleChange(value, direction, movedKeys) {
      this.form.caseJiluIdList = value.join();
    },
    generateData1() {
      var params = new URLSearchParams();
      // params.append('dbName', 'schende_cns');
      params.append("dbName", "schender");

      this.$store
        .dispatch("CP/getqueryAllProduct", params)
        .then((res) => {
          if (res.code == "1") {
            var a = [];

            res.record.forEach((item, index) => {
              a.push({
                label: item.name + "-" + item.productModel,
                key: item.id,
                // pinyin: res.record[index],
                productModel: item.productModel,
              });
            });
            console.log(a, "a");
            this.data12 = a;
          } else {
            this.data12 = [];
          }
        })
        .catch(() => {});
    },
  },
  created() {
    const SEDJJXCX_LHFW_UserInformation_HT = JSON.parse(
      decodeURIComponent(
        window.localStorage.getItem("SEDJJXCX_LHFW_UserInformation_HT")
      )
    );
    this.SEDJJXCX_LHFW_UserInformation_HT = SEDJJXCX_LHFW_UserInformation_HT;

    this.getqueryCaseType();
    this.getproductTypeList();
    this.generateData();
    this.generateData1();
  },
  mounted() {},
};
</script>

<style lang="scss">
.el-table thead {
  color: #000000 !important;
}

.el-tree-node__label {
  font-weight: 900 !important;
}

.el-tree-node__content {
  font-weight: 900 !important;
}

.dashboard {
  &-container {
    margin: 30px;
  }

  &-text {
    font-size: 30px;
    line-height: 46px;
  }
}

.item {
  margin-bottom: 18px;
}

.el-input_ss1 {
  width: 200px;
  margin-top: 4px;
}

.pagdw {
  float: right;
  margin-top: 46px;
}

.el-input_ss {
  width: 200px;
}

.el-input_ss1 {
  width: 200px;
  margin-top: 3px;
}

.el-transfer-panel {
  width: 550px;
}

.el-table td.el-table__cell div {
  display: block !important;
}
.bm {
  font-weight: 800;
  font-size: 20px;
  color: #000;
}
</style>
