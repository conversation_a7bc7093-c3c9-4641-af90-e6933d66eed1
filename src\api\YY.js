import request from '@/utils/request'

export function createWorkerStory(params) {//添加故事
  return request({
    url: 'oe_createWorkerStory_.csp',
    method: 'get',
    params
  })
}

export function yuyues(params) {//查询预约管理
  return request({
    url: 'oe_yuyues_.csp',
    method: 'get',
    params
  })
}

export function delStorys(params) {//删除故事
  return request({
    url: 'oe_delStorys_.csp ',
    method: 'get',
    params
  })
}
export function serviceList(params) {
  return request({
    url: 'oe_serviceList_.csp',
    method: 'get',
    params
  })
}
export function updateServiceStatus(params) {
  return request({
    url: 'oe_updateServiceStatus_.csp',
    method: 'get',
    params
  })
}

