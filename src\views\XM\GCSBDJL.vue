<template>
  <div class="app-container">
    <!-- 搜索筛选 -->
    <el-form :inline="true" class="user-search">

      <el-form-item prop="Product.catld">
        <el-select size="mini" v-model="input3" placeholder="保单状态" @change="change2">
          <el-option size="mini" v-for="item in classify" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item prop="Product.catld">
          <el-select size="mini" v-model="input3" placeholder="角色" @change="change2">
            <el-option size="mini" v-for="item in classify" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item> -->
      <el-form-item>
        <el-input size="mini" placeholder="请输入内容" v-model="input4" class="el-input_ss1">
          <el-button size="mini" slot="append" icon="el-icon-search" @click="change3"></el-button>
        </el-input>
      </el-form-item>
      <el-form-item>
        <!-- <el-button size="mini" type="primary" icon="el-icon-plus" @click="increase()">添加</el-button> -->

        <el-button size="mini" type="primary" @click="Reload" icon="el-icon-refresh-left">重新加载</el-button>
        <!-- <el-button @click="leading">批量导出</el-button> -->




      </el-form-item>
    </el-form>

    <el-table :data="data" border fit highlight-current-row @selection-change="handleSelectionChange">

      <el-table-column align="center" label="序号" width="95">
        <template slot-scope="scope">{{ scope.$index + 1 }}</template>
      </el-table-column>

      <!-- <el-table-column align="center" prop="name" label="微信头像" width="90">
                <template slot-scope="scope">
          <el-popover placement="bottom" trigger="hover" width="100">
            <img :src="scope.row.wxPhoto" width="100%" />
            <img slot="reference" :src="scope.row.wxPhoto" :alt="scope.row.wxPhoto"
              style="max-height: 40px;max-width: 40px;" />
          </el-popover>
        </template>
  
            </el-table-column> -->
      <el-table-column align="center" prop="img" label="申请人名称" width="300">
        <template slot-scope="scope">
          {{ scope.row.name }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="申请人手机号" width="200">
        <template slot-scope="scope">{{ scope.row.phone }}</template>
      </el-table-column>
      <el-table-column align="center" label="身份证号" width="200">
        <template slot-scope="scope">{{ scope.row.idCard ? scope.row.idCard : '暂无' }}</template>
      </el-table-column>
      <el-table-column align="center" label="开始时间" width="200">
        <template slot-scope="scope">{{ scope.row.starTime }}</template>
      </el-table-column>
      <el-table-column align="center" label="结束时间" width="200">
        <template slot-scope="scope">{{ scope.row.endTime }}</template>
      </el-table-column>
      <el-table-column align="center" label="状态" width="100">
        <template slot-scope="scope">
          <el-button v-if="scope.row.status == '0'" size="mini" type="warning">待确认</el-button>
          <el-button v-if="scope.row.status == '1'" size="mini" type="success">已购买</el-button>
          <el-button v-if="scope.row.status == '2'" size="mini" type="danger">已停止</el-button>
        </template>
      </el-table-column>

      <el-table-column align="center" prop="img" label="小区名" width="200">
        <template slot-scope="scope">
          {{ scope.row.community ? scope.row.community : '暂无' }}
        </template>
      </el-table-column>
      <el-table-column align="center" prop="img" label="备注" width="300">
        <template slot-scope="scope">
          {{ scope.row.remark ? scope.row.remark : '暂无' }}
        </template>
      </el-table-column>




      <!-- fixed="right"  width="200" -->

      <el-table-column fixed="right" label="操作" align="center" width="100">
        <template slot-scope="scope">
        

          <el-button size="mini" type="primary" @click="handleEditzt(scope.row)">修改</el-button>
          <!-- <el-button size="mini" type="danger" @click="handleDelete(scope.row.id)">查看维保记录</el-button> -->

        </template>
      </el-table-column>
    </el-table>
    <el-row>
      <el-pagination :current-page.sync="currentPage" background style="margin-top:30px" :page-size="pageSize"
        layout="total,prev,pager,next,jumper" :total="total" @current-change="handleCurrentChange1" />

    </el-row>
    <!-- 修改 -->
    <!-- 编辑界面 -->
    <el-dialog title="修改" :visible.sync="specialShow" @close="closeDialog" width="50%">
      <el-form label-position="rigth" label-width="120px">

        <el-form-item label="申请人名称:">
          <el-input style="width: 100%;" size="mini" v-model="special.name" auto-complete="off"
            placeholder="申请人名称"></el-input>
        </el-form-item>


        <el-form-item label="申请人手机号:">
          <el-input  style="width: 100%;" size="mini" v-model="special.phone" auto-complete="off"
            placeholder="申请人手机号"></el-input>
        </el-form-item>

        <el-form-item label="身份证号:">
          <el-input  style="width: 100%;" size="mini" v-model="special.idCard" auto-complete="off"
            placeholder="身份证号"></el-input>
        </el-form-item>

        <el-form-item label="小区名:">
          <el-input    :disabled="true"  style="width: 100%;" size="mini" v-model="special.community" auto-complete="off"
            placeholder="小区名"></el-input>
        </el-form-item>
        <el-form-item label="到期时间:">

          <el-date-picker v-model="value2" type="daterange" range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>


        <el-form-item label="状态:">
          <el-radio v-model="special.status" :label="'0'">待确认</el-radio>
          <el-radio v-model="special.status" :label="'1'">已购买</el-radio>
          <el-radio v-model="special.status" :label="'2'">已停止</el-radio>
        </el-form-item>

        <el-form-item label="备注:">
          <el-input type="textarea" style="width: 100%;" size="mini" v-model="special.remark" auto-complete="off"
            placeholder="请输入备注"></el-input>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="closeDialog">取消</el-button>
        <el-button size="mini" type="primary" class="title" @click="submitForm()">保存</el-button>
      </div>
    </el-dialog>

  </div>
</template>
  
<script>

import { getInsurances, updateInsurance } from '@/api/XM'
import { upload } from '@/api/upload'
import { formatDate_RQ, formatDate } from '@/utils/time'
import { Loading } from 'element-ui';
// import VueEditor from 'vue-word-editor'
import 'quill/dist/quill.snow.css'
import { log } from 'console';
export default {
  components: {
    // VueEditor,

  },
  data() {
    return {

      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }],
      },
      dialogTitle: '新增文件',
      ewmShowgg: false,
      specialShow: false,
      specialShowgg: false,
      formList: {},
      data: [],
      pageSize: 10,
      value1: "",
      value2: [],
      input2: '',
      input3: '',
      input4: '',
      currentPage: 1,
      handleSelectionChangeList: [],
      bookUrllist: [],
      dialogVisibleImg: false,
      formData: {
        startTime: '',
        endTime: ""
      },
      Product: {
        typeId: '',
        name: '',
        linkUrl: '',
        fileType: '',
        fileName: ''
      },
      total: 1,
      valnum: 1,
      SEDJJXCX_LHFW_UserInformation_HT: {},
      baseUrl: process.env.VUE_APP_BASE_API + "/uploadCloud?filename=11111",
      baseUrl1: process.env.VUE_APP_BASE_API + "/oe_createDatum_.csp",
      //上传后的文件列表
      fileList: [],
      // 允许的文件类型
      fileType: ["pdf", "doc", "docx", "xls", "xlsx", "txt", "png", "jpg", "bmp", "jpeg"],
      // 运行上传文件大小，单位 M
      fileSize: 50,
      // 附件数量限制
      fileLimit: 1,
      //请求头
      headers: { "Content-Type": "multipart/form-data" },
     classify: [
        {
          id:0,name:'待确认'
        },
        {
          id:1,name:'已购买'
        },{
          id:2,name:'已停止'
        }

      ],
      special: {},
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },

      },
    }
  },
  mounted() {

  },
  created() {
    const SEDJJXCX_LHFW_UserInformation_HT = JSON.parse(decodeURIComponent(window.localStorage.getItem('SEDJJXCX_LHFW_UserInformation_HT')))
    this.SEDJJXCX_LHFW_UserInformation_HT = SEDJJXCX_LHFW_UserInformation_HT;
    this.input2 = this.$route.query.input21  ;
    if(this.input2){
      this.input3=this.classify.find(item=>item.id ==this.input2 ).name

    }
  this.getGoods();

    // this.fetchData();
  },
  methods: {
    
    // 时间点击事件
    handleDatePickerChange() {
      this.special.birthday = formatDate_RQ(this.special.birthday);

    },
    // 编辑、增加页面保存方法
    submitForm() {
      const params = {
        id: this.special.id,
        status: this.special.status,
        remark: this.special.remark,
        name: this.special.name,
        phone: this.special.phone,
        idCard: this.special.idCard,
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

      }
      if (this.value2 && this.value2[0]) {
        params.starTime = formatDate_RQ(this.value2[0])
        params.endTime = formatDate_RQ(this.value2[1])
      }

      updateInsurance(params).then(response => {
        if (response.data.code == '1') {
          this.specialShow = false
          this.special = {};
          this.value2=[]

          this.getGoods();
          this.currentPage=1
          this.$message({
            type: 'success',
            message: '修改成功！'

          })
        } else {
          this.$message({
            type: 'warning',
            message: '修改失败！'
          })
        }
      })


    },
    // 关闭
    closeDialog() {
      this.specialShow = false;
      this.value2=[];
    },
    change2() {
      const params = {
        status: this.input3,
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: 1
      }



      if (this.input4) {
        params.keyword = this.input4;
      }

      getInsurances(params).then(response => {
        this.total = response.data.totalNum;
        this.currentPage = 1;
        if (response.data.code === '1') {
          this.data = response.data.list
          // this.value2 = '';
          this.status = ''
        } else {
          this.data = response.data.list

          // this.value2 = '';
          this.input4 = ''
        }
      })
    },
    //上传文件之前
    beforeUpload(file) {
      this.Product.fileName = file.name;
      if (file.type != "" || file.type != null || file.type != undefined) {
        //截取文件的后缀，判断文件类型
        const FileExt = file.name.replace(/.+\./, "").toLowerCase();
        //计算文件的大小
        const isLt5M = file.size / 1024 / 1024 < 500; //这里做文件大小限制
        //如果大于50M
        if (!isLt5M) {
          this.$message.error('上传文件大小不能超过 500MB!')
          return false;
        }
        //如果文件类型不在允许上传的范围内
        if (this.fileType.includes(FileExt)) {
          return true;
        }
        else {
          this.$message.error("上传文件格式不正确!");
          return false;
        }
      }
    },
    //上传了的文件给移除的事件，由于我没有用到默认的展示，所以没有用到
    handleRemove() {
    },
    //这是我自定义的移除事件
    handleClose(i) {
      this.fileList.splice(i, 1);//删除上传的文件
      if (this.fileList.length == 0) {//如果删完了
        this.fileflag = true;//显示url必填的标识
        this.$set(this.rules.url, 0, { required: true, validator: this.validatorUrl, trigger: 'blur' })//然后动态的添加本地方法的校验规则
      }
    },
    //超出文件个数的回调
    handleExceed() {
      this.$message({
        type: 'warning',
        message: '超出最大上传文件数量的限制！'
      }); return
    },
    //上传文件的事件
    uploadFile(item) {
      this.loading = Loading.service({
        lock: true,
        text: '文件上传中........',
        // background: 'rgba(0, 0, 0, 0.7)',
        target: document.querySelector('#loadingDiv')
      })
      //上传文件的需要formdata类型;所以要转
      let FormDatas = new FormData()
      FormDatas.append('file', item.file);
      upload(FormDatas).then(res => {
        if (res.data.code == '0') {
          setTimeout(() => {
            this.loading.close()

          }, 2000);
          this.Product.linkUrl = res.data.yunUrl;
          this.Product.fileType = res.data.ext;
          // this.Product.filename=res.data.fileNameOld;
        }
      })

      // 	})
    },
    //上传成功后的回调
    handleSuccess() {

    },
    wangEditorChange(val) {
      this.Product.detail = val;
    },
    // 关闭
    formCancal1() {
      // this.Product.typeId='';
      // this.Product.name='';
      // this.Product.linkUrl='';
      // this.Product.fileType='';
      this.Product = {}
      // this.Product.filename='';
    },
    // 添加
    increase() {
      this.specialShow = true;
    },
    // 重新加载
    Reload() {
      this.value1 = '';
      this.value2 = '';
      this.input2 = '';
      this.input3 = '';
      this.input4 = '';
      this.input5 = '';
      this.getGoods(1);

    },
    fetchData(val = 1) {
      // const params = {
      //                       dbName: window.localStorage.getItem('JJHTDBnmame'),

      //   curPage: val
      // }
      // allDatumType(params).then(response => {
      //   if (response.data.code == '1') {
      //     this.classify = response.data.records
      //   }
      // })
    },

    getGoods(val = 1) {
      const params = {
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: val
      }
      if (this.value2 && this.value2[0] && this.value2[1]) {
        params.starTime = formatDate_RQ(this.value2[0]);
        params.endTime = formatDate_RQ(this.value2[1]);
      }

      if (this.input3) {
        params.catId = this.input3;
      }
      if (this.input4) {
        params.name = this.input4;
      }
      if (this.input2) {
        params.status = this.input2;
      }

      getInsurances(params).then(response => {
        this.total = response.data.totalNum;
        if (response.data.code == '1') {
          this.data = response.data.list
        } else {
          this.data = response.data.list

        }
      })
    },
    handleCurrentChange1(val) {
      this.valnum = val;
      this.getGoods(val)

    },
    handleCurrentChange(val) {
    },
    // 删除文件
    handleDelete(row) {
      this.$confirm('此操作将永久删除文件', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          const params = {
                                dbName: window.localStorage.getItem('JJHTDBnmame'),

            listid: row
          }
          DeleteGoods(params).then(response => {
            if (response.data.code == '1') {
              this.$message.success('删除成功')

              this.getGoods()
            }
          })
        })
        .catch((err) => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    handleSelectionChange(val) {
      this.handleSelectionChangeList = val;
    },

    handlePreview(file) {
      this.Product.coverPic = file.url
      this.dialogVisibleImg = true
    },
    handlePreview1(file) {
      this.specifications.url = file.url
      this.dialogVisibleImg = true
    },
    handleRemove1(file, fileList) {
      // 移除

    },




    //添加文件
    RechargeRole() {

      if (this.Product.catld == '') {
        this.$message({
          showClose: true,
          message: '请选择分类！',
          type: 'warning'
        });
        return
      }
      if (this.Product.name == '') {
        this.$message({
          showClose: true,
          message: '请输入文件名称！',
          type: 'warning'
        });
        return
      }

      if (this.Product.linkUrl == '') {
        this.$message({
          showClose: true,
          message: '请上传对应文件！',
          type: 'warning'
        });
        return
      }
      console.log(this.Product);
      var params = new URLSearchParams();
      params.append('name', this.Product.name);
      params.append('linkUrl', this.Product.linkUrl);
            params.append('dbName', window.localStorage.getItem('JJHTDBnmame'));

      params.append('typeId', this.Product.typeId);
      params.append('fileType', this.Product.fileType);
      params.append('fileName', this.Product.fileName);
      params.append('id', this.Product.id ? this.Product.id : '');
      this.axios.post(this.baseUrl1, params,).then(res => {
        if (res.data.code == '1') {
          this.$message({
            type: 'success',
            message: this.dialogTitle == '修改文件' ? '修改成功' : '添加成功'

          })
          this.getGoods(this.valnum)
          this.specialShowgg = false
          this.specialShow = false;
          this.Product = {}
          this.bookUrllist = []
          this.tableOption = [];
          this.input5 = '';
        } else {
          this.$message({
            type: 'warning',
            message: this.dialogTitle == '修改文件' ? '修改失败' : '添加失败'

          })
        }
      }).catch(err => {
      })
      return

    },
    // 修改文件
    handleEditzt(row) {
      this.dialogTitle = '修改文件';
      this.specialShow = true;
      // this.special = row;
      this.special = JSON.parse(JSON.stringify(row));
      this.value2.push(new Date(row.createTime))
      this.value2.push(new Date(row.endTime))

    },

    // 时间筛选
    change() {
      this.formData.startTime = formatDate_RQ(this.value2[0])
      this.formData.endTime = formatDate_RQ(this.value2[1])
      const params = {
        dbName: "mati", curPage: 1,
        starTime: this.formData.startTime,
        endTime: this.formData.endTime,
      }
      if (this.input2) {
        params.status = this.input2;
      }
      if (this.input3) {
        params.catId = this.input3;
      }
      if (this.input4) {
        params.name = this.input4;
      }
      getInsurances(params).then(response => {
        this.total = response.data.totalNum;
        this.currentPage = 1;
        if (response.data.code === '1') {
          this.formData.startTime = ''
          this.formData.endTime = ''
          this.data = response.data.records
          // this.value2 = '';
        } else {
          this.data = response.data.records

          // this.value2 = '';
        }
      })
    },

    // 名字筛选
    change3() {
      const params = {
        keyword: this.input4,
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: 1
      }
  

      getInsurances(params).then(response => {
        this.total = response.data.totalNum;
        this.currentPage = 1;
        if (response.data.code === '1') {
          this.data = response.data.list
          // this.value2 = '';
          // this.input4 = ''
        } else {
          this.data = response.data.list

          // this.value2 = '';
          // this.input4 = ''
        }
      })
    },
    // 批量导出
    // leading(){
    //   if (this.handleSelectionChangeList && this.handleSelectionChangeList.length) {
    //     let url = 'https://cdn.juesedao.cn/huiya/682fe11b0a2648d19f07e9d8b78279c2'
    //     const a = document.createElement('a')
    //     a.href = url
    //     a.download = '测试'// 下载后文件名
    //     a.style.display = 'none'
    //     document.body.appendChild(a)
    //     a.click() // 点击下载
    //     document.body.removeChild(a) // 下载完成移除元素

    //   } else {
    //     this.$message({
    //       type: 'warning',
    //       message: '请选择你导出的选项'
    //     })
    //   }
    // },



    handleCheck(index, row) {
      this.IMGindex = index;
    },

    change5() {
      if (this.input5) {
        this.tableOption.push({
          'label': this.input5
        })

        // this.tableData[0].attr_list = this.tableOption
        // return
        const params = {
          dbName: 'mati',
          attrGroup: this.input5
        }
        QueryAttrGroup(params).then(response => {
          if (response.data.code == '1') {
            this.attrGroup_ID.push(response.data.record.id);
            for (let i in this.tableData) {
              this.tableData[i].attr_list.push({
                attr_group_id: response.data.record.id,//表头id
                attr_group_name: this.input5,//表头
                attr_id: "",
                attr_name: ''
              })
            }

          }
        })
      } else {
        this.$message({
          type: 'warning',
          message: '请填写规格'
        })
      }

    },
    // change6(index, row) {
    //   const params = {
    //     dbName: 'mati',
    //     attrGroup: 5,
    //     attrName: row.specifications
    //   }
    //   QueryAttr(params).then(response => {
    //     if (response.data.code == '1') {
    //       this.tableData[index].attr_list.push({
    //         attr_id: response.data.record.id,
    //         attr_name: row.specifications,
    //       })
    //     }
    //   })
    // },
    change6(index, i) {
      console.log(this.attrGroup_ID);
      const params = {
        dbName: 'mati',
        attrGroup: this.attrGroup_ID[i], // this.attrGroupID,
        // attrGroup: this.attrGroupID, // this.attrGroupID,
        attrName: this.tableData[index].attr_list[i].attr_name
      }
      QueryAttr(params).then(response => {
        if (response.data.code == '1') {
          this.tableData[index].attr_list[i].attr_id = response.data.record.id;
        }
      })
    },






  },
};
</script>
  
<style lang="scss" scoped>
.el-row {
  margin-bottom: 20px;
}

.el-row:last-child {
  margin-bottom: 0;
}

.el-tag+.el-tag {
  margin-left: 10px;
}



.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}

.input-new-tag {
  width: 300px;
  margin-left: 10px;
  vertical-align: bottom;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.el-button--medium {
  margin-top: 10px;
  margin-left: 600px;
  height: 45px;
  width: 100px;
}

.el-input_ss {
  width: 200px;
}

.el-input_ss1 {

  margin-top: 6px;
}

.btn_jia {
  position: relative;
  margin-left: 8px;
  margin-top: 20px;
}

.btn_jia:hover {
  font-weight: 600;
  text-decoration: underline;
}

.btn_jia::after {
  content: "";
  width: 2px;
  height: 20px;
  background: #000;
  position: absolute;
  top: 10px;
  right: 33px;
}

.status_shop {
  margin-right: 8px;
}

// 分页
.paging {
  width: 100%;
  height: 100%;
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

// 功能按钮
.function_btn_box {
  width: 100%;
  display: flex;
  margin-right: 10px;
  margin-bottom: 20px;
}

.input-with-select {
  margin-left: 50px;
}

.float_rigth {
  float: right;
}

// .el-input.inp {
//   width: auto !important;
// }
</style>
  