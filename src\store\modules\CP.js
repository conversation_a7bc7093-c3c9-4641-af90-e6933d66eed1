import {queryAllSndysgd,approvalCoupon,getCouponById,cashCouponList,selectEntityOne,syncPicturesGD,createProductGD,changeConfig,changeType,changeScene,getqueryAllProduct,queryProductByIdGD,fileTypeCURD,storeAccountCURD,brandCRUD,strategyCURD,addcreateFinishBag,getfinishBagList,delCase,getcreateCase,getAllProduct,getcaseList,delCaseType,createCaseType, getqueryCaseTypes,delProduct,createProduct,getproductList,AddcreateProductSeries,getSeries,delProductType,  getproductTypeList,AddcreateProductType} from '@/api/CP'



const actions = {
  delCase({ commit }, data) {//登录接口
    return new Promise((resolve, reject) => {
      delCase(data).then(response => {
        resolve(response.data)
      }).catch(error => {
        console.log(error);
        reject(error)
      })
    })
  },
  getproductTypeList({ commit }, data) {//登录接口
    return new Promise((resolve, reject) => {
      getproductTypeList(data).then(response => {
        resolve(response.data)
      }).catch(error => {
        console.log(error);
        reject(error)
      })
    })
  },

  AddcreateProductType({ commit }, data) {//登录接口
    return new Promise((resolve, reject) => {
      AddcreateProductType(data).then(response => {
        resolve(response.data)
      }).catch(error => {
        console.log(error);
        reject(error)
      })
    })
  },
  delProductType({ commit }, data) {//登录接口
    return new Promise((resolve, reject) => {
      delProductType(data).then(response => {
        resolve(response.data)
      }).catch(error => {
        console.log(error);
        reject(error)
      })
    })
  },

  getSeries({ commit }, data) {//登录接口
    return new Promise((resolve, reject) => {
      getSeries(data).then(response => {
        resolve(response.data)
      }).catch(error => {
        console.log(error);
        reject(error)
      })
    })
  },AddcreateProductSeries({ commit }, data) {//登录接口
    return new Promise((resolve, reject) => {
      AddcreateProductSeries(data).then(response => {
        resolve(response.data)
      }).catch(error => {
        console.log(error);
        reject(error)
      })
    })
  },
  getproductList({ commit }, data) {//登录接口
    return new Promise((resolve, reject) => {
      getproductList(data).then(response => {
        resolve(response.data)
      }).catch(error => {
        console.log(error);
        reject(error)
      })
    })
  },
  createProduct({ commit }, data) {//登录接口
    return new Promise((resolve, reject) => {
      createProduct(data).then(response => {
        resolve(response.data)
      }).catch(error => {
        console.log(error);
        reject(error)
      })
    })
  },
  delProduct({ commit }, data) {//登录接口
    return new Promise((resolve, reject) => {
      delProduct(data).then(response => {
        resolve(response.data)
      }).catch(error => {
        console.log(error);
        reject(error)
      })
    })
  },
  getqueryCaseTypes({ commit }, data) {//登录接口
    return new Promise((resolve, reject) => {
      getqueryCaseTypes(data).then(response => {
        resolve(response.data)
      }).catch(error => {
        console.log(error);
        reject(error)
      })
    })
  },

  delCaseType({ commit }, data) {//登录接口
    return new Promise((resolve, reject) => {
      delCaseType(data).then(response => {
        resolve(response.data)
      }).catch(error => {
        console.log(error);
        reject(error)
      })
    })
  },

  createCaseType({ commit }, data) {//登录接口
    return new Promise((resolve, reject) => {
      createCaseType(data).then(response => {
        resolve(response.data)
      }).catch(error => {
        console.log(error);
        reject(error)
      })
    })
  },
  getcaseList({ commit }, data) {//登录接口
    return new Promise((resolve, reject) => {
      getcaseList(data).then(response => {
        resolve(response.data)
      }).catch(error => {
        console.log(error);
        reject(error)
      })
    })
  },  getAllProduct({ commit }, data) {//登录接口
    return new Promise((resolve, reject) => {
      getAllProduct(data).then(response => {
        resolve(response.data)
      }).catch(error => {
        console.log(error);
        reject(error)
      })
    })
  },

  getcreateCase({ commit }, data) {//登录接口
    return new Promise((resolve, reject) => {
      getcreateCase(data).then(response => {
        resolve(response.data)
      }).catch(error => {
        console.log(error);
        reject(error)
      })
    })
  },
  getfinishBagList({ commit }, data) {//登录接口
    return new Promise((resolve, reject) => {
      getfinishBagList(data).then(response => {
        resolve(response.data)
      }).catch(error => {
        console.log(error);
        reject(error)
      })
    })
  },

  addcreateFinishBag({ commit }, data) {//登录接口
    return new Promise((resolve, reject) => {
      addcreateFinishBag(data).then(response => {
        resolve(response.data)
      }).catch(error => {
        console.log(error);
        reject(error)
      })
    })
  },
  strategyCURD({ commit }, data) {//登录接口
    return new Promise((resolve, reject) => {
      strategyCURD(data).then(response => {
        resolve(response.data)
      }).catch(error => {
        console.log(error);
        reject(error)
      })
    })
  },
  brandCRUD({ commit }, data) {//登录接口
    return new Promise((resolve, reject) => {
      brandCRUD(data).then(response => {
        resolve(response.data)
      }).catch(error => {
        console.log(error);
        reject(error)
      })
    })
  },
  storeAccountCURD({ commit }, data) {//登录接口
    return new Promise((resolve, reject) => {
      storeAccountCURD(data).then(response => {
        resolve(response.data)
      }).catch(error => {
        console.log(error);
        reject(error)
      })
    })
  },
  fileTypeCURD({ commit }, data) {//登录接口
    return new Promise((resolve, reject) => {
      fileTypeCURD(data).then(response => {
        resolve(response.data)
      }).catch(error => {
        console.log(error);
        reject(error)
      })
    })
  },
  queryProductByIdGD({ commit }, data) {//登录接口
    return new Promise((resolve, reject) => {
      queryProductByIdGD(data).then(response => {
        resolve(response.data)
      }).catch(error => {
        console.log(error);
        reject(error)
      })
    })
  },getqueryAllProduct({ commit }, data) {//登录接口
    return new Promise((resolve, reject) => {
      getqueryAllProduct(data).then(response => {
        resolve(response.data)
      }).catch(error => {
        console.log(error);
        reject(error)
      })
    })
  },
  changeScene({ commit }, data) {//登录接口
    return new Promise((resolve, reject) => {
      changeScene(data).then(response => {
        resolve(response.data)
      }).catch(error => {
        console.log(error);
        reject(error)
      })
    })
  },
  changeConfig({ commit }, data) {//登录接口
    return new Promise((resolve, reject) => {
      changeConfig(data).then(response => {
        resolve(response.data)
      }).catch(error => {
        console.log(error);
        reject(error)
      })
    })
  }, createProductGD({ commit }, data) {//登录接口
    return new Promise((resolve, reject) => {
      createProductGD(data).then(response => {
        resolve(response.data)
      }).catch(error => {
        console.log(error);
        reject(error)
      })
    })
  },
  syncPicturesGD({ commit }, data) {//登录接口
    return new Promise((resolve, reject) => {
      syncPicturesGD(data).then(response => {
        resolve(response.data)
      }).catch(error => {
        console.log(error);
        reject(error)
      })
    })
  },
  selectEntityOne({ commit }, data) {//登录接口
    return new Promise((resolve, reject) => {
      selectEntityOne(data).then(response => {
        resolve(response.data)
      }).catch(error => {
        console.log(error);
        reject(error)
      })
    })
  },
  cashCouponList({ commit }, data) {//登录接口
    return new Promise((resolve, reject) => {
      cashCouponList(data).then(response => {
        resolve(response.data)
      }).catch(error => {
        console.log(error);
        reject(error)
      })
    })
  },
  getCouponById({ commit }, data) {//登录接口
    return new Promise((resolve, reject) => {
      getCouponById(data).then(response => {
        resolve(response.data)
      }).catch(error => {
        console.log(error);
        reject(error)
      })
    })
  },
  approvalCoupon({ commit }, data) {//登录接口
    return new Promise((resolve, reject) => {
      approvalCoupon(data).then(response => {
        resolve(response.data)
      }).catch(error => {
        console.log(error);
        reject(error)
      })
    })
  },
  queryAllSndysgd({ commit }, data) {//登录接口
    return new Promise((resolve, reject) => {
      queryAllSndysgd(data).then(response => {
        resolve(response.data)
      }).catch(error => {
        console.log(error);
        reject(error)
      })
    })
  },
}

export default {
  namespaced: true,
  actions
}

