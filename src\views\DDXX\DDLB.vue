<template>
  <div class="app-container">
    <el-form :inline="true" class="user-search">
      <el-form-item label="下单时间">
        <el-date-picker size="small" v-model="value2" type="datetimerange" :picker-options="pickerOptions"
          range-separator="至" start-placeholder="开始日期" @change="change" end-placeholder="结束日期" align="right">
        </el-date-picker>
      </el-form-item>


    </el-form>
    <el-form :inline="true" class="user-search">
      <el-form-item>
        <el-form-item label="">
          <el-select size="small" v-model="role" placeholder="订单号">
            <el-option label="用户名" value="1"></el-option>
            <el-option label="收件手机号" value="2"></el-option>
            <el-option label="收件人" value="3"></el-option>
            <el-option label="订单号" value="4"></el-option>
          </el-select>
        </el-form-item>
        <el-input size="small" v-model="search" placeholder="请输入" class="el-input_ss1">
          <el-button type="primary" slot="append" @click="searchss">搜索</el-button>
        </el-input>
        <el-button size="small" @click="leading" type="warning" style="margin-left:10px">批量导出</el-button>
                <el-button size="small" type="info" @click="Reload" icon="el-icon-refresh-left">重新加载</el-button>


        <a ref="basicRef" download="测试" style="margin:0 10px"></a>
        <!-- <el-button @click="search">清空回收站</el-button> -->
        <!-- 
        <el-input placeholder="请输入内容" v-model="input3" class="input-with-select">
          <el-button slot="append" icon="el-icon-search"></el-button>
        </el-input> -->
      </el-form-item>
    </el-form>
    <div class="Operationclass">
      <div :class="this.Operationclasstype == '0' ? 'Operationclassxz' : ''" @click="Operationclass(0)">全部</div>
      <div :class="this.Operationclasstype == '1' ? 'Operationclassxz' : ''" @click="Operationclass(1)">未付款</div>
      <div :class="this.Operationclasstype == '2' ? 'Operationclassxz' : ''" @click="Operationclass(2)">待发货</div>
      <div :class="this.Operationclasstype == '3' ? 'Operationclassxz' : ''" @click="Operationclass(3)">待收货</div>
      <div :class="this.Operationclasstype == '4' ? 'Operationclassxz' : ''" @click="Operationclass(4)">已完成</div>
      <div :class="this.Operationclasstype == '5' ? 'Operationclassxz' : ''" @click="Operationclass(5)">待处理</div>
      <div :class="this.Operationclasstype == '6' ? 'Operationclassxz' : ''" @click="Operationclass(6)">已取消</div>
      <div :class="this.Operationclasstype == '7' ? 'Operationclassxz' : ''" @click="Operationclass(7)">回收站</div>
    </div>
    <!-- 商品列表 -->
    <template>

      <el-row class="el-col_border">
        <el-col :span="11" style="font-weight: 600;">商品信息</el-col>
        <el-col :span="5" style="font-weight: 600;">金额</el-col>
        <el-col :span="2" style="font-weight: 600;">实际付款</el-col>
        <el-col :span="4" style="font-weight: 600;">订单状态</el-col>
        <el-col :span="1" style="font-weight: 600;">操作</el-col>
      </el-row>
      <div v-for="(item, index) of data" v-if="data && data.length">
        <div class="border">

          <el-row>
            <el-col class="el-col_border" :span="24" style="display: flex;font-size: 8px; align-items: center;">
              <div class="time">下单时间：{{ item.createTime }}</div>
              <div class="status">
                <!-- item.isCancel !=1 -->
                <el-tag type="danger" v-if="item.applyDelete == '1'"
                  style="margin-right: 10px;font-weight: 600;">申请取消</el-tag>
                <el-tag type="warning" v-if="item.isCancel == '1'"
                  style="margin-right: 10px;font-weight: 600;">已取消</el-tag>
                <el-tag type="success" v-if="item.isPay == '1' && item.isCancel == '0'"
                  style="margin-right: 10px;font-weight: 600;">已支付</el-tag>
                <el-tag type="info" v-if="item.isPay == '0' && item.isCancel == '0'"
                  style="margin-right: 10px;font-weight: 600;">未支付</el-tag>
                <el-tag type="primary" v-if="item.isSend == '1' && item.isCancel == '0'"
                  style="margin-right: 10px;font-weight: 600;">已发货</el-tag>
                <el-tag v-if="item.isSend == '0' && item.isCancel == '0'"
                  style="margin-right: 10px;font-weight: 600;">未发货</el-tag>
              </div>
              <div class="number">订单号：{{ item.orderNo }}</div>
              <div class="user" style="margin-left:10px">用户名：{{ item.nickname }}</div>
              <!-- <div class="terrace">
                <el-tag v-if="item.payType == '1'" type="success" style="margin-left: 10px">微信</el-tag>
                <el-tag v-if="item.payType == '2'" type="success" style="margin-left: 10px">支付宝</el-tag>
                <el-tag v-if="item.payType == '3'" type="success" style="margin-left: 10px">积分</el-tag>
              </div> -->
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <div class="commodity" style="cursor:pointer;" v-for="(arr, index) of item.detailList"
                @click="Jumptoproduct(arr)">
                <div class="commodity_left">
                  <div class="img">
                    <img style="width: 5.5rem;height: 5.5rem" :src="arr.pic" alt="">
                  </div>
                </div>
                <div class="commodity_rigth">
                  <div class="name">{{ arr.goods_name }}</div>
                  <div class="gg" v-for="(err, index) of arr.attr">
                    {{err.attr_group_name}}： <span style="margin-right:10px">{{ err.attr_name }}</span> <div v-if="index == 0" style="display: inline-block">数量： <span v-if="index == 0"> {{ arr.num }}件</span></div>
                  </div>
                  <div class="xj">
                    小计： <span>{{ arr.totalPrice }}元</span>
                  </div>
                </div>
              </div>

            </el-col>
            <el-col :span="16">
              <el-row>
                <el-col :span="10">
                  <div class="amount">总金额：<span style="color: blue">{{ item.totalPrice }}元</span>
                    (运费：{{ item.expressPrice }}元)</div>
                </el-col>
                <el-col :span="4">
                  <div class="reality">
                    <span style="color: blue">{{ item.payPrice }}</span>元
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="status1">
                    <div class="zffs">
                      支付方式:<el-tag v-if="item.payType == '1'" type="success" style="margin-left: 10px">微信</el-tag>
                      <el-tag v-if="item.payType == '2'" type="success" style="margin-left: 10px">支付宝</el-tag>
                      <el-tag v-if="item.payType == '3'" type="success" style="margin-left: 10px">余额支付</el-tag>
                    </div>
                    <div style="margin-left: 60px;">发货方式：<el-tag type="warning">快递发送</el-tag></div>
                  </div>
                </el-col>
                <el-col :span="4">
                  <div class="operate">
                    <div class="fh"></div>

                    <div style=" text-align: center;align-items: center; margin:10px 0">
                      <el-button v-if="item.isSend == '0' && item.isCancel == '0'" size="mini" type="primary"
                        @click="Addlogistics(index)">发货</el-button>
                      <!-- <el-button size="mini" type="primary">取消</el-button> -->
                    </div>
                    <div style="display: flex;align-items: center; margin:10px 0 10px 36px">
                      <el-button size="mini" type="primary" @click="Addcommodityinformation(index)">详情</el-button>
                    </div>
                    <div v-if="item.isSend == '1' && item.isCancel == '0'"
                      style="display: flex;align-items: center; margin:10px 0 10px 36px">
                      <el-button size="mini" type="primary" @click="Modifylogistics(index)">快递</el-button>
                    </div>
                    <div style="display: flex;align-items: center; margin:10px 0 10px 26px">
                      <el-button size="mini" type="primary" @click="Addcomments(index)">添加备注</el-button>
                    </div>
                    <div style="display: flex;align-items: center; margin:10px 0 10px 26px">
                      <el-button
                        v-if="item.isPay == '1' && item.isSend == '0' && item.isCancel != 1 || item.applyDelete == '1'"
                        size="mini" type="primary" @click="cancellationorder(index)">取消订单</el-button>
                    </div>
                    <!-- <div style="margin-left: 18px;"> <el-button size="mini" type="danger" disabled>移入回收站</el-button></div> -->
                  </div>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
          <el-row class="el-col_border">
            <el-col :span="24">
              <div class="consignee">
                收货人：<span>{{ item.name }}</span>
                电话:<span>{{ item.mobile }} </span>
                地址:<span>{{ item.address }}</span>
                <el-button style="margin-left: 20px;" size="mini" type="primary"
                  @click="Reviseaddress(index)">修改地址</el-button>
                <el-button v-if="item.express" style="margin-left: 20px;" size="mini"
                  type="info">{{ item.express }}</el-button>
                <!-- <span style="margin-left: 20px;">快递单号</span> -->
                <span><el-link type="primary" v-if="item.expressNo" style="margin-left: 20px;"
                    @click="Jump(item.expressNo)">{{ item.expressNo }}</el-link></span>
              </div>
            </el-col>
          </el-row>
        </div>

      </div>

      <el-row v-if="data && data.length == 0">
        <el-col>
          <div class="coner">暂无数据！</div>

        </el-col>
      </el-row>
      <!-- 分页 -->
      <el-row>
        <el-pagination :current-page.sync="currentPage" background style="margin-top:30px" :page-size="pageSize"
          layout="total,prev,pager,next,jumper" :total="integratelNum" @current-change="handleCurrentChangeintegrate" />
      </el-row>
    </template>
    <!-- 添加备注 -->
    <el-dialog width="26%" title="备注" :visible.sync="comments">
      <div>添加备注：</div>
      <textarea style="resize:none; border: 2px solid #f7f7f7;margin-top: 10px;" cols="52" rows="8"
        v-model="commentsValue"></textarea>
      <div slot="footer" class="dialog-footer">
        <el-button @click="formCancal">取 消</el-button>
        <el-button type="primary" @click="definecomments(1)">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 物流信息 -->
    <el-dialog width="26%" :title="wlxx" :visible.sync="Logisticsinformation.logistics">
      <el-form ref="Product.isNegotiable">
        <el-form-item label="物流选择:">
          <el-radio-group v-model="Logisticsinformation.radio">
            <el-radio :label="1">快递</el-radio>
            <el-radio :label="0">无需物流</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <el-form ref="editForm" v-if="Logisticsinformation.radio == '1'">
        <el-form-item label="快递公司:" prop="Product.catld">
          <el-select v-model="Logisticsinformation.corporation" placeholder="请输入快递公司">
            <el-option size="small" v-for="item in Logisticsinformation.delivery" :key="item.id" :label="item.name"
              :value="item.name"></el-option>
          </el-select>
        </el-form-item>

      </el-form>
      <el-form ref="editForm" v-if="Logisticsinformation.radio == '1'">
        <el-form-item label="快递单号:" prop="Product.name">
          <el-input v-model="Logisticsinformation.numbers" size="small" auto-complete="off"
            placeholder="请输入快递单号"></el-input>
        </el-form-item>
      </el-form>
      <el-form ref="editForm">
        <el-form-item label="留言:" prop="Logisticsinformation.departure">
          <el-input type="textarea" v-model="Logisticsinformation.departure"></el-input>
         
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="formCancal">取 消</el-button>
        <el-button type="primary" @click="definelogistics('form')">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 详情 -->
    <el-dialog width="60%" title="详情" :visible.sync="commodityinformation.informationshow">
      <el-row :gutter="20">
        <el-col :span="12">
          <table border="1" style=" border-color:#eeeeee;border-collapse:collapse;width:100%;text-align: center;">
            <tr>
              <td colspan="6">订单状态</td>
            </tr>
            <tr>
              <td class="tr_right">订单号</td>
              <td class="tr_left">{{ this.commodityinformation.information.orderNo }}</td>
            </tr>
            <tr>
              <td class="tr_right">用户</td>
              <td class="tr_left">{{ this.commodityinformation.information.nickname }} </td>
            </tr>
            <tr>
              <td class="tr_right">支付方式</td>
              <td class="tr_left">
                <el-tag v-if="this.commodityinformation.information.payType == '1'" type="success"
                  style="margin-left: 10px">微信</el-tag>
                <el-tag v-if="this.commodityinformation.information.payType == '2'" type="success"
                  style="margin-left: 10px">支付宝</el-tag>
                <el-tag v-if="this.commodityinformation.information.payType == '3'" type="success"
                  style="margin-left: 10px">余额支付</el-tag>
              </td>
            </tr>

            <tr>
              <td class="tr_right">支付状态</td>
              <td class="tr_left">
                <el-tag v-if="this.commodityinformation.information.isPay == '0'" style="margin-left: 10px">已下单</el-tag>
                <el-tag v-if="this.commodityinformation.information.isPay == '1'" style="margin-left: 10px">已付款</el-tag>
              </td>
            </tr>
            <tr>
              <td class="tr_right">发货状态</td>
              <td class="tr_left">

                <el-tag v-if="this.commodityinformation.information.isSend == '0'" type="warning"
                  style="margin-left: 10px">未发货</el-tag>
                <el-tag v-if="this.commodityinformation.information.isSend == '1'" type="success"
                  style="margin-left: 10px">已发货</el-tag>
              </td>
            </tr>
            <tr>
              <td class="tr_right">收货状态</td>
              <td class="tr_left">

                <el-tag v-if="this.commodityinformation.information.isConfirm == '0'" type="danger"
                  style="margin-left: 10px">未确认收货</el-tag>
                <el-tag v-if="this.commodityinformation.information.isConfirm == '1'" type="success"
                  style="margin-left: 10px">已确认收货</el-tag>
              </td>
            </tr>
            <tr>
              <td class="tr_right">收货信息</td>
              <td class="tr_left">
                <div>收货人:{{ this.commodityinformation.information.name }}</div>
                <div>电话:{{ this.commodityinformation.information.mobile }}</div>
                <div>收货地址: {{ this.commodityinformation.information.address }}</div>
              </td>
            </tr>
            <tr>
              <td class="tr_right">快递信息</td>
              <td class="tr_left">
                <div>快递公司:<el-tag type="info">{{ this.commodityinformation.information.express
                  ? this.commodityinformation.information.express : '空' }}</el-tag></div>
                <div>快递单号:{{ this.commodityinformation.information.expressNo ?
                  this.commodityinformation.information.expressNo : '空' }}</div>
              </td>
            </tr>
            <tr>
              <td colspan="6">订单金额</td>

            </tr>
            <tr>
              <td class="tr_right">
                <div>总金额</div>
                <div>(含运费)</div>
              </td>
              <td class="tr_left">{{ this.commodityinformation.information.totalPrice ?
                this.commodityinformation.information.totalPrice : 0 }}</td>
            </tr>
            <tr>
              <td class="tr_right">运费</td>
              <td class="tr_left">{{ this.commodityinformation.information.expressPrice ?
                this.commodityinformation.information.expressPrice : 0 }}</td>
            </tr>
          </table>
        </el-col>
        <el-col :span="12">
          <table border="1" style=" border-color:#eeeeee;border-collapse:collapse;width: 100%;text-align: center;">
            <tr>
              <td colspan="6">商品信息</td>
            </tr>
            <template v-for="item in this.commodityinformation.information.detailList">
              <tr class="tr_left1">
                <td rowspan="4" class="tr_right ">商品1</td>
                <td colspan="2">商品名</td>
                <td>{{ item.goods_name }}</td>
              </tr>

              <tr class="tr_left1" v-for="arr in item.attr">
                <td colspan="2">{{arr.attr_group_name}}</td>
                <td colspan="3">{{ arr.attr_name }}</td>
              </tr>
              <tr class="tr_left1">
                <td colspan="2">数量</td>
                <td colspan="3">{{ item.num }}件</td>
              </tr>
              <tr class="tr_left1">
                <td colspan="2">小计</td>
                <td colspan="3">{{ item.totalPrice }}</td>
              </tr>
            </template>
            <tr class="tr_left">
              <td>买家留言</td>
              <td colspan="4">{{ this.commodityinformation.information.words }}</td>
            </tr>
            <tr>
              <td>商家备注</td>
              <td colspan="4">
                <textarea style="resize:none;margin-top: 10px;" cols="52" rows="8" v-model="commentsValue"></textarea>
              </td>
            </tr>
            <tr>
              <td colspan="6">
                <div>
                  <el-button type="success" plain @click="definecomments()">确定</el-button>
                  <el-button type="info" plain @click="formCancal">返回</el-button>
                </div>
              </td>
            </tr>
          </table>
        </el-col>
      </el-row>
    </el-dialog>
    <!-- 修改收货地址 -->
    <el-dialog width="60%" title="修改收货地址" :visible.sync="user.shdzShow">

      <el-form label-width="150px" ref="user.name">
        <el-form-item label="收货人姓名:">
          <el-input v-model="user.name" size="small" auto-complete="off" placeholder="请输入用户名"></el-input>
        </el-form-item>

      </el-form>
      <el-form label-width="150px" ref="user.mobile">
        <el-form-item label="电话:">
          <el-input v-model="user.mobile" size="small" auto-complete="off" placeholder="请输入电话"></el-input>
        </el-form-item>

      </el-form>
      <el-form label-width="150px" ref="user.name">
        <el-form-item label="发件人地区:">
          <el-cascader size="large" :options="options" v-model="selectedOptions" @change="handleChange">
          </el-cascader> </el-form-item>

      </el-form>

      <el-form label-width="150px" ref="user.addressData.detail">
        <el-form-item label="详细地址:">
          <el-input v-model="user.addressData.detail" size="small" auto-complete="off" placeholder="请输入详细地址"></el-input>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="ReviseaddressData('form')">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 添加商品 -->
    <el-dialog @close="formCancal1" width="50%" :title="dialogTitle" :visible.sync="specialShow"
      :close-on-click-modal="false" :close-on-press-escape="false">
      <el-form label-width="150px" ref="editForm">

        <el-form-item label="商品分类:" prop="Product.catId">
          <el-input disabled="true" v-model="Product.catId" size="small" auto-complete="off"
            placeholder="请输入商品名称"></el-input>
        </el-form-item>
      </el-form>
      <el-form label-width="150px" ref="editForm">
        <el-form-item label="商品名称:" prop="Product.name">
          <el-input disabled="true" v-model="Product.name" size="small" auto-complete="off"
            placeholder="请输入商品名称"></el-input>
        </el-form-item>
      </el-form>
      <el-form label-width="150px" ref="editForm">
        <el-form-item label="商品副标题:" prop="Product.subTitle">
          <el-input disabled="true" v-model="Product.subTitle" size="small" auto-complete="off"
            placeholder="请输入副标题"></el-input>
        </el-form-item>
      </el-form>
      <el-form label-width="150px" ref="editForm">
        <el-form-item label="单位:" prop="Product.unit">
          <el-input disabled="true" v-model="Product.unit" size="small" auto-complete="off" placeholder="件"></el-input>
        </el-form-item>
      </el-form>
      <el-form label-width="150px" ref="editForm">
        <el-form-item label="商品排序:" prop="Product.sort">
          <template slot-scope="scope">
            <el-input disabled="true" v-model="Product.sort" size="small" auto-complete="off"
              placeholder="请输入序列号"></el-input>
            <div style="font-size: 12px">排序按升序排列</div>
          </template>
        </el-form-item>
      </el-form>
      <el-form label-width="150px" ref="editForm">
        <el-form-item label="已出售量:" prop="Product.virtualSales">
          <template slot-scope="scope">
            <el-input disabled="true" v-model="Product.virtualSales" size="small" auto-complete="off"
              placeholder="请输入已出售量"></el-input>
            <div style="font-size: 12px">前端展示的销量=实际销量+已出售量</div>
          </template>
        </el-form-item>
      </el-form>
      <el-form label-width="150px" ref="editForm">
        <el-form-item label="限购数量:" prop="Product.confineCount">
          <template slot-scope="scope">
            <el-input disabled="true" v-model="Product.confineCount" size="small" auto-complete="off"
              placeholder="请输入限购数量"></el-input>
            <div style="font-size: 12px">设置为0则不限购，大于0则等于对应的限购数量</div>
          </template>
        </el-form-item>
      </el-form>
      <el-form label-width="150px" ref="editForm">
        <el-form-item label="重量:" prop="Product.weight">
          <template slot-scope="scope">
            <el-input disabled="true" v-model="Product.weight" size="small" auto-complete="off"
              placeholder="请输入商品重量"></el-input>
          </template>
        </el-form-item>
      </el-form>
      <el-form label-width="150px" ref="editForm">
        <el-form-item label="商品图片:" prop="Product.coverPic">
          <el-upload disabled="true" action="#" list-type="picture-card" :on-preview="handlePreview"
            :on-remove="handleRemove1" :auto-upload="false" :file-list="bookUrllist" multiple
            :on-change="handleSmallPicSuccess">
            <i class="el-icon-plus" />
          </el-upload>
          <el-dialog :visible.sync="dialogVisibleImg" top="0" center :modal="false">
            <img width="100%" :src="Product.coverPic" alt>
          </el-dialog>
        </el-form-item>
      </el-form>
      <el-form label-width="150px" ref="editForm">
        <el-form-item label="售价:" prop="Product.price">
          <template slot-scope="scope">
            <el-input disabled="true" v-model="Product.price" size="small" auto-complete="off"
              placeholder="请输入售价"></el-input>
          </template>
        </el-form-item>
      </el-form>
      <el-form label-width="150px" ref="editForm">
        <el-form-item label="成本价:" prop="Product.costPrice">
          <template slot-scope="scope">
            <el-input disabled="true" v-model="Product.costPrice" size="small" auto-complete="off"
              placeholder="请输入成本价"></el-input>
          </template>
        </el-form-item>
      </el-form>
      <el-form label-width="150px" ref="editForm">
        <el-form-item label="原价:" prop="Product.originalPrice">
          <template slot-scope="scope">
            <el-input disabled="true" v-model="Product.originalPrice" size="small" auto-complete="off"
              placeholder="请输入原价"></el-input>
          </template>
        </el-form-item>
      </el-form>
      <el-form label-width="150px" ref="editForm">
        <el-form-item label="服务内容:" prop="Product.service">
          <template slot-scope="scope">
            <el-input disabled="true" v-model="Product.service" size="small" auto-complete="off"
              placeholder="请输入服务内容"></el-input>
            <div style="font-size: 12px">例子：正品保障,极速发货,7天退换货。多个请使用英文逗号,分隔</div>
          </template>
        </el-form-item>
      </el-form>

      <el-form label-width="150px" ref="Product.hotCakes">
        <el-form-item label="是否新品:">
          <el-radio-group v-model="Product.hotCakes" disabled="true">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <el-form label-width="150px" ref="Product.goodsNum">
        <el-form-item label="商品库存:" prop="Product.goodsNum">
          <template slot-scope="scope">
            <el-input disabled="true" v-model="Product.goodsNum" size="small" auto-complete="off"
              placeholder="请输入库存"></el-input>
          </template>
        </el-form-item>
      </el-form>
      <el-form label-width="150px" ref="Product.useAttr">
        <el-form-item label="是否使用规格:">
          <template slot-scope="scope">
            <el-table @click="formCancal" border ref="singleTable" :data="tableData" highlight-current-row
              @current-change="handleCurrentChange" style="width: 100%">
              <el-table-column width="200" v-for="(item, index) in tableOption" :key="index" :label="item.label"
                align="center">
                <template slot-scope="scope">
                  <el-input disabled="true" @blur="change6(scope.$index, index)" clearable
                    v-model="scope.row.attr_list[index].attr_name" placeholder="请输入"></el-input>
                </template>
              </el-table-column>
              <!-- <el-table-column align="center" property="specifications" label="规格" width="120">
            <template slot-scope="scope">
              <el-input @blur="change6(scope.$index, scope.row)" v-model="scope.row.specifications"
                placeholder="请输入规格"></el-input>
            </template>
          </el-table-column> -->
              <el-table-column align="center" property="num" label="库存">
                <template slot-scope="scope">
                  <el-input disabled="true" clearable v-model="scope.row.num" placeholder="请输入库存"></el-input>
                </template>
              </el-table-column>
              <el-table-column align="center" property="price" label="价格">
                <template slot-scope="scope">
                  <el-input disabled="true" clearable v-model="scope.row.price" placeholder="请输入价格"></el-input>
                </template>
              </el-table-column>
              <el-table-column align="center" property="no" label="货号">
                <template slot-scope="scope">
                  <el-input disabled="true" clearable v-model="scope.row.no" placeholder="请输入货号"></el-input>
                </template>
              </el-table-column>
              <el-table-column align="center" property="pic" label="规格图片" width="200">
                <template slot-scope="scope">
                  <div @click="handleCheck(scope.$index, scope.row)" style="display: flex"><el-input disabled="true"
                      class="el-input_ss" v-model="scope.row.pic" placeholder="请选择图片"></el-input>
                    <el-upload disabled="true" class="upload-demo" action="#" :on-preview="handlePreview1"
                      :on-remove="handleRemove1" :on-change="handleSmallPicSuccess1" multiple :limit="3">
                      <el-button disabled="true" icon="el-icon-upload"></el-button>
                    </el-upload>
                  </div>
                  　　　　<img v-if="scope.row.pic" :src="scope.row.pic" width="40" height="40" class="head_pic" />

                </template>
              </el-table-column>

            </el-table>
          </template>

        </el-form-item>
      </el-form>
      <el-form label-width="150px" ref="Product.integral">
        <el-form-item label="积分赠送:" prop="Product.integral">
          <template slot-scope="scope">
            <el-input disabled="true" v-model="Product.integral" size="small" auto-complete="off"
              placeholder="请输入积分"></el-input>
          </template>
        </el-form-item>
        <el-form-item label="图文详情:" prop="Product.detail">
          <!-- <VueEditor ref="vueEditor" :config="config" /> -->
          <template slot-scope="scope">
            <wangEditor disabled="notEditBasicInfo" v-model="wangEditorDetail" :isClear="isClear"
              @change="wangEditorChange"></wangEditor>

          </template>

        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="formCancal1">关闭</el-button>
        <!-- <el-button type="success" @click="formCancal2">保存</el-button> -->
        <!-- <el-button type="primary" @click="RechargeRole('form')">确 定</el-button> -->
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { IntergralOrder, AddRemark, OrderSend, UpdateOrderAddress, exportOrders, cancelOrder, goodsDetail } from '@/api/order';
import { formatDate_RQ } from '@/utils/time'
import { regionData, CodeToText, TextToCode } from 'element-china-area-data'
import wangEditor from "@/components/wangEditor/wangEditor.vue";
import { upload } from '@/api/upload'

import { callbackify, log } from 'util';
export default {
  components: {
    // VueEditor,
    wangEditor
  },
  data() {
    return {
      isClear: false,//设置为true的时候，这个可以用this.wangEditorDetail=''来替代
      wangEditorDetail: "",
      dialogTitle: '商品详情',
      classify: [],
      tableData: [],
      dialogVisibleImg: false,
      bookUrllist: [],
      specialShow: false,
      user: {
        shdzShow: false,
        name: '',
        mobile: '',
        address: '',
        id: '',
        Product: {
          catId: '',
          name: '',
          subTitle: '',
          unit: '',
          sort: '',
          virtualSales: '',
          confineCount: 1,
          weight: '',
          coverPic: '',
          price: '',
          costPrice: '',
          originalPrice: '',
          service: '',
          isNegotiable: '',
          goodsNum: '',
          useAttr: '',
          attr: '',
          integral: '',
          detail: [],
          hotCakes: 0
        },
        addressData: {
          province: '',
          city: '',
          district: '',
          detail: ''
        }
      },
      options: regionData,
      selectedOptions: ['140000', '140300', '140303'],
      //加载状态
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      data: [],
      attr: [],
      status: '',
      integratelNum: 1,
      pageSize: 5,
      value2: '',
      formData: {
        startTime: '',
        endTime: ""
      },
      search: '',
      role: '',
      comments: false,
      commentsValue: '',
      commentsindex: '',
      Operationclasstype: 0,
      tableOption: [],
      Logisticsinformation: {
        delivery: [
          { name: "极兔速递" },
          { name: "速尔快递" },
          { name: "D速物流" },
          { name: "宅急送" },
          { name: "中通快运" },
          { name: "国通快递" },
          { name: "顺丰速运" },
          { name: "百世快递" },
          { name: "中通快递" },
          { name: "申通快递" },
          { name: "圆通速递" },
          { name: "韵达速递" },
          { name: "龙邦快递" },
          { name: "联昊通速递" },
          { name: "民航快递" },
          { name: "邮政快递包裹" },
          { name: "EMS" },
          { name: "天天快递" },
          { name: "京东物流" },
          { name: "明亮物流" },
          { name: "全峰快递" },
          { name: "国通快递" },
          { name: "优速快递" },
          { name: "德邦" },
          { name: "快捷快递" },
          { name: "安捷快递" },
          { name: "能达速递" },
          { name: "平安达腾飞快递" },
          { name: "泛捷快递" },
          { name: "PCA Express" },
          { name: "全晨快递" },
          { name: "全日通快递" },
          { name: "快客快递" },
          { name: "义达国际物流" },
          { name: "越丰物流" },
          { name: "原飞航物流" },
          { name: "亚风快递" },
          { name: "运通快递" },
          { name: "亿翔快递" },
          { name: "增益快递" },
          { name: "汇强快递" },
          { name: "众通快递" },
          { name: "中铁快运" },
          { name: "中邮物流" },
          { name: "中铁物流" },
          { name: "亚马逊物流" },
          { name: "安能物流" },
          { name: "安信达快递" },
          { name: "澳邮专线" },
          { name: "百福东方" },
          { name: "北青小红帽" },
          { name: "百世快运" },
          { name: "CCES快递" },
          { name: "城市100" },
          { name: "城际快递" },
          { name: "CNPEX中邮快递" },
          { name: "COE东方快递" },
          { name: "长沙创一" },
          { name: "成都善途速运" },
          { name: "D速物流" },
          { name: "大田物流" },
          { name: "FEDEX联邦(国内件）" },
          { name: "FEDEX联邦(国际件）" },
          { name: "飞康达" },
          { name: "广东邮政" },
          { name: "共速达" },
          { name: "高铁速递" },
          { name: "汇丰物流" },
          { name: "恒路物流" },
          { name: "天地华宇" },
          { name: "鸿桥供应链" },
          { name: "海派通物流公司" },
          { name: "华强物流" },
          { name: "华夏龙物流" },
          { name: "好来运快递" },
          { name: "京广速递" },
          { name: "九曳供应链" },
          { name: "佳吉快运" },
          { name: "嘉里物流" },
          { name: "捷特快递" },
          { name: "急先达" },
          { name: "晋越快递" },
          { name: "加运美" },
          { name: "佳怡物流" },
          { name: "跨越物流" },
          { name: "如风达" },
          { name: "瑞丰速递" },
          { name: "赛澳递" },
          { name: "圣安物流" },
          { name: "盛邦物流" },
          { name: "盛丰物流" },
          { name: "上大物流" },
          { name: "盛辉物流" },
          { name: "速通物流" },
          { name: "速腾快递" },
          { name: "速必达物流" },
          { name: "速尔快递" },
          { name: "唐山申通" },
          { name: "全一快递" },
          { name: "UEQ Express" },
          { name: "万家物流" },
          { name: "万象物流" },
          { name: "新邦物流" },
          { name: "信丰物流" },
          { name: "希优特" },
          { name: "新杰物流" },
          { name: "源安达快递" },
          { name: "远成物流" },
          { name: "丰恒物流" },
          { name: "佳润达物流" },
          { name: "邮政包裹信件" },
          { name: "安能快递" },
          { name: "程光" },
          { name: "富腾达" },
          { name: "中通快运" },
        ],
        logistics: false,
        logisticsIndex: '',
        radio: 1,
        numbers: '',
        corporation: '',
        departure: ''
      },
      currentPage: 1,
      commodityinformation: {
        information: {},
        commodityIndex: '',
        informationshow: false,
      },
      Product: {
        catId: '',
        name: '',
        subTitle: '',
        unit: '',
        sort: '',
        virtualSales: '',
        confineCount: 1,
        weight: '',
        coverPic: '',
        price: '',
        costPrice: '',
        originalPrice: '',
        service: '',
        isNegotiable: '',
        goodsNum: '',
        useAttr: '',
        attr: '',
        integral: '',
        detail: [],
        hotCakes: 0
      },
      wlxx: '物流信息',
      yspage:1

    }
  },
  created() {
    this.Operationclasstype = this.$route.query.Operationclasstype ? this.$route.query.Operationclasstype : 0;
    this.Getuser(1, this.Operationclasstype);
  },
  mounted() {
    window.addEventListener("scroll", this.showbtn, true)
  },
  methods: {
    onEdintorFocus(event) {
      console.log(event);
      event.enable(false)

    },
    wangEditorChange(val) {
      this.Product.detail = val;
    },
    handleRemove1(file, fileList) {
      // 移除

    },
    handleSmallPicSuccess1(res, file, fileList) {
      if (file && file.length == 2) {
        file.shift()
      }
      this.uploadImg(res, 2);

    },
    handleSmallPicSuccess(res, file, fileList) {
      if (file && file.length == 2) {
        file.shift()
      }
      this.uploadImg(res, 1);

    },
    uploadImg(file, type) {
      let formData = new FormData()
      formData.append('file', file.raw)
      upload(formData).then(response => {
        if (type == '1') {
          this.Product.coverPic = response.data.yunUrl;

        } else {
          this.tableData[this.IMGindex].pic = response.data.yunUrl;

        }
      })

    },
    handleCurrentChange(val) {
      this.currentRow = val;
    },
    handlePreview(file) {
      this.Product.coverPic = file.url
      this.dialogVisibleImg = true
    },
    handlePreview1(file) {
      this.specifications.url = file.url
      this.dialogVisibleImg = true
    },
    formCancal1() {
      this.specialShow = false;
      this.Product = {};
      this.wangEditorDetail = '';
      this.bookUrllist = [];
      this.tableOption = []
      this.tableData = []
    },
    //跳转商品详情
    Jumptoproduct(e) {
      // this.$router.push({ path: '/personnel/audit',query:{goods_name:e.goods_name} })
      this.specialShow = true;

      const params = {
        dbName: "mati", goodsId: e.goodsId, userId: 1
      }


      goodsDetail(params).then(response => {
        if (response.data.code === '1') {
          this.wangEditorDetail = response.data.records.detail
          this.Product = response.data.records;
          this.Product.catId = response.data.records.catName;
          if (response.data.records.attr) {
            this.tableData = JSON.parse(response.data.records.attr);
          }
          let a = []
          for (let i in this.tableData) {
            for (let j in this.tableData[i].attr_list) {
              this.tableData[i].specifications = this.tableData[i].attr_list[j].attr_name;
              if (a.indexOf(this.tableData[i].attr_list[j].attr_group_name) == -1) {
                a.push(this.tableData[i].attr_list[j].attr_group_name)
              }
            }
          }
          for (let i in a) {
            let obj = {
              label: a[i]
            }
            this.tableOption.push(obj)
          }
          if (this.tableData && this.tableData[0].num || this.tableData[0].price) {
            this.Product.useAttr = true;
          }

          this.bookUrllist = [
            {
              url: response.data.records.coverPic
            }
          ]

        } else {
        }
      })
    },
    // 重新加载
    Reload() {
      this.Operationclasstype = 0;
      this.search = '';
      this.value2 = '';
      this.role = '';
      this.Getuser(1, 0);

    },
    //快递
    Jump(e) {
      // window.location.href='https://www.baidu.com/s?wd=' + e
      // window.open='https://www.baidu.com/s?wd=' + e
      window.open(`https://www.baidu.com/s?wd=${e}`, '_blank')
    },
    // 取消订单
    cancellationorder(index) {
      this.$confirm('您将取消订单, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const params = {
          userId: this.data[index].userId,
          dbName: 'mati',
          orderId: this.data[index].id,
          type: 1
        }; cancelOrder(params).then(response => {
          if (response.data.code == '1') {
            this.Getuser()
          }
        })
        this.$message({
          type: 'success',
          message: '取消成功!'
        });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    // 城市·
    handleChange(value) {
      console.log(value)
      var a = `${CodeToText[value[0]]}/${CodeToText[value[1]]}/${CodeToText[value[2]]
        }`
      this.user.addressData.province = a.split('/')[0]
      this.user.addressData.city = a.split('/')[1]
      this.user.addressData.district = a.split('/')[2]

    },
    // 修改地址
    Reviseaddress(index) {
      this.user.shdzShow = true;
      this.user.name = this.data[index].name;
      this.user.mobile = this.data[index].mobile;
      this.user.id = this.data[index].id;
      this.user.addressData.detail = this.data[index].address;
      let a = JSON.parse(this.data[index].addressData);
      if (TextToCode[a.province] && TextToCode[a.province].code && TextToCode[a.province][a.city] && TextToCode[a.province][a.city].code && TextToCode[a.province][a.city][a.district] && TextToCode[a.province][a.city][a.district].code) {
        this.selectedOptions = [
          TextToCode[a.province].code,
          TextToCode[a.province][a.city].code,
          TextToCode[a.province][a.city][a.district].code
        ];
      } else {
        this.selectedOptions = []
      }

      // this.selectedOptions = TextToCode[a.city]
      // this.selectedOptions=['150000','150500','150523']

    },
    ReviseaddressData() {
      const params = {
        dbName: "mati", name: this.user.name, mobile: this.user.mobile,
        address: this.user.addressData.detail,
        orderId: this.user.id,
        addressData: this.user.addressData
      }
      UpdateOrderAddress(params).then(response => {
        if (response.data.code === '1') {
          this.$message.success('修改成功')
          this.user.shdzShow = false;
          this.Getuser()

        }
      })
    },
    Operationclass(value) {
      this.Operationclasstype = value;
      this.currentPage = 1;
      this.Getuser(1, value);

    },
    //获取用户
    Getuser(val = 1, status) {
      const params = {
        dbName: "mati", curPage: val, status: status
      }
      if (this.role == '1') {
        params.nickname = this.search
      } else if (this.role == '2') {
        params.mobile = this.search

      } else if (this.role == '3') {
        params.name = this.search

      } else {
        if (this.search) {
          params.orderNo = this.search

        }

      }
      if (this.value2 && this.value2[0]) {
        params.starTime = formatDate_RQ(this.value2[0])
        params.endTime = formatDate_RQ(this.value2[1])
      }

      IntergralOrder(params).then(response => {
        this.integratelNum = response.data.totalNum;
        if (response.data.code === '1') {
          for (var i in response.data.list) {
            for (var j in response.data.list[i].detailList) {
              response.data.list[i].detailList[j].attr = JSON.parse(response.data.list[i].detailList[j].attr)
            }

          }
          this.data = response.data.list
        } else {
          this.data = response.data.list
        }
      })
    },
    handleCurrentChangeintegrate(val) {
      this.yspage=val;
      this.Getuser(val, this.Operationclasstype);
      var timer = setInterval(function () {
        let osTop = document.documentElement.scrollTop || document.body.scrollTop;
        let ispeed = Math.floor(-osTop / 5);
        document.documentElement.scrollTop = document.body.scrollTop = osTop + ispeed;
        this.isTop = true;
        if (osTop === 0) { clearInterval(timer) }
      }, 20);
    },
    // 时间筛选
    change() {
      this.formData.startTime = formatDate_RQ(this.value2[0])
      this.formData.endTime = formatDate_RQ(this.value2[1])
      const params = {
        dbName: "mati", curPage: 1,
        starTime: this.formData.startTime,
        endTime: this.formData.endTime,
      }
      if (this.role == '1') {
        params.nickname = this.search
      } else if (this.role == '2') {
        params.mobile = this.search

      } else if (this.role == '3') {
        params.name = this.search

      } else {
        params.orderNo = this.search

      }
      IntergralOrder(params).then(response => {
        this.integratelNum = response.data.totalNum;
        this.currentPage = 1;
        if (response.data.code === '1') {
          for (var i in response.data.list) {
            for (var j in response.data.list[i].detailList) {
              response.data.list[i].detailList[j].attr = JSON.parse(response.data.list[i].detailList[j].attr)
            }

          }
          this.data = response.data.list
        } else {
          this.data = response.data.list
        }
      })
    },
    abb(url) {
      return new Promise((resolve) => {
        const aa = new XMLHttpRequest();
        aa.open("GET", url, true);
        aa.responseType = "blob";
        aa.onload = () => {
          if (aa.status === 200) {
            resolve(aa.response);
          }
        };
        aa.send();
      });
    },
    //  批量导出
    leading() {


      if (this.value2[0]) {
        const params = {
          dbName: "mati", curPage: 1,
        }
        if (this.role == '1') {
          params.nickname = this.search
        } else if (this.role == '2') {
          params.mobile = this.search

        } else if (this.role == '3') {
          params.name = this.search

        } else {
          if (this.search) {
            params.orderNo = this.search

          }

        }
        if (this.value2 && this.value2[0]) {
          params.starTime = formatDate_RQ(this.value2[0])
          params.endTime = formatDate_RQ(this.value2[1])
        }

        exportOrders(params).then(response => {
          if (response.data.code === '1') {
            this.abb(response.data.url).then((blob) => {
              let fileName = '订单.xlsx'
              this.downloadFile(blob, fileName)

            })
          } else {
            // this.value2 = '';
          }
        })
        return


      } else {
        this.$message({
          type: 'warning',
          message: '请输入你导出的条件'
        })
      }
    },

    downloadFile(url, fileName) {
      const aLink = document.createElement("a");
      var blob = new Blob([url]);
      aLink.href = URL.createObjectURL(blob);
      aLink.setAttribute("download", fileName); // 
      document.body.appendChild(aLink);
      aLink.click();
      document.body.removeChild(aLink);
      window.URL.revokeObjectURL(aLink.href);

    },

    // 搜索筛选
    searchss() {
      const params = {
        dbName: "mati", curPage: 1,
      }
      if (this.role == '1') {
        params.nickname = this.search
      } else if (this.role == '2') {
        params.mobile = this.search

      } else if (this.role == '3') {
        params.name = this.search

      } else {
        params.orderNo = this.search

      }
      if (this.value2 && this.value2[0]) {
        params.starTime = formatDate_RQ(this.value2[0])
        params.endTime = formatDate_RQ(this.value2[1])
      }
      IntergralOrder(params).then(response => {
        this.integratelNum = response.data.totalNum;
        this.currentPage = 1;
        if (response.data.code === '1') {
          for (var i in response.data.list) {
            for (var j in response.data.list[i].detailList) {
              response.data.list[i].detailList[j].attr = JSON.parse(response.data.list[i].detailList[j].attr)
            }

          }
          this.data = response.data.list
        } else {
          this.data = response.data.list
        }
      })
    },

    formCancal() {
      this.comments = false;
      this.commodityinformation.informationshow = false;

    },
    // 添加备注
    Addcomments(row) {
      this.comments = true;
      this.commentsindex = row;
    },
    definecomments() {

      const params = {
        dbName: "mati", sellerComments: this.commentsValue, orderId: this.data[this.commentsindex].id
      }
      AddRemark(params).then(response => {
        if (response.data.code === '1') {
          this.comments = false;
          this.commodityinformation.informationshow = false;
          this.commentsValue = '';
          this.commentsindex = '';

          this.Getuser()
        }
      })
    },
    // 发货物流信息
    Addlogistics(index) {
      this.Logisticsinformation.logistics = true;
      this.Logisticsinformation.logisticsIndex = index;
    },
    definelogistics() {
      const params = {
        dbName: "mati",
        words: this.Logisticsinformation.departure,
        express: this.Logisticsinformation.corporation ? this.Logisticsinformation.corporation : '',
        expressNo: this.Logisticsinformation.numbers ? this.Logisticsinformation.numbers : '',
        orderId: this.data[this.Logisticsinformation.logisticsIndex].id
      }
      OrderSend(params).then(response => {
        if (response.data.code === '1') {
          this.Logisticsinformation.logistics = false;
          this.Logisticsinformation.departure = '';
          this.Logisticsinformation.corporation = '';
          this.Logisticsinformation.numbers = '';
          this.$message.success(response.data.msg)
          this.Getuser(this.yspage, this.Operationclasstype);
        }
      })
    },
    //详情
    Addcommodityinformation(index) {
      this.commodityinformation.informationshow = true;
      this.commodityinformation.information = this.data[index];
      this.commentsValue = this.data[index].sellerComments;
      this.commentsindex = index;
    },
    Modifylogistics(index) {
      this.wlxx = '修改物流信息'
      this.Logisticsinformation.logistics = true;
      this.Logisticsinformation.logisticsIndex = index;
      this.Logisticsinformation.numbers = this.data[index].orderNo;
      this.Logisticsinformation.radio = 1;
      this.Logisticsinformation.corporation = this.data[index].express;
      this.Logisticsinformation.departure = this.data[index].words;

    },
  }
}
</script>

<style scoped lang="scss">
.app-container {
  font-size: 10px;
}

.el-col_border {
  border: 1px solid #eee;
  padding: 10px;
}

.commodity {
  padding: 6px;
  display: flex;
  align-items: center;
  border: 1px solid #eee;
}

.amount,
.reality {
  // height: 260px;
  // line-height: 260px;
  margin-left: 20px;
  text-align: center;

}

tr,
tb {
  height: 50px;
}

.consignee {
  color: #888888;
  font-size: 12px;

}

.consignee span {
  color: #000;
  font-size: 12px;
}

.status1 .zffs {
  margin-left: 60px;
  margin-bottom: 6px;
}

.operate .fh {
  margin-left: 60px;
}

.operate {
  padding: 10px;
}

.status1,
.operate {
  margin-left: 20px;
  // height: 260px;
}

.commodity .commodity_rigth {
  margin-left: 20px;
}

.commodity .commodity_rigth .name {
  color: #363636;
  margin-bottom: 20px;
}

.commodity .commodity_rigth .gg span {
  color: red;
}

.commodity .commodity_rigth .xj {
  margin-top: 8px;
}

.commodity .commodity_rigth .xj span {
  color: red;
}

.el-input_ss {
  width: 200px;
}

.el-input_ss1 {
  width: 300px;
  margin-top: 3px;
}


.el-row {
  display: flex;
  align-items: center;
  // border: 1px solid #eee;

}

.Operationclass {
  background: #f5f7fa;
  width: 100%;
  display: flex;
  align-items: center;
  color: #909399;
  font-size: 14px;
}

.Operationclassxz {
  color: #549dfe;
  border-right: 1px solid #eee;
  border-left: 1px solid #eee;
  border-top: 1px solid #eee;
  background: #fff;
}

.Operationclass div {
  width: 80px;
  text-align: center;
  height: 40px;
  line-height: 40px;
}

.tr_left1 {
  text-align: center;
  padding-left: 6px;
  padding: 6px;
}

.tr_left {
  text-align: left;
  padding-left: 6px;
  padding: 6px;
}

.tr_right {
  text-align: right;
  padding-right: 6px;
  width: 100px;
}

.el-col {}

.status {
  margin: 0 10px;
}

.coner {
  text-align: center;
  height: 100px;
  line-height: 100px;
  font-size: 26px;
  color: #909399;
}

.border {
  border: 1px solid #000;
  // background: pink;
  margin: 20px 0;
}</style>