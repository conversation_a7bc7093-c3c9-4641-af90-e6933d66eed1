import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'/'el-icon-x' the icon show in the sidebar
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },

  {
    path: '/404',
    component: () => import('@/views/404'),
    hidden: true
  },

  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [{
      path: 'dashboard',
      name: 'Dashboard',
      component: () => import('@/views/dashboard/index'),
      meta: { title: '概况', icon: 'el-icon-s-platform' }
    }]
  },









  {
    path: '/TP',
    component: Layout,


    name: 'TP',
    meta: { title: '图片管理', icon: 'el-icon-picture' },
    children: [
      {
        path: 'TPLB',
        name: 'TPLB',
        component: () => import('@/views/TP/TPLB'),
        meta: { title: '图片列表' }
      },
      {
        path: 'TPFL',
        name: 'TPFL',
        component: () => import('@/views/TP/TPFL'),
        meta: { title: '图片分类' }
      }
    ]
  },

  {
    path: '/CPZX',
    component: Layout,
    redirect: '/CPZX/CPZX',
    name: 'CPZX',
    meta: { title: '产品中心', icon: 'el-icon-platform-eleme' },
    children: [

      {
        path: '/CP',
        name: '/CP',
        component: () => import('@/views/CPZX/CP'),
        meta: { title: '产品列表', icon: '' }
      },
      {
        path: '/CPFL',
        name: '/CPFL',
        component: () => import('@/views/CPZX/CPFL'),
        meta: { title: '产品分类', icon: '' }
      },
      // {
      //   path: '/cscsp',
      //   name: '/cscsp',
      //   component: () => import('@/views/CPZX/cscsp'),
      //   meta: { title: '产品分类', icon: '' }
      // }
    ]
  },
  {
    path: '/JJPZ',
    component: Layout,
    redirect: '/JJPZ/JJPZ',
    name: 'JJPZ',
    meta: { title: '场景', icon: 'el-icon-menu' },
    children: [

      {
        path: '/CJLB',
        name: '/CJLB',
        component: () => import('@/views/JJPZ/CP'),
        meta: { title: '场景列表', icon: '' }
      },
      {
        path: '/CJPZ',
        name: '/CJPZ',
        component: () => import('@/views/JJPZ/CPFL'),
        meta: { title: '场景配置', icon: '' }
      }
    ]
  },
  // {
  //   path: '/AL',
  //   component: Layout,
  //   redirect: '/AL/AL',
  //   name: 'AL',
  //   meta: { title: '案例中心', icon: 'el-icon-menu' },
  //   children: [

  //     {
  //       path: '/AL',
  //       name: '/AL',
  //       component: () => import('@/views/ALZX/AL'),
  //       meta: { title: '案例', icon: '' }
  //     },
  //     {
  //       path: '/ALFL',
  //       name: '/ALFL',
  //       component: () => import('@/views/ALZX/ALFL'),
  //       meta: { title: '案例分类', icon: '' }
  //     }
  //   ]
  // },
  // {
  //   path: '/TZGL',
  //   component: Layout,
  //   redirect: '/TZGL/TZGL',
  //   name: 'TZGL',
  //   meta: { title: '图册中心', icon: 'el-icon-menu' },
  //   children: [

  //     {
  //       path: '/GC',
  //       name: '/GC',
  //       component: () => import('@/views/TZGL/AL'),
  //       meta: { title: '图册', icon: '' }
  //     },
  //     {
  //       path: '/TCFL',
  //       name: '/TCFL',
  //       component: () => import('@/views/TZGL/ALFL'),
  //       meta: { title: '图册分类', icon: '' }
  //     }
  //   ]
  // },
  // {
  //   path: '/PPDT',
  //   component: Layout,
  //   redirect: '/AL/AL',
  //   name: 'PPDT',
  //   meta: { title: '品牌动态', icon: 'el-icon-s-grid' },
  //   children: [

  //     {
  //       path: '/PPDT',
  //       name: '/PPDT',
  //       component: () => import('@/views/PPDT/AL'),
  //       meta: { title: '品牌动态', icon: '' }
  //     },

  //   ]
  // },
  {
    path: '/PPXX',
    component: Layout,
    redirect: '/AL/AL',
    name: 'PPXX',
    meta: { title: '品牌信息', icon: 'el-icon-s-management' },
    children: [

      {
        path: '/PPXX',
        name: '/PPXX',
        component: () => import('@/views/PPXX/AL'),
        meta: { title: '品牌信息', icon: '' }
      },

    ]
  },{
    path: '/ZHLB',
    component: Layout,
    redirect: '/ZHLB/ZHLB',
    name: 'ZHLB',
    meta: { title: '门店列表', icon: 'el-icon-s-custom' },
    children: [

      {
        path: '/ZHLB',
        name: '/ZHLB',
        component: () => import('@/views/ZHLB/AL'),
        meta: { title: '门店列表', icon: 'el-icon-s-custom' }
      },

    ]
  },
  {
    path: '/DJK',
    component: Layout,
    redirect: '/DJK/DJK',
    name: 'DJK',
    meta: { title: '代金卡列表', icon: 'el-icon-wallet' },
    children: [

      {
        path: '/DJK',
        name: '/DJK',
        component: () => import('@/views/DJK/AL'),
        meta: { title: '代金卡列表', icon: 'el-icon-wallet' }
      },

    ]
  },{
    path: '/FC',
    component: Layout,
    redirect: '/FC/FC',
    name: 'FC',
    meta: { title: '订单管理', icon: 'el-icon-s-shop' },
    children: [

      {
        path: '/FCDD',
        name: '/FCDD',
        component: () => import('@/views/FC/FCDD'),
        meta: { title: '订单列表', icon: 'el-icon-s-shop' }
      },

    ]
  },
  {
    path: '/LXWM',
    component: Layout,


    name: 'LXWM',
    meta: { title: '联系我们', icon: 'el-icon-phone' },
    children: [

      {
        path: 'TPFL',
        name: 'TPFL',
        component: () => import('@/views/LXWM/TPFL'),
        meta: { title: '联系我们' }
      }
    ]
  },


  // 404 page must be placed at the end !!!
  // { path: '*', redirect: '/404', hidden: true }
]

const createRouter = () => new Router({
  // mode: 'history', // require service support
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})

const router = createRouter()
router.onError((error) => {
  const pattern = /Loading chunk chunk-(.*)+ failed/g;
  const isChunkLoadFailed = error.message.match(pattern);
  if (isChunkLoadFailed) {
    // Message({
    //   message: '系统已升级，正在刷新本地存储，请稍候...',
    //   type: 'warning',
    //   duration: 1500,
    //   offset: 60
    // });
    location.reload();
  }
});
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
