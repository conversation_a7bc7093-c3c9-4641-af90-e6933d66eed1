(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0f217030"],{"04be":function(e,t,r){"use strict";r("0657")},"0657":function(e,t,r){},"25f0":function(e,t,r){"use strict";var a=r("6eeb"),o=r("825a"),n=r("d039"),l=r("ad6d"),i="toString",s=RegExp.prototype,u=s[i],c=n((function(){return"/a/b"!=u.call({source:"a",flags:"b"})})),d=u.name!=i;(c||d)&&a(RegExp.prototype,i,(function(){var e=o(this),t=String(e.source),r=e.flags,a=String(void 0===r&&e instanceof RegExp&&!("flags"in s)?l.call(e):r);return"/"+t+"/"+a}),{unsafe:!0})},6135:function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"app-container"},[r("el-row",[r("el-form",{staticStyle:{"margin-top":"15px","margin-bottom":"-15px"},attrs:{inline:!0}},[r("el-form-item",{attrs:{label:"姓名："}},[[r("el-input",{attrs:{placeholder:"姓名/电话"},model:{value:e.keyword,callback:function(t){e.keyword=t},expression:"keyword"}})]],2),r("el-form-item",{attrs:{label:"行业/赛道："}},[[r("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:e.industryId,callback:function(t){e.industryId=t},expression:"industryId"}},e._l(e.industryList,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)]],2),r("el-form-item",[r("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.handleQuery}},[e._v("查询")])],1)],1)],1),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],attrs:{data:e.list,"element-loading-text":"Loading","border··":"",fit:"","highlight-current-row":""}},[r("el-table-column",{attrs:{align:"center",label:"ID",width:"95"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.id))]}}])}),r("el-table-column",{attrs:{prop:"img",label:"会员头像",width:"100"},scopedSlots:e._u([{key:"default",fn:function(e){return[r("el-image",{staticStyle:{width:"50px",height:"50px"},attrs:{src:e.row.vipPhoto,"preview-src-list":[e.row.vipPhoto]}})]}}])}),r("el-table-column",{attrs:{label:"真实姓名"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.realName))]}}])}),r("el-table-column",{attrs:{label:"公司",width:"200px",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",[e._v(e._s(t.row.companyName))])]}}])}),r("el-table-column",{attrs:{label:"地址",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.address))]}}])}),r("el-table-column",{attrs:{label:"电话号码",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.phone))]}}])}),r("el-table-column",{attrs:{label:"职位",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.vipPosition))]}}])}),r("el-table-column",{attrs:{label:"微信号",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.wechat))]}}])}),r("el-table-column",{attrs:{prop:"tag",label:"审核状态",width:"100",filters:[{text:"待审核",value:0},{text:"通过",value:1},{text:"不通过",value:2}],"filter-method":e.filterTag,"filter-placement":"bottom-end"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-tag",{attrs:{type:0===t.row.status?"warning":1===t.row.status?"success":"info","disable-transitions":""}},[e._v(e._s(0===t.row.status?"待审核":1===t.row.status?"通过":"不通过"))])]}}])}),r("el-table-column",{attrs:{align:"center",prop:"created_at",label:"创建时间",width:"200"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("i",{staticClass:"el-icon-time"}),r("span",[e._v(e._s(t.row.createTime))])]}}])}),r("el-table-column",{attrs:{fixed:"right",label:"操作",width:"140"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"编辑","hide-after":2e3,placement:"top"}},[r("el-button",{attrs:{type:"primary",icon:"el-icon-edit",circle:"",size:"small"},on:{click:function(r){return e.handleEdit(t.row)}}})],1),r("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"删除","hide-after":2e3,placement:"top"}},[r("el-button",{attrs:{type:"danger",icon:"el-icon-delete",size:"small",circle:""},on:{click:function(r){return e.handleDelete(t.row.id)}}})],1)]}}])})],1),r("el-row",[r("el-pagination",{staticStyle:{"margin-top":"30px"},attrs:{background:"","page-size":e.pageSize,"page-sizes":[20,50,100],layout:"prev,pager,next,total,->,sizes,jumper",total:e.count},on:{"size-change":e.sizeChangeHandle,"current-change":e.currentChangeHandle,"prev-click":e.prevClickHandle,"next-click":e.nextClickHandle}})],1),r("el-dialog",{attrs:{title:"编辑内容",visible:e.dialogFormVisible,"close-on-click-modal":!1,"close-on-press-escape":!1},on:{"update:visible":function(t){e.dialogFormVisible=t}}},[r("el-form",{ref:"form",attrs:{model:e.form}},[r("el-form-item",{attrs:{label:"会员头像",prop:"vipPhoto","label-width":e.formLabelWidth}},[r("el-upload",{staticClass:"avatar-uploader",attrs:{"auto-upload":!1,action:"#","show-file-list":!1,"on-change":e.handleBigPicSuccess,"before-upload":e.beforeBigPicUpload}},[e.form.vipPhoto?r("img",{staticClass:"avatar",attrs:{src:e.form.vipPhoto}}):r("i",{staticClass:"el-icon-plus avatar-uploader-icon"})])],1),r("el-form-item",{attrs:{label:"真实姓名",prop:"realName"}},[r("el-input",{attrs:{autocomplete:"off"},model:{value:e.form.realName,callback:function(t){e.$set(e.form,"realName",t)},expression:"form.realName"}})],1),r("el-form-item",{attrs:{label:"手机号",prop:"phone"}},[r("el-input",{attrs:{autocomplete:"off"},model:{value:e.form.phone,callback:function(t){e.$set(e.form,"phone",t)},expression:"form.phone"}})],1),r("el-form-item",{attrs:{label:"微信号",prop:"wechat"}},[r("el-input",{attrs:{autocomplete:"off"},model:{value:e.form.wechat,callback:function(t){e.$set(e.form,"wechat",t)},expression:"form.wechat"}})],1),r("el-form-item",{attrs:{label:"公司",prop:"companyName"}},[r("el-input",{attrs:{autocomplete:"off"},model:{value:e.form.companyName,callback:function(t){e.$set(e.form,"companyName",t)},expression:"form.companyName"}})],1),r("el-form-item",{attrs:{label:"职位",prop:"vipPosition"}},[r("el-input",{attrs:{autocomplete:"off"},model:{value:e.form.vipPosition,callback:function(t){e.$set(e.form,"vipPosition",t)},expression:"form.vipPosition"}})],1),r("el-form-item",{attrs:{label:"所在地区",prop:"address"}},[r("el-input",{attrs:{autocomplete:"off"},model:{value:e.form.address,callback:function(t){e.$set(e.form,"address",t)},expression:"form.address"}})],1),r("el-form-item",{attrs:{label:"行业/赛道",prop:"industryId"}},[r("el-select",{attrs:{filterable:"","value-key":"id","default-first-option":"",remote:"","reserve-keyword":"",clearable:!0,placeholder:"请选择"},model:{value:e.form.industryId,callback:function(t){e.$set(e.form,"industryId",t)},expression:"form.industryId"}},e._l(e.industryList,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-form-item",{attrs:{label:"个人简历",prop:"vipDescribe"}},[r("el-input",{attrs:{type:"textarea",autosize:"",placeholder:"请输入内容"},model:{value:e.form.vipDescribe,callback:function(t){e.$set(e.form,"vipDescribe",t)},expression:"form.vipDescribe"}})],1),r("el-form-item",{attrs:{label:"我能提供的",prop:"resourcesProvided"}},[r("el-input",{attrs:{type:"textarea",rows:2,placeholder:"请输入内容"},model:{value:e.form.resourcesProvided,callback:function(t){e.$set(e.form,"resourcesProvided",t)},expression:"form.resourcesProvided"}})],1),r("el-form-item",{attrs:{label:"我需要的",prop:"requirement"}},[r("el-input",{attrs:{type:"textarea",rows:2,placeholder:"请输入内容"},model:{value:e.form.requirement,callback:function(t){e.$set(e.form,"requirement",t)},expression:"form.requirement"}})],1),r("el-form-item",{attrs:{label:"审核状态"}},[r("el-radio-group",{model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},[r("el-radio",{attrs:{label:1}},[e._v("通过")]),r("el-radio",{attrs:{label:2}},[e._v("不通过")])],1)],1),r("el-form-item",{attrs:{label:"名片/工作证",prop:"powUrl","label-width":e.formLabelWidth}},[r("el-upload",{attrs:{action:"#","list-type":"picture-card","on-preview":e.handlePreview,"on-remove":e.handleRemove,"auto-upload":!1,"file-list":e.form.powUrl,multiple:"","on-change":e.handleSmallPicSuccess}},[r("i",{staticClass:"el-icon-plus"})]),r("el-dialog",{attrs:{visible:e.dialogVisibleImg,top:"0",center:"",modal:!1},on:{"update:visible":function(t){e.dialogVisibleImg=t}}},[r("img",{attrs:{width:"100%",src:e.dialogImageUrl,alt:""}})])],1),r("el-form-item",{attrs:{label:"公司环境图",prop:"workEnvironmentUrl","label-width":e.formLabelWidth}},[r("el-upload",{attrs:{action:"#","list-type":"picture-card","on-preview":e.handlePreview,"on-remove":e.handleRemove,"auto-upload":!1,"file-list":e.form.workEnvironmentUrl,multiple:"","on-change":e.handleSmallPicSuccess1,"data-type":"1"}},[r("i",{staticClass:"el-icon-plus"})]),r("el-dialog",{attrs:{visible:e.dialogVisibleImg,top:"0",center:"",modal:!1},on:{"update:visible":function(t){e.dialogVisibleImg=t}}},[r("img",{attrs:{width:"100%",src:e.dialogImageUrl,alt:""}})])],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:e.formCancal}},[e._v("取 消")]),r("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.formSubmit("form")}}},[e._v("确 定")])],1)],1)],1)},o=[],n=r("5530"),l=(r("d81d"),r("e9c4"),r("d3b7"),r("25f0"),r("d0f1")),i=r("91b6"),s={data:function(){return{formLabelWidth:"220",list:null,listLoading:!0,pageNum:1,pageSize:20,industryId:"",keyword:"",count:0,form:{powUrl:[],workEnvironmentUrl:[]},dialogFormVisible:!1,groupList:[],dialogImageUrl:"",dialogVisibleImg:!1,industryList:[],formList:{}}},created:function(){this.fetchData(),this.fetchIndustryDAta()},methods:{handleQuery:function(){this.pageNum=1,this.fetchData()},formSubmit:function(){var e=this,t=this.form.powUrl.map((function(e){return e.url})),r=this.form.workEnvironmentUrl.map((function(e){return e.url}));delete this.form.expireTime,delete this.form.updateTime,Object(l["a"])(Object(n["a"])(Object(n["a"])({},this.form),{},{powUrl:JSON.stringify(t),workEnvironmentUrl:JSON.stringify(r)})).then((function(t){console.log(t),200===t.data.code?(e.$message.success("编辑成功"),e.fetchData()):e.$message.error("编辑失败")})),this.dialogFormVisible=!1},fetchIndustryDAta:function(){var e=this;Object(l["j"])().then((function(t){var r=t.data,a=r.map((function(e){return{value:e.id,label:e.industryName}}));e.industryList=a}))},fetchData:function(){var e=this;this.listLoading=!0;var t=this.pageNum,r=this.pageSize,a=this.industryId,o=this.keyword,n={pageNum:t,pageSize:r,industryId:a,keyword:o};Object(l["g"])(n).then((function(t){if(200===t.data.code){console.log(t.data.page.records);var r=t.data.page.records;r.map((function(e){e.workEnvironmentUrl||(e.workEnvironmentUrl=[]),e.powUrl||(e.powUrl=[])})),e.list=r,e.count=t.data.page.total}e.listLoading=!1}))},sizeChangeHandle:function(e){this.pageSize=parseInt(e),this.pageNum=1,this.fetchData()},currentChangeHandle:function(e){this.pageNum=parseInt(e),this.fetchData()},prevClickHandle:function(){this.pageNum-=1,this.fetchData()},nextClickHandle:function(){this.currentPage+=1,this.fetchData()},filterTag:function(e,t){return t.role===e},handleEdit:function(e){this.fetchIndustryDAta(),this.dialogFormVisible=!0,e.powUrl&&e.powUrl.length>0&&(e.powUrl=JSON.parse(e.powUrl).map((function(e,t){return{uid:t,url:e}}))),e.workEnvironmentUrl&&e.powUrl.length>0&&(e.workEnvironmentUrl=JSON.parse(e.workEnvironmentUrl).map((function(e,t){return{uid:t,url:e}}))),this.form=e},handleDelete:function(e){},formCancal:function(){this.form={powUrl:[],workEnvironmentUrl:[]},this.dialogFormVisible=!1},handleRemove:function(e,t){console.log(t),this.form.workEnvironmentUrl=t},handleRemove1:function(e,t){this.form.powUrl=t},handlePreview:function(e){this.dialogImageUrl=e.url,this.dialogVisibleImg=!0},checkPicFormat:function(e){var t="image/jpeg"===e.raw.type,r="image/png"===e.raw.type;return!(!r&&!t)||(this.$message.error("上传图片只能是 JPG/PNG 格式!"),!1)},handlePictureCardPreview:function(e){this.dialogImageUrl=e.url,this.dialogVisible=!0},handleSmallPicSuccess:function(e,t){this.uploadImg(e,2)},handleSmallPicSuccess1:function(e,t){this.uploadImg(e,3)},handleBigPicSuccess:function(e,t){this.uploadImg(e,1)},beforeBigPicUpload:function(e){return this.checkPicFormat(e)},uploadImg:function(e,t){var r=this,a=new FormData;a.append("file",e.raw),Object(i["a"])(a).then((function(e){if(1===t)r.form.vipPhoto=e.data.data;else if(2===t){var a=r.form.powUrl;a.push({uid:Math.random().toString(16).substring(6),url:e.data.data}),r.form=Object(n["a"])(Object(n["a"])({},r.form),{},{powUrl:a})}else if(3===t){var o=r.form.workEnvironmentUrl;o.push({uid:Math.random().toString(16).substring(6),url:e.data.data}),r.form=Object(n["a"])(Object(n["a"])({},r.form),{},{workEnvironmentUrl:o})}}))}}},u=s,c=(r("04be"),r("2877")),d=Object(c["a"])(u,a,o,!1,null,"61592c96",null);t["default"]=d.exports},"91b6":function(e,t,r){"use strict";r.d(t,"a",(function(){return o}));var a=r("b775");function o(e){return Object(a["a"])({url:"upload/fileUpload",method:"post",data:e})}},d0f1:function(e,t,r){"use strict";r.d(t,"g",(function(){return o})),r.d(t,"j",(function(){return n})),r.d(t,"a",(function(){return l})),r.d(t,"f",(function(){return i})),r.d(t,"n",(function(){return s})),r.d(t,"c",(function(){return u})),r.d(t,"e",(function(){return c})),r.d(t,"m",(function(){return d})),r.d(t,"b",(function(){return m})),r.d(t,"h",(function(){return f})),r.d(t,"k",(function(){return p})),r.d(t,"l",(function(){return h})),r.d(t,"i",(function(){return b})),r.d(t,"d",(function(){return g}));var a=r("b775");function o(e){return Object(a["a"])({url:"membersVip/queryAllPc",method:"get",params:e})}function n(e){return Object(a["a"])({url:"memberIndustryType/findAll",method:"get",params:e})}function l(e){return Object(a["a"])({url:"message/AuditMember",method:"post",data:e})}function i(e){return Object(a["a"])({url:"memberIndustryType/page",method:"get",params:e})}function s(e){return Object(a["a"])({url:"memberIndustryType/saveOrEedit",method:"post",data:e})}function u(e){return Object(a["a"])({url:"memberIndustryType",method:"delete",data:e})}function c(e){return Object(a["a"])({url:"membersGroupType/page",method:"get",params:e})}function d(e){return Object(a["a"])({url:"membersGroupType/saveOrEedit",method:"post",data:e})}function m(e){return Object(a["a"])({url:"membersGroupType",method:"delete",data:e})}function f(e){return Object(a["a"])({url:"membersVip/queryBig",method:"get",params:e})}function p(e){return Object(a["a"])({url:"payOrder/queryAllPc",method:"get",params:e})}function h(e){return Object(a["a"])({url:"membersGroupType/removeVip",method:"post",data:e})}function b(e){return Object(a["a"])({url:"membersVip/queryDistributionVip",method:"get",params:e})}function g(e){return Object(a["a"])({url:"membersGroupType/distributionVipGroupId",method:"post",data:e})}},e9c4:function(e,t,r){var a=r("23e7"),o=r("d066"),n=r("d039"),l=o("JSON","stringify"),i=/[\uD800-\uDFFF]/g,s=/^[\uD800-\uDBFF]$/,u=/^[\uDC00-\uDFFF]$/,c=function(e,t,r){var a=r.charAt(t-1),o=r.charAt(t+1);return s.test(e)&&!u.test(o)||u.test(e)&&!s.test(a)?"\\u"+e.charCodeAt(0).toString(16):e},d=n((function(){return'"\\udf06\\ud834"'!==l("\udf06\ud834")||'"\\udead"'!==l("\udead")}));l&&a({target:"JSON",stat:!0,forced:d},{stringify:function(e,t,r){var a=l.apply(null,arguments);return"string"==typeof a?a.replace(i,c):a}})}}]);