import request from '@/utils/request'

export function userinfos(params) {
  return request({
    url: 'oe_userinfos_.csp',
    method: 'get',
    params
  })
}
export function brickLayers(params) {
  return request({
    url: 'oe_brickLayers_.csp',
    method: 'get',
    params
  })
}
export function editBrickInfo(params) {
  return request({
    url: 'oe_editBrickInfo_.csp',
    method: 'get',
    params
  })
}


export function createSupervisor(params) {
  return request({
    url: 'oe_createSupervisor_.csp',
    method: 'get',
    params
  })
}


export function supervisorList(params) {
  return request({
    url: 'oe_supervisorList_.csp',
    method: 'get',
    params
  })
}

export function delSupervisor(params) {
  return request({
    url: 'oe_delSupervisor_.csp',
    method: 'get',
    params
  })
}

export function delBrick(params) {
  return request({
    url: 'oe_delBrick_.csp',
    method: 'get',
    params
  })
}export function setHqBrick(params) {
  return request({
    url: 'oe_setHqBrick_.csp',
    method: 'get',
    params
  })
}

export function checkApplys(params) {
  return request({
    url: 'oe_checkApplys_.csp',
    method: 'get',
    params
  })
}