<template>
  <div class="login-container">
    <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form" auto-complete="on"
      label-position="left">

      <div class="title-container">
        <h3 class="title">施恩德岩奢高定后台</h3>
        <div class="bxz"></div>
      </div>

      <el-form-item prop="account">
        <span class="svg-container">
          <svg-icon icon-class="user" />
        </span>
        <el-input ref="account" v-model="loginForm.account" placeholder="account" name="account" type="text"
          tabindex="1" auto-complete="on" />
      </el-form-item>

      <el-form-item prop="password">
        <span class="svg-container">
          <svg-icon icon-class="password" />
        </span>
        <el-input :key="passwordType" ref="password" v-model="loginForm.password" :type="passwordType"
          placeholder="Password" name="password" tabindex="2" auto-complete="on" @keyup.enter.native="handleLogin" />
        <span class="show-pwd" @click="showPwd">
          <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'" />
        </span>
      </el-form-item>

      <el-button :loading="loading" type="primary"
        style="width:100%;margin-top: 30px; margin-bottom:30px;background-color:#3F9DFF;border:#3F9DFF"
        @click.native.prevent="handleLogin">登录</el-button>

    
    </el-form>
  </div>
</template>

<script>
import { validUsername } from '@/utils/validate'
import { Loading } from 'element-ui';
export default {
  name: 'Login',
  data() {
    const validateUsername = (rule, value, callback) => {
      // if (!validUsername(value)) {
      //   callback(new Error('请输入正确的账号'))
      // } else {
      //   callback()
      // }
    }
    const validatePassword = (rule, value, callback) => {
      // if (value.length < 6) {
      //   callback(new Error('密码错误'))
      // } else {
      //   callback()
      // }
    }
    return {
       loading: undefined,
        baseUrl : process.env.VUE_APP_BASE_API + "/oe_loginManage_.csp",
      loginForm: {
        account: '',
        password: '',
        //account: '',
        //password: ''
      },
      loginRules: {
        account: [
          { required: true, trigger: 'blur', validator: validateUsername }
        ],
        password: [
          { required: true, trigger: 'blur', validator: validatePassword }
        ]
      },
      loading: false,
      passwordType: 'password',
      redirect: undefined,
      zh:"sedysgd",
      mm:"sedysgd",
      zh1:"SCHENDERAR",
      mm1:"SCHENDERAR",
      
    }
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect
      },
      immediate: true
    }
  },
  methods: {
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    handleLogin() {
      if (!this.loginForm.account) {
        this.$message({
          showClose: true,
          message: '请输入账号！',
          type: 'warning'
        });
        return
      }
      if (!this.loginForm.password) {
        this.$message({
          showClose: true,
          message: '请输入密码！',
          type: 'warning'
        });
        return
      }

      this.loading = false;
      //  var params = new URLSearchParams();
      // params.append('userCode', this.loginForm.account);
      // // params.append('dbName', 'mati');
      // params.append('pwd',this.$md5(this.loginForm.password) ? this.$md5(this.loginForm.password) : '');
      // // this.axios.post('http://192.168.0.116:9528/api/oe_adminLogin_.csp', params,).then(res => { 
      // this.axios.post(this.baseUrl, params,).then(res => {
   
      // }).catch(err => { 
      //   console.log(err);
        
      //   this.$message.error(res.data.msg) })
      // this.loading = false

      if (this.loginForm.account == this.zh && this.loginForm.password == this.mm) {
          window.localStorage.setItem('SEDJJXCX', 'abc123');
          window.localStorage.setItem('JJHTDBnmame', 'sedysgd_cn');
          window.localStorage.setItem('SEDJJXCX_LHFW_UserInformation_HT', encodeURIComponent(JSON.stringify(11)))
          this.$message.success('登录成功！')

          this.$router.replace("/");
        } else if(this.loginForm.account == this.zh1 && this.loginForm.password == this.mm1){
          window.localStorage.setItem('hwljhtxs', 'abc123');
          window.localStorage.setItem('JJHTDBnmame', 'sedysgd_cn');

          window.localStorage.setItem('SEDJJXCX_LHFW_UserInformation_HT', encodeURIComponent(JSON.stringify(11)))
          this.$message.success('登录成功！')

          this.$router.replace("/");
        }
        
        else {
          this.$message.error('账号密码错误！')
        }
   

    }
  
  }
}
</script>

<style lang="scss">
/* 修复input 背景不协调 和光标变色 */
/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */

$bg: #283443;
$light_gray: #fff;
$cursor: #fff;

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
  .login-container .el-input input {
    color: $cursor;
  }
}

/* reset element-ui css */
.login-container {
  .el-input {
    display: inline-block;
    height: 47px;
    width: 85%;

    input {
      background: transparent;
      border: 0px;
      -webkit-appearance: none;
      border-radius: 0px;
      padding: 12px 5px 12px 15px;
      color: $light_gray;
      height: 47px;
      caret-color: $cursor;

      &:-webkit-autofill {
        box-shadow: 0 0 0px 1000px $bg inset !important;
        -webkit-text-fill-color: $cursor !important;
      }
    }
  }

  .el-form-item {
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    color: #454545;
  }
}
</style>

<style lang="scss" scoped>
$bg: #2d3a4b;
$dark_gray: #889aa4;
$light_gray: #eee;

.login-container {
  min-height: 100%;
  width: 100%;
  overflow: hidden;
  background: url(https://cdn.juesedao.cn/sed/b46706326a824b81b9fb51719a36e4b1) no-repeat;
  .login-form {
    width: 400px;
    max-width: 100%;
    overflow: hidden;
    position: absolute;
       left: 50%;top: 45%;
        transform: translate(-50%, -50%);
        background-color: #ffffff;
        padding: 40px 20px;
        border-radius: 15px;

  }

  .tips {
    font-size: 14px;
    color: #fff;
    margin-bottom: 10px;

    span {
      &:first-of-type {
        margin-right: 16px;
      }
    }
  }

  .svg-container {
    padding: 6px 5px 6px 15px;
    color: $dark_gray;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }

  .title-container {
    position: relative;

    .title {
      font-size: 28px;
      color: #404347;
      text-align: center;
      font-weight: bold;
      margin: 0;
    }
    .bxz{
      width: 60px;
      height: 3px;
      border-radius: 15px;
      margin: 0 auto; 
      background-color: #3F9DFF;
      margin: 20px auto 40px auto;

    }
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    color: $dark_gray;
    cursor: pointer;
    user-select: none;
  }
}
</style>
