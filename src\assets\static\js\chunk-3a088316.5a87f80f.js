(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3a088316"],{"34d0":function(e,t,a){},"91b6":function(e,t,a){"use strict";a.d(t,"a",(function(){return i}));var o=a("b775");function i(e){return Object(o["a"])({url:"upload/fileUpload",method:"post",data:e})}},b9f1:function(e,t,a){"use strict";a("34d0")},e3e7:function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:15}},[a("el-button",{attrs:{type:"primary",size:"medium"},on:{click:e.handleAdd}},[e._v("新增")])],1),a("el-col",{attrs:{span:7}},[a("el-input",{attrs:{placeholder:"请输入内容","prefix-icon":"el-icon-search"},model:{value:e.keyWords,callback:function(t){e.keyWords=t},expression:"keyWords"}})],1),a("el-col",{attrs:{span:1}},[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.searchBtn}},[e._v("搜索")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.tableData,"border..":"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{align:"center",label:"ID",width:"95"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.id))]}}])}),a("el-table-column",{attrs:{prop:"coverImage",label:"活动封面图",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(e){return[a("el-image",{staticStyle:{width:"50px",height:"50px"},attrs:{src:e.row.coverImage,"preview-src-list":[e.row.coverImage]}})]}}])}),a("el-table-column",{attrs:{prop:"activityTitle",label:"活动标题",width:"120",align:"center"}}),a("el-table-column",{attrs:{prop:"activityDescribe",label:"活动简述",align:"center"}}),a("el-table-column",{attrs:{prop:"activeAddress",label:"活动地址",align:"center"}}),a("el-table-column",{attrs:{prop:"activeContext",label:"活动详细信息",align:"center",width:"300px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"ImageDiv",domProps:{innerHTML:e._s(t.row.activeContext)}})]}}])}),a("el-table-column",{attrs:{prop:"readingsNumber",label:"阅读量",width:"80",align:"center"}}),a("el-table-column",{attrs:{prop:"startTime",label:"活动开始时间",align:"center"}}),a("el-table-column",{attrs:{prop:"endTime",label:"活动结束时间",align:"center"}}),a("el-table-column",{attrs:{prop:"createTime",label:"订单创建时间",align:"center"}}),a("el-table-column",{attrs:{label:"操作",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"编辑","hide-after":2e3,placement:"top"}},[a("el-button",{attrs:{type:"primary",icon:"el-icon-edit",circle:"",size:"small"},on:{click:function(a){return e.handleEdit(t.row)}}})],1),a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"删除","hide-after":2e3,placement:"top"}},[a("el-button",{attrs:{type:"danger",icon:"el-icon-delete",size:"small",circle:""},on:{click:function(a){return e.handleDelete(t.row.id)}}})],1)]}}])})],1),a("el-row",[a("el-pagination",{staticStyle:{"margin-top":"30px"},attrs:{background:"","page-size":e.page.pageSize,"page-sizes":[5,10,20],layout:"prev,pager,next,total,->,sizes,jumper",total:e.count},on:{"size-change":e.sizeChangeHandle,"current-change":e.currentChangeHandle,"prev-click":e.prevClickHandle,"next-click":e.nextClickHandle}})],1),a("el-dialog",{attrs:{title:e.dialogTitle,visible:e.isShowDialog,"close-on-click-modal":!1,width:"800px"},on:{"update:visible":function(t){e.isShowDialog=t},close:e.formCancal}},[a("el-form",{ref:"form",attrs:{model:e.formData}},[a("el-form-item",{attrs:{label:"活动封面图","label-width":e.formLabelWidth}},[a("el-upload",{staticClass:"avatar-uploader",class:{disable:e.coverImage.length>0},attrs:{action:"#","list-type":"picture-card","on-remove":e.handleRemove,"auto-upload":!1,"file-list":e.coverImage,"on-change":e.handleChange,"before-upload":e.beforeUpload}},[a("i",{staticClass:"el-icon-plus"})])],1),a("el-form-item",{attrs:{label:"活动标题",prop:"activityTitle"}},[a("el-input",{attrs:{autocomplete:"off"},model:{value:e.formData.activityTitle,callback:function(t){e.$set(e.formData,"activityTitle",t)},expression:"formData.activityTitle"}})],1),a("el-form-item",{attrs:{label:"活动简述",prop:"activityDescribe"}},[a("el-input",{attrs:{autocomplete:"off"},model:{value:e.formData.activityDescribe,callback:function(t){e.$set(e.formData,"activityDescribe",t)},expression:"formData.activityDescribe"}})],1),a("el-form-item",{attrs:{label:"活动地址",prop:"activeAddress"}},[a("el-input",{attrs:{autocomplete:"off"},model:{value:e.formData.activeAddress,callback:function(t){e.$set(e.formData,"activeAddress",t)},expression:"formData.activeAddress"}})],1),a("el-form-item",{attrs:{label:"活动详细信息"}}),a("VueEditor",{ref:"vueEditor",attrs:{config:e.config}}),a("el-form-item",{attrs:{label:"活动开始时间",prop:"startTime"}},[a("el-date-picker",{attrs:{type:"date",placeholder:"选择开始日期"},model:{value:e.formData.startTime,callback:function(t){e.$set(e.formData,"startTime",t)},expression:"formData.startTime"}})],1),a("el-form-item",{attrs:{label:"活动结束时间",prop:"endTime"}},[a("el-date-picker",{attrs:{type:"date",placeholder:"选择结束日期"},model:{value:e.formData.endTime,callback:function(t){e.$set(e.formData,"endTime",t)},expression:"formData.endTime"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.formCancal}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.formSubmit()}}},[e._v("确 定")])],1)],1)],1)},i=[],l=a("5530"),r=a("b775");function n(e){return Object(r["a"])({url:"active/queryAllPc",method:"get",params:e})}function s(e){return Object(r["a"])({url:"active/saveOrEedit",method:"POST",data:e})}function c(e){return Object(r["a"])({url:"active",method:"delete",data:e})}var d=a("91b6"),u=a("3707"),m=a.n(u);a("8096");function f(e){if("undefined"==typeof e)return"";var t=new Date(parseInt(e)),a=t.getFullYear(),o=t.getMonth()+1;o=o<10?"0"+o:o;var i=t.getDate();i=i<10?"0"+i:i;var l=t.getHours();l=l<10?"0"+l:l;var r=t.getMinutes();r=r<10?"0"+r:r;var n=t.getSeconds();return n=n<10?"0"+n:n,a+"-"+o+"-"+i+" "+l+":"+r+":"+n}var p={components:{VueEditor:m.a},data:function(){return{loading:!0,formLabelWidth:"220",tableData:[],keyWords:"",count:10,isShowDialog:!1,dialogTitle:"新增弹窗",formData:{coverImage:"",activityTitle:"",activityDescribe:"",activeAddress:"",activeContext:"",startTime:"",endTime:""},config:{uploadImage:{url:"".concat("https://medodt.juesedao.com/wanliu/","upload/fileUpload"),name:"file",uploadSuccess:function(e,t){console.log(e),t(e.data.data)}},uploadVideo:{url:"".concat("https://medodt.juesedao.com/wanliu/","upload/fileUpload"),name:"file",uploadSuccess:function(e,t){t(e.data.data)}}},coverImage:[],ImgeUrl:"",delList:[],page:{pageNum:1,pageSize:5}}},created:function(){this.queryAllData()},mounted:function(){},methods:{queryAllData:function(){var e=this;this.loading=!0,n(Object(l["a"])(Object(l["a"])({},this.page),{},{keyWords:this.keyWords})).then((function(t){console.log(t),200===t.data.code&&(e.tableData=t.data.startList.records,console.log(e.tableData),e.count=t.data.startList.total),e.loading=!1}))},searchBtn:function(){this.queryAllData()},handleEdit:function(e){var t=this;this.dialogTitle="编辑内容",this.isShowDialog=!0,console.log(e),this.coverImage=[{url:e.coverImage}],this.$nextTick((function(){var a=t.$refs.vueEditor.editor;a.clipboard.dangerouslyPasteHTML(0,e.activeContext)})),this.formData=e,console.log(this.formData)},handleDelete:function(e){var t=this,a=[];a.push(e),this.delList=a,this.$confirm("此操作将永久删除选中的话题","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",center:!0}).then((function(){c(t.delList).then((function(e){console.log(e),200===e.data.code&&(t.$message.success("删除成功"),t.queryAllData())}))})).catch((function(){t.$message({type:"info",message:"已取消删除"})}))},handleAdd:function(){this.dialogTitle="新增内容",this.isShowDialog=!0},sizeChangeHandle:function(e){console.log(e),this.page.pageSize=e,this.queryAllData()},currentChangeHandle:function(e){this.page.pageNum=e,this.queryAllData()},prevClickHandle:function(e){this.page.pageNum=e,this.queryAllData()},nextClickHandle:function(e){console.log(4),this.page.pageNum=e,this.queryAllData()},handleChange:function(e,t){this.coverImage=t,console.log(1),this.uploadImg(e)},beforeUpload:function(e){console.log(e);var t=["image/jpeg","image/png"];if(-1===t.indexOf(e.type))return this.$message.error("目前只支持jpeg / png 格式"),!1},handleRemove:function(e,t){this.coverImage=t},uploadImg:function(e){var t=this,a=new FormData;a.append("file",e.raw),Object(d["a"])(a).then((function(e){console.log(e),"success"===e.data.msg&&(t.ImgeUrl=e.data.data,t.coverImage[0].status="success")}))},formCancal:function(){this.isShowDialog=!1,this.formData={},this.coverImage=[],this.$refs.vueEditor.editor.root.innerHTML=""},formSubmit:function(){var e=this;this.formData.activeContext=this.$refs.vueEditor.editor.root.innerHTML,"新增内容"===this.dialogTitle&&(this.formData.coverImage=this.ImgeUrl?this.ImgeUrl:"");var t=new Date(this.formData.startTime),a=new Date(this.formData.endTime);this.formData.startTime=f(t.getTime()),this.formData.endTime=f(a.getTime()),s(this.formData).then((function(t){console.log(t),200===t.data.code&&("新增内容"===e.dialogTitle?e.$message.success("新增成功"):e.$message.success("编辑成功"),e.isShowDialog=!1,e.formData={},e.queryAllData())}))}}},g=p,h=(a("b9f1"),a("2877")),v=Object(h["a"])(g,o,i,!1,null,"4d1dca2c",null);t["default"]=v.exports}}]);