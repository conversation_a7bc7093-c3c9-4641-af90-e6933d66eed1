import request from '@/utils/request'

export function datumTypeList(params) {
  return request({
    url: 'oe_datumTypeList_.csp',
    method: 'get',
    params
  })
}
export function createDatumType(params) {
    return request({
      url: 'oe_createDatumType_.csp',
      method: 'post',
      params
    })
  }
  export function deleteDatumType(params) {
    return request({
      url: 'oe_deleteDatumType_.csp',
      method: 'post',
      params
    })
  }
  export function Goods(params) {
    return request({
      url: 'oe_datumList_.csp',
      method: 'get',
      params
    })
  }

  export function DeleteGoods(params) {
    return request({
      url: 'oe_deleteDatum_.csp',
      method: 'post',
      params
    })
  }

  export function QueryAttrGroup(params) {
    return request({
      url: 'oe_queryAttrGroup_.csp',
      method: 'post',
      params
    })
  }
  export function QueryAttr(params) {
    return request({
      url: 'oe_queryAttr_.csp',
      method: 'post',
      params
    })
  }
  export function allDatumType(params) {
    return request({
      url: 'oe_allDatumType_.csp',
      method: 'post',
      params
    })
  }
