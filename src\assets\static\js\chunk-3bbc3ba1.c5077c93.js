(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3bbc3ba1"],{"25f0":function(e,t,o){"use strict";var i=o("6eeb"),a=o("825a"),n=o("d039"),r=o("ad6d"),l="toString",c=RegExp.prototype,s=c[l],u=n((function(){return"/a/b"!=s.call({source:"a",flags:"b"})})),d=s.name!=l;(u||d)&&i(RegExp.prototype,l,(function(){var e=a(this),t=String(e.source),o=e.flags,i=String(void 0===o&&e instanceof RegExp&&!("flags"in c)?r.call(e):o);return"/"+t+"/"+i}),{unsafe:!0})},"7c27":function(e,t,o){"use strict";o("9743")},"91b6":function(e,t,o){"use strict";o.d(t,"a",(function(){return a}));var i=o("b775");function a(e){return Object(i["a"])({url:"upload/fileUpload",method:"post",data:e})}},9743:function(e,t,o){},a710:function(e,t,o){"use strict";o.d(t,"g",(function(){return a})),o.d(t,"f",(function(){return n})),o.d(t,"e",(function(){return r})),o.d(t,"c",(function(){return l})),o.d(t,"d",(function(){return c})),o.d(t,"a",(function(){return s}));var i=o("b775");function a(e){return Object(i["a"])({url:"topicCooperation/page",method:"get",params:e})}function n(){return Object(i["a"])({url:"topicCooperation/findAll",method:"get"})}function r(e){return Object(i["a"])({url:"topicCooperation/saveOrEedit",method:"post",data:e})}function l(e){return Object(i["a"])({url:"topicCooperation",method:"delete",data:e})}function c(e){return Object(i["a"])({url:"cooperation/queryAll",method:"get",params:e})}function s(e){return Object(i["a"])({url:"cooperation/saveOrEedit",method:"post",data:e})}},e9c4:function(e,t,o){var i=o("23e7"),a=o("d066"),n=o("d039"),r=a("JSON","stringify"),l=/[\uD800-\uDFFF]/g,c=/^[\uD800-\uDBFF]$/,s=/^[\uDC00-\uDFFF]$/,u=function(e,t,o){var i=o.charAt(t-1),a=o.charAt(t+1);return c.test(e)&&!s.test(a)||s.test(e)&&!c.test(i)?"\\u"+e.charCodeAt(0).toString(16):e},d=n((function(){return'"\\udf06\\ud834"'!==r("\udf06\ud834")||'"\\udead"'!==r("\udead")}));r&&i({target:"JSON",stat:!0,forced:d},{stringify:function(e,t,o){var i=r.apply(null,arguments);return"string"==typeof i?i.replace(l,u):i}})},efc7:function(e,t,o){"use strict";o.r(t);var i=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticClass:"app-container"},[o("el-row",[o("el-col",{attrs:{span:12}},[o("el-button",{attrs:{type:"primary",icon:"el-icon-plus"},on:{click:e.handleAddLearn}},[e._v("添加")]),o("el-button",{attrs:{type:"danger",icon:"el-icon-delete"},on:{click:e.handleDeleteLearn}},[e._v("批量删除")])],1)],1),o("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"multipleTable",attrs:{data:e.list,"element-loading-text":"Loading","border··":"",fit:"","highlight-current-row":"","tooltip-effect":"dark"},on:{"selection-change":e.handleSelectionChange}},[o("el-table-column",{attrs:{type:"selection",width:"55"}}),o("el-table-column",{attrs:{align:"center",label:"ID",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.id))]}}])}),o("el-table-column",{attrs:{label:"会员姓名",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.realName))]}}])}),o("el-table-column",{attrs:{label:"会员公司",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.companyName))]}}])}),o("el-table-column",{attrs:{label:"标题",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.title))]}}])}),o("el-table-column",{attrs:{label:"正文",align:"center",width:"400px"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.cooperationDescribe))]}}])}),o("el-table-column",{attrs:{prop:"vipId",label:"类型",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-tag",{attrs:{type:1===t.row.vipId?"success":"primary","disable-transitions":""}},[e._v(e._s(1===t.row.vipId?"平台需求":"会员需求"))])]}}])}),o("el-table-column",{attrs:{align:"center",prop:"created_at",label:"创建时间"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("i",{staticClass:"el-icon-time"}),o("span",[e._v(e._s(t.row.createTime?t.row.createTime:"暂无"))])]}}])}),o("el-table-column",{attrs:{align:"center",prop:"created_at",label:"修改时间"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("i",{staticClass:"el-icon-time"}),o("span",[e._v(e._s(t.row.updateTime?t.row.updateTime:"暂无"))])]}}])}),o("el-table-column",{attrs:{prop:"tag",label:"审核状态",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-tag",{attrs:{type:0===t.row.status?"warning":1===t.row.status?"success":"info","disable-transitions":""}},[e._v(e._s(0===t.row.status?"待审核":1===t.row.status?"通过":"不通过"))])]}}])}),o("el-table-column",{attrs:{fixed:"right",label:"操作",width:"140"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"编辑","hide-after":2e3,placement:"top"}},[o("el-button",{attrs:{type:"primary",icon:"el-icon-edit",circle:"",size:"small"},on:{click:function(o){return e.handleEdit(t.row)}}})],1),o("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"删除","hide-after":2e3,placement:"top"}},[o("el-button",{attrs:{type:"danger",icon:"el-icon-delete",size:"small",circle:""},on:{click:function(o){return e.handleDelete(t.row.id)}}})],1)]}}])})],1),o("el-row",[o("el-pagination",{staticStyle:{"margin-top":"30px"},attrs:{background:"","page-size":e.pageSize,"page-sizes":[10,20,30],layout:"prev,pager,next,total,->,sizes,jumper",total:e.count},on:{"size-change":e.sizeChangeHandle,"current-change":e.currentChangeHandle,"prev-click":e.prevClickHandle,"next-click":e.nextClickHandle}})],1),o("el-dialog",{attrs:{title:"编辑内容",visible:e.dialogFormVisible,"before-close":e.handleClose},on:{"update:visible":function(t){e.dialogFormVisible=t}}},[o("el-form",{ref:"form",attrs:{model:e.form}},[o("el-form-item",{attrs:{label:"标题",prop:"title"}},[o("el-input",{attrs:{autocomplete:"off"},model:{value:e.form.title,callback:function(t){e.$set(e.form,"title",t)},expression:"form.title"}})],1),o("el-form-item",{attrs:{label:"正文",prop:"cooperationDescribe"}},[o("el-input",{attrs:{type:"textarea",autosize:{minRows:2,maxRows:4},placeholder:"请输入内容"},model:{value:e.form.cooperationDescribe,callback:function(t){e.$set(e.form,"cooperationDescribe",t)},expression:"form.cooperationDescribe"}})],1),o("el-form-item",{attrs:{label:"话题",prop:"topicCooperationId"}},[o("el-select",{attrs:{filterable:"","value-key":"id","default-first-option":"",remote:"","reserve-keyword":"",clearable:!0,placeholder:"请选择",multiple:""},model:{value:e.form.topicCooperationId,callback:function(t){e.$set(e.form,"topicCooperationId",t)},expression:"form.topicCooperationId"}},e._l(e.topicList,(function(e){return o("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),o("el-form-item",{attrs:{label:"图片",prop:"cooperationPicture","label-width":e.formLabelWidth}},[o("el-upload",{attrs:{action:"#","list-type":"picture-card","on-preview":e.handlePreview,"on-remove":e.handleRemove,"auto-upload":!1,"file-list":e.form.cooperationPicture,"on-change":e.handleSmallPicSuccess}},[o("i",{staticClass:"el-icon-plus"})]),o("el-dialog",{attrs:{visible:e.dialogVisibleImg,top:"0",center:"",modal:!1},on:{"update:visible":function(t){e.dialogVisibleImg=t}}},[o("img",{attrs:{width:"100%",src:e.dialogImageUrl,alt:""}})])],1),o("el-form-item",{attrs:{label:"视频",prop:"cooperationVideo","label-width":e.formLabelWidth}},[o("el-upload",{staticClass:"avatar-uploader",attrs:{id:"uploder",action:"#","on-change":e.handleVideoSuccess,"before-upload":e.beforeUploadVideo,"show-file-list":!1,"auto-upload":!1}},[""!=e.form.cooperationVideo?o("video",{staticClass:"avatar",attrs:{src:e.form.cooperationVideo,controls:"controls"}},[e._v(" 您的浏览器不支持视频播放 ")]):""==e.form.cooperationVideo?o("i",{staticClass:"el-icon-plus avatar-uploader-icon"}):e._e()])],1),o("el-form-item",{attrs:{label:"审核状态"}},[o("el-radio-group",{model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},[o("el-radio",{attrs:{label:1}},[e._v("通过")]),o("el-radio",{attrs:{label:2}},[e._v("不通过")])],1)],1)],1),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{on:{click:e.formCancal}},[e._v("取 消")]),o("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.formSubmit("form")}}},[e._v("确 定")])],1)],1)],1)},a=[],n=o("5530"),r=(o("d81d"),o("d3b7"),o("25f0"),o("e9c4"),o("a710")),l=o("91b6"),c={data:function(){return{formLabelWidth:"220",viplist:null,list:null,listLoading:!0,pageNum:1,pageSize:10,keyWords:"",vipid:0,form:{title:"",cooperationDescribe:"",cooperationPicture:[],cooperationVideo:"",topicCooperationId:[]},count:0,dialogFormVisible:!1,dialogQueryVip:!1,dialogImageUrl:"",dialogVisibleImg:!1,ids:[],topicList:[]}},created:function(){this.fetchData(),this.fetchTopicDAta()},methods:{fetchTopicDAta:function(){var e=this;Object(r["f"])().then((function(t){var o=t.data,i=o.map((function(e){return{value:e.id,label:e.title}}));e.topicList=i}))},handleClose:function(){this.formCancal()},beforeUploadVideo:function(e){var t=e.size/1024/1024<50;return-1==["video/mp4","video/ogg","video/flv","video/avi","video/wmv","video/rmvb","video/mov"].indexOf(e.type)?(this.$message("请上传正确的视频格式"),!1):t?void 0:(this.$message("视频大小不能超过50MB"),!1)},handleVideoSuccess:function(e,t){this.uploadImg(e,3)},handleSmallPicSuccess:function(e,t){this.uploadImg(e,2)},handleRemove:function(e,t){this.form.cooperationPicture=t},handlePreview:function(e){this.dialogImageUrl=e.url,this.dialogVisibleImg=!0},uploadImg:function(e,t){var o=this,i=new FormData;i.append("file",e.raw),Object(l["a"])(i).then((function(e){if(console.log(e.data.data),2===t){var i=o.form.cooperationPicture;i.push({uid:Math.random().toString(16).substring(6),url:e.data.data}),o.form=Object(n["a"])(Object(n["a"])({},o.form),{},{cooperationPicture:i})}else if(3===t){var a=e.data.data;o.form=Object(n["a"])(Object(n["a"])({},o.form),{},{cooperationVideo:a})}}))},formSubmit:function(){var e=this,t=this.form;t.topicCooperationId=t.topicCooperationId.toString();var o=[],i=[];t.cooperationPicture&&t.cooperationPicture.length>0&&(i=t.cooperationPicture.map((function(e){return e.url}))),t.cooperationPicture=JSON.stringify(i),t.cooperationVideo&&o.push(t.cooperationVideo),t.cooperationVideo=JSON.stringify(o),t.userId||(t.userId=1),t.vipId||(t.vipId=1),Object(r["a"])(this.form).then((function(t){console.log(t),200==t.data.code&&(e.$message({message:t.data.msg,type:"success"}),e.sizeChangeHandle(e.pageSize),e.dialogFormVisible=!1)}))},fetchData:function(){var e=this;this.listLoading=!0;var t=this.pageNum,o=this.pageSize,i=this.keyWords,a=this.vipid,n={pageNum:t,pageSize:o,keyWords:i,vipid:a};Object(r["d"])(n).then((function(t){console.log(t);var o=t.data.data.records;e.list=o,e.count=t.data.data.total,e.listLoading=!1}))},sizeChangeHandle:function(e){this.pageSize=parseInt(e),this.pageNum=1,this.fetchData()},currentChangeHandle:function(e){this.pageNum=parseInt(e),this.fetchData()},prevClickHandle:function(){this.pageNum-=1,this.fetchData()},nextClickHandle:function(){this.currentPage+=1,this.fetchData()},handleEdit:function(e){this.dialogFormVisible=!0,e.cooperationPicture&&e.cooperationPicture.length>0&&(e.cooperationPicture=JSON.parse(e.cooperationPicture).map((function(e,t){return{uid:t,url:e}}))),e.topicCooperationId=e.topicList.map((function(e){return e.id})),e.cooperationVideo=JSON.parse(e.cooperationVideo)[0]||"",console.log(e),this.form=e},handleQuery:function(e){this.dialogFormVisible=!0,this.form=e},handleDelete:function(e){var t=[];t.push(e),this.ids=t,this.open()},formCancal:function(){this.form={},this.$message({message:"取消编辑"}),this.fetchData(),this.dialogFormVisible=!1},handleAddLearn:function(){this.form={title:"",cooperationDescribe:"",cooperationPicture:[],cooperationVideo:"",topicCooperationId:[]},this.dialogFormVisible=!0},handleDeleteLearn:function(){this.open()},handleSelectionChange:function(e){var t=e.map((function(e){return e.id}));this.ids=t},open:function(){var e=this;this.$confirm("此操作将永久删除选中的话题","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",center:!0}).then((function(){Object(r["deleteGroupType"])(e.ids).then((function(t){console.log(t,"删除回调"),200==t.data.code&&(e.$message({type:"success",message:"删除成功!"}),e.sizeChangeHandle(e.pageSize))}))})).catch((function(){e.$message({type:"info",message:"已取消删除"})}))}}},s=c,u=(o("7c27"),o("2877")),d=Object(u["a"])(s,i,a,!1,null,"e4630690",null);t["default"]=d.exports}}]);