<template>
  <div class="app-container">
    <p>{{ value1 }}</p>
    <!-- 搜索筛选 -->
    <el-form :inline="true" class="user-search">
      <!-- <el-form-item label="下单时间">
        <el-date-picker size="small" v-model="value2" type="datetimerange" :picker-options="pickerOptions"
          range-separator="至" start-placeholder="开始日期" @change="change" end-placeholder="结束日期" align="right">
        </el-date-picker>
      </el-form-item> -->
      <!-- <el-form-item label="">
        <el-select size="small" placeholder="全部" @change="change1" v-model="input2">
          <el-option label="上架" value="1"></el-option>
          <el-option label="下架" value="0"></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item prop="Product.catld">
        <el-select size="small" v-model="input3" placeholder="全部类型" @change="change2">
          <el-option size="small" v-for="item in classify" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input size="small" placeholder="请输入内容" v-model="input4" class="el-input_ss1">
          <el-button size="small" slot="append" icon="el-icon-search" @click="change3"></el-button>
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-button size="small" type="primary" icon="el-icon-plus" @click="increase()">添加</el-button>
        <el-button size="small" type="info" @click="Reload" icon="el-icon-refresh-left">重新加载</el-button>

        <!-- <el-button size="small" type="success" @click="grounding">批量上架</el-button>
        <el-button size="small" type="warning" @click="undercarriage">批量下架</el-button>
        <el-button size="small" type="danger" @click="Batchdeletion">批量删除</el-button> -->
        <!-- <el-button @click="leading">批量导出</el-button> -->




      </el-form-item>
    </el-form>
 
    <el-table :data="data" border fit highlight-current-row @selection-change="handleSelectionChange">
      <el-table-column align="center" type="selection" width="60">
      </el-table-column>
      <el-table-column align="center" label="ID" width="95">
        <template slot-scope="scope">{{ scope.row.id }}</template>
      </el-table-column>
      <el-table-column align="center" prop="img" label="类型" width="100">
        <template slot-scope="scope">
          <el-tag>{{ scope.row.cateName }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="名称">
        <template slot-scope="scope">{{ scope.row.name }}</template>
      </el-table-column>
      <el-table-column label="封面图" align="center">
        <template slot-scope="scope">
                    <el-popover placement="bottom" trigger="hover" width="300">
                <img :src="scope.row.image" width="100%" />
                <img slot="reference" :src="scope.row.image" :alt="scope.row.coverPic" style="max-height: 60px;max-width: 60px; padding: 5px" />
              </el-popover>
                </template>
      </el-table-column>
      <el-table-column label="售价" width="100px" align="center">
        <template slot-scope="scope"> <el-link type="danger">{{ scope.row.money }}元</el-link></template>
      </el-table-column>
      <el-table-column label="库存" width="100" align="center">
        <template slot-scope="scope">{{ scope.row.stock }}</template>
      </el-table-column>
      <el-table-column label="状态" width="120" align="center">
        <template slot-scope="scope">

          <el-tag size="mini" type="success" v-if="scope.row.status == '1'">已上架</el-tag>
          <el-tag size="mini" v-if="scope.row.status == '0'" type="info">已下架</el-tag>
          <!-- <span>|</span> -->
          <!-- <el-link v-if="scope.row.status == '1'" type="primary" @click="grounding1(scope.row.id)">下架</el-link> -->
          <!-- <el-link v-if="scope.row.status == '0'" type="primary" @click="undercarriage1(scope.row.id)">上架</el-link> -->
        </template>

      </el-table-column>

      <el-table-column label="排序" width="120" align="center">
        <template slot-scope="scope">{{ scope.row.sort }}</template>
      </el-table-column>

      <el-table-column fixed="right" label="操作" width="100" align="center">
        <template slot-scope="scope">
          <el-button size="small" type="primary" @click="handleEditzt(scope.row)">修改</el-button>
          <!-- <el-button size="small" type="danger" @click="handleDelete(scope.row.id)">删除</el-button> -->

        </template>
      </el-table-column>
    </el-table>
    <el-row>
      <el-pagination background style="margin-top:30px" :page-size="pageSize" layout="total,prev,pager,next,jumper"
        :total="total" @current-change="handleCurrentChange1" />
    </el-row>
    <!-- 添加 -->
    <el-dialog top="6vh" @close="formCancal1" width="60%" :title="dialogTitle" :visible.sync="specialShow"
      :close-on-click-modal="false" :close-on-press-escape="false">
      <div class="import-dialog">
        <el-form label-width="120px" ref="editForm">
        <el-form-item label="分类:" prop="Product.cateName">
          <el-select v-model="Product.cateName" placeholder="请选择分类">
            <el-option size="small" v-for="item in classify" :key="item.id" :label="item.name"
              :value="item.name"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否上下架">
    <el-radio-group v-model="Product.status">
      <el-radio :label="1">上架</el-radio>
      <el-radio :label="2">下架</el-radio>
    </el-radio-group>
  </el-form-item>
      </el-form>
      <el-form label-width="120px" ref="editForm">
        <el-form-item label="名称:" prop="Product.name">
          <el-input v-model="Product.name" size="small" auto-complete="off" placeholder="请输入名称"></el-input>
        </el-form-item>
      </el-form>
      <el-form label-width="120px" ref="editForm">
        <el-form-item label="描述:" prop="Product.description">
          <el-input  type="textarea" v-model="Product.description" size="small" auto-complete="off" placeholder="请输入描述"></el-input>
        </el-form-item>
      </el-form>
   
      <el-form label-width="120px" ref="editForm">
        <el-form-item label="排序:" prop="Product.sort">
          <template slot-scope="scope">
            <el-input v-model="Product.sort" size="small" auto-complete="off" placeholder="请输入序列号"></el-input>
            <!-- <div style="font-size: 12px">排序按升序排列</div> -->
          </template>
        </el-form-item>
      </el-form>
 
      <el-form label-width="120px" ref="editForm">
        <el-form-item label="限购数量:" prop="Product.quota">
          <template slot-scope="scope">
            <el-input v-model="Product.quota" size="small" auto-complete="off" placeholder="请输入限购数量"></el-input>
            <!-- <div style="font-size: 12px">设置为0则不限购，大于0则等于对应的限购数量</div> -->
          </template>
        </el-form-item>
      </el-form>
    
      <el-form label-width="120px" ref="editForm">
        <el-form-item label="封面图:" prop="Product.image">
          <el-upload action="#" list-type="picture-card" :on-preview="handlePreview" :on-remove="handleRemove1"
            :auto-upload="false" :file-list="bookUrllist" multiple :on-change="handleSmallPicSuccess">
            <i class="el-icon-plus" />
          </el-upload>
          <el-dialog :visible.sync="dialogVisibleImg" top="0" center :modal="false">
            <img width="100%" :src="Product.image" alt>
          </el-dialog>
        </el-form-item>
      </el-form>
  
      <el-form label-width="120px" ref="editForm">
        <el-form-item label="价格:" prop="Product.money">
          <template slot-scope="scope">
            <el-input v-model="Product.money" size="small" auto-complete="off" placeholder="请输入价格"></el-input>
          </template>
        </el-form-item>
      </el-form>
     
      <el-form label-width="120px" ref="Product.stock">
        <el-form-item label="库存:" prop="Product.stock">
          <template slot-scope="scope">
            <el-input v-model="Product.stock" size="small" auto-complete="off" placeholder="请输入库存"></el-input>
          </template>
        </el-form-item>
      </el-form>
      <el-form label-width="120px" ref="Product.useAttr">
        <el-form-item label="规格:">
          <el-table @click="formCancal" border ref="singleTable" :data="tableData" highlight-current-row
          @current-change="handleCurrentChange" style="width: 100%">
          <el-table-column width="120" v-for="(item, index) in tableOption" :key="index" :label="item.label"
            align="center">
            <template slot-scope="scope">
              <el-input @blur="change6(scope.$index, index)" clearable v-model="scope.row.attr_list[index].attr_name"
                placeholder="请输入"></el-input>
            </template>
          </el-table-column>
          <!-- <el-table-column align="center" property="specifications" label="规格" width="120">
              <template slot-scope="scope">
                <el-input @blur="change6(scope.$index, scope.row)" v-model="scope.row.specifications"
                  placeholder="请输入规格"></el-input>
              </template>
            </el-table-column> -->
          <el-table-column align="center" property="size" label="尺码" width="120">
            <template slot-scope="scope">
              <el-input clearable v-model="scope.row.size" placeholder="请输入尺码"></el-input>
            </template>
          </el-table-column>
          <el-table-column align="center" property="color" label="颜色">
            <template slot-scope="scope">
              <el-input clearable v-model="scope.row.color" placeholder="请输入颜色"></el-input>
            </template>
          </el-table-column>
          <el-table-column align="center" property="price" label="价格">
            <template slot-scope="scope">
              <el-input clearable v-model="scope.row.price" placeholder="请输入价格"></el-input>
            </template>
          </el-table-column>
          <el-table-column align="center" property="num" label="库存">
            <template slot-scope="scope">
              <el-input clearable v-model="scope.row.num" placeholder="请输入库存"></el-input>
            </template>
          </el-table-column>
          <!-- <el-table-column align="center" property="pic" label="规格图片" width="200">
            <template slot-scope="scope">
              <div @click="handleCheck(scope.$index, scope.row)" style="display: flex"><el-input class="el-input_ss"
                  v-model="scope.row.pic" placeholder="请选择图片"></el-input>
                <el-upload class="upload-demo" action="#" :on-preview="handlePreview1" :on-remove="handleRemove1"
                  :on-change="handleSmallPicSuccess1" multiple :limit="3">
                  <el-button icon="el-icon-upload"></el-button>
                </el-upload>
              </div>
              　　　　<img v-if="scope.row.pic" :src="scope.row.pic" width="40" height="40" class="head_pic" />

            </template>
          </el-table-column> -->
          <el-table-column align="center"  label="操作" width="150">
            <template slot-scope="scope">
              <el-button @click="add()"  size="small">增加</el-button>
              <el-button @click="handleClick(scope.$index, scope.row)" type="danger" size="small">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        </el-form-item>
      </el-form>
      <el-form label-width="120px" ref="Product.integral">

        <el-form-item label="图文详情:" prop="Product.detail">
          <!-- <VueEditor ref="vueEditor" :config="config" /> -->
          <wangEditor v-model="wangEditorDetail" :isClear="isClear" @change="wangEditorChange"></wangEditor>

        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="formCancal1">取消</el-button>
        <el-button type="primary" @click="RechargeRole('form')">确 定</el-button>
      </div>
      </div>
  
    </el-dialog>
   
  </div>
</template>
  
<script>
import {ingredientslProducts,  ingredientsCats,addIngredientsl} from '@/api/FC'

import { upload } from '@/api/upload'
import { formatDate_RQ } from '@/utils/time'
import VueEditor from 'vue-word-editor'
import 'quill/dist/quill.snow.css'
import wangEditor from "@/components/wangEditor/wangEditor.vue";

import { log } from 'console'
export default {
  components: {
    // VueEditor,
    wangEditor
  },
  data() {
    return {
      isClear: false,//设置为true的时候，这个可以用this.wangEditorDetail=''来替代
      wangEditorDetail: "",
valnum:1,
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }],
      },
      dialogTitle: '新增',
      specialShow: false,
      specialShowgg: false,
      formList: {},
      data: [],
      pageSize: 10,
      value1: "",
      value2: "",
      input2: '',
      input3: '',
      input4: '',
      handleSelectionChangeList: [],
      bookUrllist: [],
      dialogVisibleImg: false,
      formData: {
        startTime: '',
        endTime: ""
      },
      Product: {
        cateId: '',
        cateName:'',
        name: '',
        description: '',
        sort: '',
        quota: '',
        image: '',
        money: '',
        stock: '',
        attr:[],
        status:1,

        detail: '',
      },
      specifications: {
        price: '',
        assort: '',
        stock: '',
        price: '',
        Article: '',
        url: ''
      },
      total: 1,
      // 富文本器的配置
      config: {
        // 上传图片的配置
        uploadImage: {
          // url: "/api/upload/fileUpload", //服务器地址
          url: `${process.env.VUE_APP_BASE_API}/uploadCloud?filename=11111`,
          name: 'file', // 参考接口文档的文件上传的参数
          // headers: { Authorization: localStorage.getItem("mytoken") }, //配置token
          // res是结果，insert方法会把内容注入到编辑器中，res.data.url是资源地址
          uploadSuccess(res, insert) {
            console.log(res) // 是否上传成功的响应结果和url地址
            insert(res.data.yunUrl)
          }
        },
        // 上传视频的配置
        // uploadVideo: {
        //     url: `${process.env.VUE_APP_BASE_API}upload/fileUpload`,
        //     name: 'file',
        //     uploadSuccess(res, insert) {
        //         insert(res.data.data)
        //     }
        // }
      },
      classify: [],
      obj: {
        size: '默认',
        color: '默认',
        price: '',
        num:'',
      },
      tableData: [{
        size: '默认',
        color: '默认',
        price: '',
        num:'',
      }],
      currentRow: null,
      input5: '',
      tableOption: [],
      IMGindex: '',
      attrGroup_ID: [],
      baseUrl : process.env.VUE_APP_BASE_API + "/oe_addIngredientsl_.csp",



    }
  },
  created() {
    const SEDJJXCX_LHFW_UserInformation_HT = JSON.parse(decodeURIComponent(window.localStorage.getItem('SEDJJXCX_LHFW_UserInformation_HT')))
    this.SEDJJXCX_LHFW_UserInformation_HT = SEDJJXCX_LHFW_UserInformation_HT
    this.fetchData()

    this.getGoods()

  },
  methods: {
     wangEditorChange(val) {
      this.Product.detail=val;
    },

    // 重新加载
    Reload() {
      this.value1 = '';
      this.value2 = '';
      this.input2 = '';
      this.input3 = '';
      this.input4 = '';
      this.input5 = '';
      this.getGoods(1);

    },
    
    fetchData(val = 1) {
 

      const params = {
                                    dbName: window.localStorage.getItem('JJHTDBnmame'),

                curPage: val
            }
            ingredientsCats(params).then(response => {
                this.total=response.data.totalNum;
                if (response.data.code == '1') {
                    this.classify = response.data.list
                }else{
                    this.classify = response.data.list
                }
            })
    },
    getGoods(val = 1) {
      const params = {
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: val
      }
      if (this.value2 && this.value2[0] && this.value2[1]) {
        params.starTime = formatDate_RQ(this.value2[0]);
        params.endTime = formatDate_RQ(this.value2[1]);
      }

      if (this.input2) {
        params.status = this.input2;
      }
      if (this.input3) {
        params.cateId = this.input3;
      }

      if (this.input4) {
        params.name = this.input4;
      }

      ingredientslProducts(params).then(response => {
        this.total = response.data.totalNum
        if (response.data.code == '1') {
          this.data = response.data.list
        }
      })
    },
    handleCurrentChange1(val) {
      this.valnum=val;
      this.getGoods(val)

    },
    handleCurrentChange(val) {
    },
    // 删除
    handleDelete(row) {
      this.$confirm('此操作将永久删除', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          const params = {
            dbName: 'mati', listid: row
          }
          DeleteGoods(params).then(response => {
            if (response.data.code == '1') {
              this.$message.success('删除成功')

              this.getGoods()
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    handleSelectionChange(val) {
      this.handleSelectionChangeList = val;
    },
    // 批量删除
    Batchdeletion() {
      if ((this.handleSelectionChangeList && !this.handleSelectionChangeList.length)) {
        this.$message({
          type: 'warning',
          message: '请选择你删除的选项'
        })
      } else {
        const list = [];
        for (var i of this.handleSelectionChangeList) {
          list.push(i.id)
        }
        var a = list.join(",");
        this.$confirm('此操作将永久删除', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            const params = {
              dbName: 'mati', listid: a
            }
            DeleteGoods(params).then(response => {
              if (response.data.code == '1') {
                this.$message.success('删除成功')

                this.getGoods()
              }
            })
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            })
          })
      }

    },
    handlePreview(file) {
      this.Product.coverPic = file.url
      this.dialogVisibleImg = true
    },
    handlePreview1(file) {
      this.specifications.url = file.url
      this.dialogVisibleImg = true
    },
    handleRemove1(file, fileList) {
      // 移除

    },
    handleSmallPicSuccess1(res, file, fileList) {
      if (file && file.length == 2) {
        file.shift()
      }
      this.uploadImg(res, 2);

    },
    handleSmallPicSuccess(res, file, fileList) {
      if (file && file.length == 2) {
        file.shift()
      }
      this.uploadImg(res, 1);

    },
    uploadImg(file, type) {
      let formData = new FormData()
      formData.append('file', file.raw)
      upload(formData).then(response => {
        if (type == '1') {
          this.Product.image = response.data.yunUrl;

        } else {
          this.tableData[this.IMGindex].pic = response.data.yunUrl;

        }
      })

    },
    handleCurrentChange(val) {
      this.currentRow = val;
    },
    //选中规格
    selectBox(val) {
      this.specialShowgg = true;
      this.Product.useAttr = true;
      console.log(this.tableOption);
    },
    // 删除规格
    handleClick(index, row) {
      if (index == '0') {
        this.$message({
          type: 'warning',
          message: '规格中最后一项不可删除！'
        })
      } else {
        this.tableData.splice(index, 1)
      }
    },
    // 点击取消
    formCancal() {
      this.specialShowgg = false;
      // this.tableOption = [];
      // this.bookUrllist = [];
      if (this.tableData && this.tableData[0] && this.tableData[0].num == '') {
        // this.Product.useAttr = false;
      }
      if(this.dialogTitle == '新增'){
        this.tableData = [{
          size: '默认',
        color: '默认',
        price: '',
        num:'',
      }]
      }
    
    },
    formCancal1() {
      this.specialShow = false;
      this.Product = {};
      this.Product.status = 1;
      this.wangEditorDetail='';
      this.bookUrllist = [];
      this.tableOption = []
      this.tableData = [{
        size: '默认',
        color: '默认',
        price: '',
        num:'',
      }]
    },

    increase() {
      this.specialShow = true;
    },
    
    //添加
    RechargeRole() {
              this.Product.cateId = this.classify.find(item => item.name == this.Product.cateName).id;

      if (this.Product.catld == '') {
        this.$message({
          showClose: true,
          message: '请选择分类！',
          type: 'warning'
        });
        return
      }
      if (this.Product.name == '') {
        this.$message({
          showClose: true,
          message: '请输入名称！',
          type: 'warning'
        });
        return
      }
   
      if (this.Product.confineCount == '') {
        this.$message({
          showClose: true,
          message: '请输入限购数量！',
          type: 'warning'
        });
        return
      } if (this.Product.coverPic == '') {
        this.$message({
          showClose: true,
          message: '请选择图片！',
          type: 'warning'
        });
        return
      }
    
    

      if (this.Product.money == '') {
        this.$message({
          showClose: true,
          message: '请输入价格！',
          type: 'warning'
        });
        return
      }
      if (this.Product.stock == '') {
        this.$message({
          showClose: true,
          message: '请输入库存！',
          type: 'warning'
        });
        return
      }
      if (this.Product.detail == '') {
        this.$message({
          showClose: true,
          message: '请填写图文详情！',
          type: 'warning'
        });
        return
      }
      this.Product.attr = JSON.stringify(this.tableData);
      var params = new URLSearchParams();
      params.append('cateId', this.Product.cateId);
      params.append('cateName', this.Product.cateName);
      params.append('status', this.Product.status);
      params.append('name', this.Product.name);
      params.append('description', this.Product.description);
      params.append('sort', this.Product.sort);
      params.append('quota', this.Product.quota);
      params.append('image', this.Product.image);
      params.append('money', this.Product.money);
      params.append('stock', this.Product.stock);
      params.append('attr', this.Product.attr);
      params.append('detail', this.Product.detail);
            params.append('dbName', window.localStorage.getItem('JJHTDBnmame'));

      params.append('id', this.Product.id ? this.Product.id: '');
      // this.axios.post('http://*************:9528/api/oe_editGoods2_.csp', params,).then(res => { 
      this.axios.post(this.baseUrl, params,).then(res => { 
        if (res.data.code == '1') {
          this.$message({
            type: 'success',
            message: this.dialogTitle == '修改' ? '修改成功' : '添加成功'

          })

          this.specialShowgg = false
          this.specialShow = false;
          this.Product = {}
          this.bookUrllist = []
          this.tableOption = [];
          this.input5 = '';
          this.getGoods(this.valnum)

          }else {
          this.$message({
            type: 'warning',
            message: this.dialogTitle == '修改' ? '修改失败' : '添加失败'

          })
          }
       }).catch(err => { 错误处理逻辑 })
       return
      EditGoods(this.Product).then(response => {
        if (response.data.code == '1') {
          this.$message({
            type: 'success',
            message: this.dialogTitle == '修改' ? '修改成功' : '添加成功'

          })
          this.getGoods()
          this.specialShowgg = false
          this.$refs.vueEditor.editor.root.innerHTML = '';
          this.specialShow = false;
          this.Product = {}
          this.bookUrllist = []
          this.tableOption = [];
          this.input5 = '';
          this.tableData = [{
            attr_list: [],
            num: '',
            price: '',
            no: '',
            pic: '',
            specifications: ''
          }]

        } else {
          this.$message({
            type: 'warning',
            message: this.dialogTitle == '修改' ? '修改失败' : '添加失败'

          })
          this.$refs.vueEditor.editor.root.innerHTML = ''
        }
      })

    },
    // 修改
    handleEditzt(row) {
      this.dialogTitle = '修改';
      this.specialShow = true;
      this.Product = JSON.parse(JSON.stringify(row));
      this.tableData = JSON.parse(row.attr);
 
      this.wangEditorDetail=row.detail;
    
      this.bookUrllist = [
        {
          url: row.image
        }
      ]
    },

    // 时间筛选
    change() {
      this.formData.startTime = formatDate_RQ(this.value2[0])
      this.formData.endTime = formatDate_RQ(this.value2[1])
      const params = {
        dbName: "mati", curPage: 1,
        starTime: this.formData.startTime,
        endTime: this.formData.endTime,
      }
      if (this.input2) {
        params.status = this.input2;
      }
      if (this.input3) {
        params.cateId = this.input3;
      }
      if (this.input4) {
        params.name = this.input4;
      }
      Goods(params).then(response => {
        this.total = response.data.totalNum
        if (response.data.code === '1') {
          this.formData.startTime = ''
          this.formData.endTime = ''
          this.data = response.data.list
          // this.value2 = '';
        } else {
          this.data = [];
          // this.value2 = '';
        }
      })
    },
    // 上下架筛选
    change1(row) {
      const params = {
        dbName: "mati", curPage: 1,
        status: row
      }

      if (this.value2 && this.value2[0] && this.value2[1]) {
        params.starTime = formatDate_RQ(this.value2[0]);
        params.endTime = formatDate_RQ(this.value2[1]);
      }

      if (this.input3) {
        params.cateId = this.input3;
      }
      if (this.input4) {
        params.name = this.input4;
      }
      Goods(params).then(response => {
        this.total = response.data.totalNum
        if (response.data.code === '1') {
          this.data = response.data.list
        } else {
          this.data = [];
        }
      })
    },
    // 全部类型筛选
    change2(row) {
      const params = {
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: 1,
        cateId: row
      }
      if (this.value2 && this.value2[0] && this.value2[1]) {
        params.starTime = formatDate_RQ(this.value2[0]);
        params.endTime = formatDate_RQ(this.value2[1]);
      }

      if (this.input2) {
        params.status = this.input2;
      }

      if (this.input4) {
        params.name = this.input4;
      }
      ingredientslProducts(params).then(response => {
        this.total = response.data.totalNum
        if (response.data.code === '1') {
          this.data = response.data.list
          // this.value2 = '';
        } else {
          this.data = [];
          // this.value2 = '';
        }
      })
    },
    // 名字筛选
    change3() {
      const params = {
                            dbName: window.localStorage.getItem('JJHTDBnmame'),


         curPage: 1,
         keyword: this.input4
      }
      if (this.value2 && this.value2[0] && this.value2[1]) {
        params.starTime = formatDate_RQ(this.value2[0]);
        params.endTime = formatDate_RQ(this.value2[1]);
      }

      if (this.input2) {
        params.status = this.input2;
      }
      if (this.input3) {
        params.cateId = this.input3;
      }

      ingredientslProducts(params).then(response => {
        this.total = response.data.totalNum
        if (response.data.code === '1') {
          this.data = response.data.list
          // this.value2 = '';
        } else {
          this.data = [];
          // this.value2 = '';
        }
      })
    },
    // 批量导出
    // leading(){
    //   if (this.handleSelectionChangeList && this.handleSelectionChangeList.length) {
    //     let url = 'https://cdn.juesedao.cn/huiya/682fe11b0a2648d19f07e9d8b78279c2'
    //     const a = document.createElement('a')
    //     a.href = url
    //     a.download = '测试'// 下载后文件名
    //     a.style.display = 'none'
    //     document.body.appendChild(a)
    //     a.click() // 点击下载
    //     document.body.removeChild(a) // 下载完成移除元素

    //   } else {
    //     this.$message({
    //       type: 'warning',
    //       message: '请选择你导出的选项'
    //     })
    //   }
    // },
    // 批量上架
    grounding() {
      if (this.handleSelectionChangeList && this.handleSelectionChangeList.length) {
        const list = [];
        for (var i of this.handleSelectionChangeList) {
          list.push(i.id)
        }
        var a = list.join(",");
        const params = {
          dbName: 'mati',
          listid: a,
          status: 1
        }
        UpAndDown(params).then(response => {
          if (response.data.code == '1') {
            this.$message({
              type: 'success',
              message: '上架成功'

            })
            this.getGoods()

          }
        })
      } else {
        this.$message({
          type: 'warning',
          message: '请选择你上架的选项'
        })
      }


    },
    // 批量下架
    undercarriage() {
      if (this.handleSelectionChangeList && this.handleSelectionChangeList.length) {
        const list = [];
        for (var i of this.handleSelectionChangeList) {
          list.push(i.id)
        }
        var a = list.join(",");
        const params = {
          dbName: 'mati',
          listid: a,
          status: 0
        }
        UpAndDown(params).then(response => {
          if (response.data.code == '1') {
            this.$message({
              type: 'success',
              message: '下架成功'

            })
            this.getGoods()

          }
        })
      } else {
        this.$message({
          type: 'warning',
          message: '请选择你下架的选项'
        })
      }

    },
    // 单个下架
    grounding1(row) {

      const params = {
        dbName: 'mati',
        listid: row,
        status: 0
      }
      UpAndDown(params).then(response => {
        if (response.data.code == '1') {
          this.$message({
            type: 'success',
            message: '下架成功'

          })
          this.getGoods()

        }
      })




    },
    // 单个上架
    undercarriage1(row) {


      const params = {
        dbName: 'mati',
        listid: row,
        status: 1
      }
      UpAndDown(params).then(response => {
        if (response.data.code == '1') {
          this.$message({
            type: 'success',
            message: '上架成功'

          })
          this.getGoods()

        }
      })


    },


    handleCheck(index, row) {
      this.IMGindex = index;
    },
    // 添加标题
    // change5() {
    //   if (this.input5) {
    //     this.tableOption.push({
    //       'label': this.input5
    //     })
    //     const params = {
    //       dbName: 'mati',
    //       attrGroup: this.input5
    //     }
    //     QueryAttrGroup(params).then(response => {
    //       if (response.data.code == '1') {
    //         this.attrGroup_ID.push(response.data.record.id);
    //         for (let i in this.tableData) {
    //           this.tableData[i].attr_list.push({
    //             id: response.data.record.id,
    //             attr_name: "",
    //           })
    //         }
    //       }
    //     })
    //   } else {
    //     this.$message({
    //       type: 'warning',
    //       message: '请填写规格'
    //     })
    //   }

    // },
    change5() {
      if (this.input5) {
        this.tableOption.push({
          'label': this.input5
        })

        // this.tableData[0].attr_list = this.tableOption
        // return
        const params = {
          dbName: 'mati',
          attrGroup: this.input5
        }
        QueryAttrGroup(params).then(response => {
          if (response.data.code == '1') {
            this.attrGroup_ID.push(response.data.record.id);
            // this.attrGroupID = response.data.record.id;
            for (let i in this.tableData) {
              this.tableData[i].attr_list.push({
                attr_group_id: response.data.record.id,//表头id
                attr_group_name: this.input5,//表头
                attr_id: "",
                attr_name: ''
              })
            }

          }
        })
      } else {
        this.$message({
          type: 'warning',
          message: '请填写规格'
        })
      }

    },
    // change6(index, row) {
    //   const params = {
    //     dbName: 'mati',
    //     attrGroup: 5,
    //     attrName: row.specifications
    //   }
    //   QueryAttr(params).then(response => {
    //     if (response.data.code == '1') {
    //       this.tableData[index].attr_list.push({
    //         attr_id: response.data.record.id,
    //         attr_name: row.specifications,
    //       })
    //     }
    //   })
    // },
    change6(index, i) {
      const params = {
        dbName: 'mati',
        attrGroup: this.attrGroup_ID[i], // this.attrGroupID,
        attrName: this.tableData[index].attr_list[i].attr_name,
      }
      QueryAttr(params).then(response => {
        if (response.data.code == '1') {
          this.tableData[index].attr_list[i].attr_id = response.data.record.id
        }
      })
    },
    // 添加一行
    add() {
     
      let obj = {
        size: '默认',
        color: '默认',
        price: '',
        num:'',
        pic: ''
      }
      this.tableData.push(obj)
    },
    // 添加规格确定
    specificationsff() {
      return

      if (this.tableData && this.tableData[0] && this.tableData[0].num == '') {
        this.$message({
          type: 'warning',
          message: '请输入规格组和规格值'
        })
        return
      }
      for (let a in this.tableData) {
        if (this.tableData[a].attr_list && this.tableData[a].attr_list.length == 0) {
          let obj = {
            attr_group_id: 1,//表头id
            attr_group_name: '规格',//表头
            attr_id: 1,
            attr_name: '默认'
          }
          this.tableData[a].attr_list.push(obj)
        }
      }

      this.Product.attr = JSON.stringify(this.tableData);
      this.specialShowgg = false;
      this.input5 = '';
    },




  },
};
</script>
  
<style lang="scss" scoped>
.el-row {
  margin-bottom: 20px;
}

.el-row:last-child {
  margin-bottom: 0;
}

.el-tag+.el-tag {
  margin-left: 10px;
}



.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}

.input-new-tag {
  width: 300px;
  margin-left: 10px;
  vertical-align: bottom;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.el-button--medium {
  margin-top: 10px;
  margin-left: 600px;
  height: 45px;
  width: 100px;
}

.el-input_ss {
  width: 200px;
}

.btn_jia {
  position: relative;
  margin-left: 8px;
  margin-top: 20px;
}

.btn_jia:hover {
  font-weight: 600;
  text-decoration: underline;
}

.btn_jia::after {
  content: "";
  width: 2px;
  height: 20px;
  background: #000;
  position: absolute;
  top: 10px;
  right: 33px;
}

.status_shop {
  margin-right: 8px;
}

// 分页
.paging {
  width: 100%;
  height: 100%;
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

// 功能按钮
.function_btn_box {
  width: 100%;
  display: flex;
  margin-right: 10px;
  margin-bottom: 20px;
}

.input-with-select {
  margin-left: 50px;
}

.float_rigth {
  float: right;
}
.import-dialog {
  height: auto;
  max-height: 80vh;
  overflow-y: auto;
}
// .el-input.inp {
//   width: auto !important;
// }
.el-input_ss1{
  margin-top: 4px;
}
.dialog-footer{
  float: right;
}
</style>
  