<template>
    <div style="background: #f5f7f9 !important; height:400px">
      <img src="https://cdn.juesedao.cn/sed/0cb02cc34bb6430f927046b293a76860" alt="" style="width: 100%;">
    </div>
  </template>
  
  <script>
  import * as echarts from 'echarts'
  require('echarts/lib/component/legend')
  import ICountUp from 'vue-countup-v2';
  import TpScroll from "@/store/tp-scroll.js";
  import { shouyeData, chartzxt, chartyzx, monthData } from '@/api/detailed'
  import { log } from 'console';
  export default {
    components: {
      ICountUp
    },
    data() {
      return {
        delay: 1000,
        endVal1: 0,
        endVal2: 0,
        endVal3: 0,
        endVal4: 0,
        endVal5: 0,
        endVal6: 0,
        endVal7: 0,
        endVal8: 0,
        endVal9: 0,
        endVal10: 0,
        options: {
          useEasing: true,
          useGrouping: true,
          separator: ',',
          decimal: '.',
          prefix: '',
          suffix: ''
        },
        detailed: {},
        strike: {},
        striketype: 1,
        salesList: [],
        salestype: 1,
        UserInformation_MPHT: {}
      }
    },
    mounted() {
     
      const UserInformation_MPHT = JSON.parse(decodeURIComponent(window.localStorage.getItem('UserInformation_MPHT')))
      this.UserInformation_MPHT = UserInformation_MPHT;
    },
    created() {
     
      // TpScroll.RemoveScroll(); //禁止body滚动
    },
    methods: {
      initEcharts3() {
        const posList = [
          'left',
          'right',
          'top',
          'bottom',
          'inside',
          'insideTop',
          'insideLeft',
          'insideRight',
          'insideBottom',
          'insideTopLeft',
          'insideTopRight',
          'insideBottomLeft',
          'insideBottomRight'
        ];
  
        app.config = {
          rotate: 90,
          align: 'left',
          verticalAlign: 'middle',
          position: 'insideBottom',
          distance: 15,
          onChange: function () {
            const labelOption = {
              rotate: app.config.rotate,
              align: app.config.align,
              verticalAlign: app.config.verticalAlign,
              position: app.config.position,
              distance: app.config.distance
            };
            myChart.setOption({
              series: [
                {
                  label: labelOption
                },
                {
                  label: labelOption
                },
                {
                  label: labelOption
                },
                {
                  label: labelOption
                }
              ]
            });
          }
        };
        const labelOption = {
          show: true,
          position: app.config.position,
          distance: app.config.distance,
          align: app.config.align,
          verticalAlign: app.config.verticalAlign,
          rotate: app.config.rotate,
          formatter: '{c}  {name|{a}}',
          fontSize: 16,
          rich: {
            name: {}
          }
        };
        const params = {
          dbName: "mati"
        }
        
        let attr1=[];
        let attr2=[];
        let attr3=[];
        var Y = [];
        var C = [];
        var G = [];
        var S = [];
        monthData(params).then(response => {
          if (response.data.code === '1') {
            let chongzhi = response.data.chongzhi;
            let goumai = response.data.goumai;
            let zengsong = response.data.zengsong;
            chongzhi.forEach((item) => {//充值
              Y.push(item.date)
            })
            for (let i in Y) {
              let obj = {
                date: Y[i],
                integral: 0,
              };
              for (let j in chongzhi) {
                if (Y[i] == chongzhi[j].date) {
                  obj.integral = chongzhi[j].integral;
                }
              }
              attr1.push(obj);
            }
            attr1.forEach((item)=>{
              S.push(item.integral)
            })
            for (let i in Y) {//购买
              let obj = {
                date: Y[i],
                integral: 0,
              };
              for (let j in goumai) {
                if (Y[i] == goumai[j].date) {
                  obj.integral = goumai[j].integral;
                }
              }
              attr2.push(obj);
            }
            attr2.forEach((item)=>{
              G.push(item.integral)
            })
            for (let i in Y) {//赠送
              let obj = {
                date: Y[i],
                integral: 0,
              };
              for (let j in zengsong) {
                if (Y[i] == zengsong[j].date) {
                  obj.integral = zengsong[j].integral;
                }
              }
              attr3.push(obj);
            }
            attr3.forEach((item)=>{
              C.push(item.integral)
            })
           
          }
        })
        const option = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          legend: {
            data: ['总部赠送', '用户充值', '用户购买']
          },
  
          xAxis: [
            {
              type: 'category',
              axisTick: { show: false },
              data: Y
            }
          ],
          yAxis: [
            {
              type: 'value'
            }
          ],
          series: [
            {
              name: '总部赠送',
              type: 'bar',
              barGap: 0,
              label: labelOption,
              emphasis: {
                focus: 'series'
              },
              data: C
            },
            {
              name: '用户充值',
              type: 'bar',
              label: labelOption,
              emphasis: {
                focus: 'series'
              },
              data: S
            },
            {
              name: '用户购买',
              type: 'bar',
              label: labelOption,
              emphasis: {
                focus: 'series'
              },
              data: G
            },
  
          ]
        };
  
        setTimeout(() => {
          const myChart = echarts.init(document.getElementById("rigth_1"));
          myChart.setOption(option);
          //随着屏幕大小调节图表
          window.addEventListener("resize", () => {
            myChart.resize();
          });
  
        }, 1000)
      },
      Getuser(val = 1) {
        const params = {
          dbName: "mati"
        }
        shouyeData(params).then(response => {
          if (response.data.code === '1') {
            this.detailed = response.data,
              this.endVal1 = response.data.users;
            this.endVal2 = response.data.goods;
            this.endVal3 = response.data.orders;
            this.endVal4 = response.data.integral1;
            this.endVal5 = response.data.integral2;
            this.endVal6 = response.data.integral3;
            this.endVal7 = response.data.integral4;
            this.endVal8 = response.data.count1;
            this.endVal9 = response.data.count2;
            this.endVal10 = response.data.count3;
            this.strike = response.data.order_sum[0]
            this.salesList = response.data.goods_1
          }
        })
      },
  
      Echarts_zxt() {
        var Line_chart_name = [];
        var money = [];
        var num = [];
        const params = {
          dbName: "mati"
        }
  
        chartzxt(params).then(response => {
          if (response.data.code === '1') {
            for (var i = 0; i < response.data.record.length; i++) {
              var item = response.data.record[i];
              Line_chart_name.push(item.payDate);
              money.push(item.money);
              num.push(item.num);
            }
  
          }
  
        })
        this.chartLine = echarts.init(document.getElementById('chartLineBox'));
        // 指定图表的配置项和数据
        var option = {
          tooltip: {              //设置tip提示
            trigger: 'axis'
          },
  
          legend: {               //设置区分（哪条线属于什么）
            data: ['成交量', '成交额']
          },
          color: ['#e3bfbd', '#566672'],       //设置区分（每条线是什么颜色，和 legend 一一对应）
          xAxis: {                //设置x轴
            type: 'category',
            boundaryGap: false,     //坐标轴两边不留白
            // data: ['2019-1-1', '2019-2-1', '2019-3-1', '2019-4-1', '2019-5-1', '2019-6-1', '2019-7-1',],
            data: Line_chart_name,
            name: '',           //X轴 name
            nameTextStyle: {        //坐标轴名称的文字样式
              color: '#FA6F53',
              fontSize: 16,
              padding: [0, 0, 0, 20]
            },
            axisLine: {             //坐标轴轴线相关设置。
              lineStyle: {
                color: '#000000',
              }
            }
          },
          yAxis: {
            name: '',
            nameTextStyle: {
              color: '#000000',
              fontSize: 10,
              padding: [0, 0, 10, 0]
            },
            axisLine: {
              lineStyle: {
                color: '#000000',
              }
            },
            type: 'value'
          },
          series: [
            {
              name: '成交量',
              data: money,
              type: 'line',               // 类型为折线图
              lineStyle: {                // 线条样式 => 必须使用normal属性
                normal: {
                  color: 'red',
                }
              },
            },
            {
              name: '成交额',
              data: num,
              type: 'line',
              lineStyle: {
                normal: {
                  color: '#000000',
                }
              },
            }
          ]
        };
  
        setTimeout(() => {
          this.chartLine.setOption(option);
  
        }, 1000)
        // 使用刚指定的配置项和数据显示图表。
      },
      Echarts_yxt() {
        var nickname = []
        var sumPrice = []
        const params = {
          dbName: "mati"
        }
        chartyzx(params).then(response => {
          if (response.data.code === '1') {
            for (var i = 0; i < response.data.record.length; i++) {
              var item = response.data.record[i];
              nickname.push(item.nickname);
              sumPrice.push(item.sumPrice);
            }
          }
        })
        // 基本柱状图
        const option = {
          xAxis: {
            data: nickname,
            axisLabel: {
              interval: 0,
              show: true,
              textStyle: {
                color: "#a9a9a9", //更改坐标轴文字颜色
                fontSize: 10 //更改坐标轴文字大小
              },
              rotate: 20,
              // formatter: function (v) {
              //   var date = new Date(v);
              //   return `${('0' + date.getHours()).slice(-2)}:${('0' + date.getMinutes()).slice(-2)}`;
              //   // return `${('0' + date.getHours()).slice(-2)}:${('0' + date.getMinutes()).slice(-2)}:${('0' + date.getSeconds()).slice(-2)}`;
              // },
              showMinLabel: true,//显示最小值
              showMaxLabel: true,//显示最大值
            }
          },
          tooltip: {              //设置tip提示
            trigger: 'axis'
          },
          yAxis: {},
          series: [
            {
              type: "bar", //形状为柱状图
              data: sumPrice,
              name: '购物力',
              itemStyle: {
                shadowColor: "#5470c6",
                shadowBlur: 3,
  
              },
              barWidth: "50%"
            }
          ]
        };
  
        setTimeout(() => {
          const myChart = echarts.init(document.getElementById("mychart"));
          myChart.setOption(option);
          //随着屏幕大小调节图表
          window.addEventListener("resize", () => {
            myChart.resize();
          });
  
        }, 1000)
      },
      select(row) {
        this.salestype = row;
        if (row == '1') {
          this.strike = this.detailed.order_sum[0]
  
        } else if (row == '2') {
          this.strike = this.detailed.order_sum[1]
  
        } else if (row == '3') {
          this.strike = this.detailed.order_sum[2]
  
        } else if (row == '4') {
          this.strike = this.detailed.order_sum[3]
  
        }
      },
      sales(row) {
        this.striketype = row;
        if (row == '1') {
          this.salesList = this.detailed.goods_1
  
        } else if (row == '2') {
          this.salesList = this.detailed.goods_2
  
  
        } else if (row == '3') {
          this.salesList = this.detailed.goods_3
  
        } else if (row == '4') {
          this.salesList = this.detailed.goods_4
  
  
        }
      },
      skip(row) {
        if (row == '1') {
          this.$router.push({ path: '/cooperation/cooperation' })
  
        } else if (row == '2') {
          this.$router.push({ path: '/personnel/audit' })
  
        } else if (row == '3') {
          this.$router.push({ path: '/learnCircle/learnCircle' })
  
        } else if (row == '4') {
          this.$router.push({ path: '/cooperation/Recharge' })
  
        } else if (row == '5') {
          this.$router.push({ path: '/cooperation/AccessRecords' })
  
        }
        else if (row == '6') {
          this.$router.push({ path: '/personnel/audit' })
  
        }
        else if (row == '7') {
          this.$router.push({ path: '/learnCircle/learnCircle', query: { Operationclasstype: 2 } })
  
        }
        else if (row == '8') {
          this.$router.push({ path: '/learnCircle/learnCircle', query: { Operationclasstype: 5 } })
  
  
        }
  
      }
    }
  
  
  
  
  
  
  }
  </script>
  
  <style scoped>
  .app-main {
    background: #f5f7f9 !important;
    padding: 20px;
  }
  
  .panel {
    padding: 0 1rem;
    background: #fff;
  }
  
  .panel .panel-header {
    border-bottom: 1px solid #eee;
  }
  
  .panel-header .nav {
    font-size: 14px;
  
  }
  
  .panel-header {
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  
  .panel-header .rz {
    display: flex;
    font-size: 14px;
    align-items: center;
  
  }
  
  .panel-body {
    padding: 10px 0;
    display: flex;
    align-items: center;
    text-align: center;
    justify-content: space-around;
  }
  
  .panel {
    padding: 0.5rem 1rem;
    background: #fff;
    margin-left: 20px;
    margin-top: 10px;
  }
  
  .panel-2-item .sp {
    display: flex;
    align-items: center;
    text-align: center;
    margin-right: 60px;
  }
  
  .panel-2-item .item-icon {
    width: 42px;
    height: 42px;
    margin-right: 10px;
    margin-top: 50px;
  }
  
  .row {
    margin-left: 20px;
    display: flex;
    align-items: center;
    justify-content: space-around;
  }
  
  .panel-2-item:last-child {
    border-right: none;
  
  }
  
  .panel-2-item {
    height: 8rem;
    border-right: 1px solid #eee;
    line-height: 8rem !important;
  }
  
  .panel .panel-header {
    border-bottom: 1px solid #eee;
  }
  
  .panel-header .nav_rigth {
    display: flex;
    align-items: center;
  }
  
  .panel-header .nav_rigth div {
    padding: 1rem 0;
    margin: 0 1rem -1px 1rem;
    height: 54px;
  }
  
  .bottom_border {
    border-bottom: 2px solid #449be6;
  
  }
  
  .panel-body .userNum {
    cursor: pointer !important;
  }
  
  .Echarts_zxt {
    width: 97.3%;
    background: #ffff;
    height: 300px;
    margin-left: 20px;
    overflow: hidden;
  }
  
  .Echarts_yzx {
    width: 97.3%;
    background: #ffff;
    height: 400px;
    margin-left: 20px;
    overflow: hidden;
  }
  
  .panel .ks {
    display: flex;
    align-items: center;
    text-align: center;
    justify-content: space-evenly;
  
  }
  
  .panel .ks .item {
    margin-top: 50px;
    padding-bottom: 30px;
    text-align: center;
  }
  
  .panel .ks .item .sl {
    margin-bottom: 10px;
    color: #f6cf5e;
  }
  </style>
  