import request from '@/utils/request'

export function getproductTypeList(data) {
  return request({
    url: '/oe_productTypeList_.csp',
    method: 'post',
    data
  })
}


export function AddcreateProductType(data) {
    return request({
      url: '/oe_createProductTypeGD_.csp',
      method: 'post',
      data
    })
  }
  

  export function delProductType(data) {
    return request({
      url: '/oe_delProductType_.csp',
      method: 'post',
      data
    })
  }
  export function getSeries(data) {
    return request({
      url: '/oe_getSeries_.csp',
      method: 'post',
      data
    })
  }
  

  export function AddcreateProductSeries(data) {
    return request({
      url: '/oe_createProductSeries_.csp',
      method: 'post',
      data
    })
  }

    

  export function getproductList(data) {
    return request({
      url: '/oe_productList_.csp',
      method: 'post',
      data
    })
  }
  
  
  
  
      

  export function createProduct(data) {
    return request({
      url: '/oe_createProduct_.csp',
      method: 'post',
      data
    })
  }
  
  
  
  
  
  export function delProduct(data) {
    return request({
      url: '/oe_deleteEntitys_.csp',
      method: 'post',
      data
    })
  }
  
  
  
  
    
  export function getqueryCaseTypes(data) {
    return request({
      url: '/oe_queryCaseTypes_.csp',
      method: 'post',
      data
    })
  }
  
  
  export function delCaseType(data) {
    return request({
      url: '/oe_delCaseType_.csp',
      method: 'post',
      data
    })
  }
  
  
    
  export function createCaseType(data) {
    return request({
      url: '/oe_createCaseType_.csp',
      method: 'post',
      data
    })
  }
  
  export function getcaseList(data) {
    return request({
      url: '/oe_caseList_.csp',
      method: 'post',
      data
    })
  }
  
  export function getAllProduct(data) {
    return request({
      url: '/oe_getAllProduct_.csp',
      method: 'post',
      data
    })
  }
  

  export function getcreateCase(data) {
    return request({
      url: '/oe_createCase_.csp',
      method: 'post',
      data
    })
  }
  

  export function delCase(data) {
    return request({
      url: '/oe_delCase_.csp',
      method: 'post',
      data
    })
  }
  


  export function getfinishBagList(data) {
    return request({
      url: '/oe_finishBagList_.csp',
      method: 'post',
      data
    })
  }

  export function addcreateFinishBag(data) {
    return request({
      url: '/oe_createFinishBag_.csp',
      method: 'post',
      data
    })
  }

  export function strategyCURD(data) {
    return request({
      url: '/oe_strategyCURD_.csp',
      method: 'post',
      data
    })
  }

  export function brandCRUD(data) {
    return request({
      url: '/oe_brandCRUD_.csp',
      method: 'post',
      data
    })
  }

  export function storeAccountCURD(data) {
    return request({
      url: '/oe_storeAccountCURDGD_.csp',
      method: 'post',
      data
    })
  }
  export function fileTypeCURD(data) {
    return request({
      url: '/oe_fileTypeCURD_.csp',
      method: 'post',
      data
    })
  }

  export function QueryAttrGroup(params) {
    return request({
      url: 'oe_queryAttrGroupGD_.csp',
      method: 'post',
      params
    })
  }
  export function QueryAttr(params) {
    return request({
      url: 'oe_queryAttrGD_.csp',
      method: 'post',
      params
    })
  }

  export function queryProductByIdGD(params) {
    return request({
      url: 'oe_queryProductByIdGD_.csp',
      method: 'post',
      params
    })
  }

  export function getqueryAllProduct(params) {
    return request({
      url: 'oe_queryAllProduct_.csp',
      method: 'post',
      params
    })
  }

  export function changeScene(params) {
    return request({
      url: 'oe_changeScene_.csp',
      method: 'post',
      params
    })
  }
  export function changeType(params) {
    return request({
      url: 'oe_changeType_.csp',
      method: 'post',
      params
    })
  } 
  export function changeConfig(params) {
    return request({
      url: 'oe_changeConfig_.csp',
      method: 'post',
      params
    })
  } 

  export function createProductGD(data) {
    return request({
      url: '/oe_saveproductGD_.csp',
      method: 'post',
      data
    })
  }
  export function syncPicturesGD(data) {
    return request({
      url: '/oe_syncPicturesGD_.csp',
      method: 'post',
      data
    })
  }

  export function selectEntityOne(data) {
    return request({
      url: '/oe_selectEntityOne_.csp',
      method: 'post',
      data
    })
  }
  export function cashCouponList(data) {
    return request({
      url: '/oe_cashCouponList_.csp',
      method: 'post',
      data
    })
  }

  export function getCouponById(data) {
    return request({
      url: '/oe_getCouponById_.csp',
      method: 'post',
      data
    })
  }

  export function approvalCoupon(data) {
    return request({
      url: '/oe_approvalCoupon_.csp',
      method: 'post',
      data
    })
  }
  export function queryAllSndysgd(data) {
    return request({
      url: '/oe_queryAllSndysgd_.csp',
      method: 'post',
      data
    })
  }