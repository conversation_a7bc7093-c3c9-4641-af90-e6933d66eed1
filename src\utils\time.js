export function formatDate(value) {
  if (typeof (value) === 'undefined') {
    return ''
  } else {
    const date = new Date(parseInt(value))
    const y = date.getFullYear()
    let MM = date.getMonth() + 1
    MM = MM < 10 ? ('0' + MM) : MM
    let d = date.getDate()
    d = d < 10 ? ('0' + d) : d
    let h = date.getHours()
    h = h < 10 ? ('0' + h) : h
    let m = date.getMinutes()
    m = m < 10 ? ('0' + m) : m
    let s = date.getSeconds()
    s = s < 10 ? ('0' + s) : s
    return y + '-' + MM + '-' + d + ' ' + h + ':' + m + ':' + s
  }

}

export function formatDate_RQ(value) {

  const date = value

  var year = date.getFullYear();
  var month = date.getMonth() + 1;
  var day = date.getDate();
  if (month < 10) {
    month = "0" + month;
  }
  if (day < 10) {
    day = "0" + day;
  }
  var currentdate = year + "-" + month + "-" + day;
  return currentdate
}

export function Url() {//富文本上传
  let url='https://ai.medodt.com/uploadcloud/?filename=11111'
  // let url='http://192.168.0.138:9528/api/uploadCloud?filename=11111'
  return url 
}
