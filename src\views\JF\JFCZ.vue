<template>
  <div class="app-container">

    <!-- 添加按钮 -->
    <el-form :inline="true" class="user-search">
      <el-form-item>
        <!-- <el-button size="mini" @click="handleEdit1" type="primary">添加</el-button> -->

      </el-form-item>
      <el-form-item prop="Product.catld">
        <el-select size="mini" v-model="input3" placeholder="充值状态" @change="change2">
          <el-option size="mini" v-for="item in modulelist" :key="item.id" :label="item.cateName"
            :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input size="mini" placeholder="请输入内容" v-model="input4" class="el-input_ss1">
          <el-button size="mini" slot="append" icon="el-icon-search" @click="change3"></el-button>
        </el-input>
      </el-form-item>
      <el-form-item>
        <!-- <el-button size="mini" type="primary" icon="el-icon-plus" @click="increase()">添加</el-button> -->

        <el-button size="mini" type="primary" @click="Reload" icon="el-icon-refresh-left">重新加载</el-button>
        <!-- <el-button @click="leading">批量导出</el-button> -->




      </el-form-item>
    </el-form>
    <!-- 表格 -->
    <el-table :data="data" border fit highlight-current-row>
      <el-table-column align="center" label="序号">
        <template slot-scope="scope">{{ scope.$index + 1 }}</template>
      </el-table-column>
      <el-table-column align="center" prop="name" label="微信头像">
        <template slot-scope="scope">
          <el-popover placement="bottom" trigger="hover" width="300">
            <img :src="scope.row.wxPhoto" width="100%" />
            <img slot="reference" :src="scope.row.wxPhoto" :alt="scope.row.wxPhoto"
              style="max-height: 40px;max-width:40px; padding: 5px" />
          </el-popover>
        </template>

      </el-table-column>
      <el-table-column align="center" label="微信名称" width="200">
        <template slot-scope="scope">{{ scope.row.wxName }}</template>

        <!-- <template slot-scope="scope">
                  <div v-if="scope.row.cateId == '1'">首页</div>
                  <div v-if="scope.row.cateId == '2'">个人中心</div>
                  <div v-if="scope.row.cateId == '3'">其他</div>
              </template> -->
      </el-table-column>
      <el-table-column align="center" label="手机号" width="200">
        <template slot-scope="scope">{{ scope.row.phone }}</template>

        <!-- <template slot-scope="scope">
                  <div v-if="scope.row.cateId == '1'">首页</div>
                  <div v-if="scope.row.cateId == '2'">个人中心</div>
                  <div v-if="scope.row.cateId == '3'">其他</div>
              </template> -->
      </el-table-column>
      <el-table-column align="center" label="门店" width="300">
        <template slot-scope="scope">{{ scope.row.storeName }}</template>


      </el-table-column>
      <el-table-column align="center" label="订单号" width="500">
        <template slot-scope="scope">{{ scope.row.orderNum }}</template>


      </el-table-column>
      <el-table-column align="center" label="充值金额">
        <template slot-scope="scope">{{ scope.row.money }}</template>


      </el-table-column>
      <el-table-column align="center" label="充值积分">
        <template slot-scope="scope">{{ scope.row.integral }}</template>


      </el-table-column>
       <el-table-column align="center" label="充值时间" width="300">
        <template slot-scope="scope">{{ scope.row.createTime }}</template>


      </el-table-column>
     
     
      <el-table-column fixed="right" align="center" prop="name" label="充值状态" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status == '1'" type="success">充值成功</el-tag>
          <el-tag v-if="scope.row.status == '0'" type="danger">未支付</el-tag>
        </template>

      </el-table-column>

    

    
      <!-- <el-table-column fixed="right" width="200" label="操作" align="center">
        <template slot-scope="scope">
          <el-button size="mini" @click="handleEdit(scope.$index, scope.row)">编辑</el-button>

          <el-button size="mini" @click="deleteUser(scope.$index, scope.row)" type="danger">删除</el-button>

        </template>
      </el-table-column> -->
      <!-- </el-table-column> -->
    </el-table>
    <el-row>
      <el-pagination :current-page.sync="currentPage" background style="margin-top:30px" :page-size="pageSize"
        layout="total,prev,pager,next,jumper" :total="total" @current-change="handleCurrentChange1" />

    </el-row>
    <!-- 编辑界面 -->
    <el-dialog :title="title" :visible.sync="editFormVisible" @close="closeDialog" width="50%">
      <el-form label-position="rigth" label-width="100px">
        <el-form-item label="模块">
          <el-select size="mini" v-model="special.cateId" placeholder="模块">
            <el-option size="mini" v-for="item in modulelist" :key="item.id" :label="item.cateName"
              :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="礼品名称:">
          <el-input style="width: 100%;" size="mini" v-model="special.proInfo" auto-complete="off"
            placeholder="请输入名称"></el-input>
        </el-form-item>
        <el-form-item label="封面图:">
          <el-upload action="#" list-type="picture-card" :on-preview="handlePreview" :on-remove="handleRemove1"
            :auto-upload="false" :file-list="bookUrllist" multiple :on-change="handleSmallPicSuccess">
            <i class="el-icon-plus" />
          </el-upload>
          <el-dialog :visible.sync="dialogVisibleImg" top="0" center :modal="false">
            <img width="100%" :src="special.productImage" alt>
          </el-dialog>
        </el-form-item>


        <el-form-item label="所需积分:">
          <el-input style="width: 100%;" size="mini" v-model="special.integralNum" auto-complete="off"
            placeholder="请输入所需积分"></el-input>
        </el-form-item>



        <el-form-item label="限购数量:">
          <el-input style="width: 100%;" size="mini" v-model="special.quota" auto-complete="off"
            placeholder="请输入限购数量"></el-input>
        </el-form-item>
        <el-form-item label="库存量:">
          <el-input style="width: 100%;" size="mini" v-model="special.productStock" auto-complete="off"
            placeholder="请输入产品库存量"></el-input>
        </el-form-item>
        <el-form-item label="是否上架:">
          <el-radio v-model="special.status" :label="1">上架</el-radio>
          <el-radio v-model="special.status" :label="2">下架</el-radio>
        </el-form-item>
        <el-form-item label="排序:">
          <el-input style="width: 100%;" size="mini" v-model="special.sort" auto-complete="off"
            placeholder="请输入排序"></el-input>
        </el-form-item>
        <!-- <el-form-item label="详情:">
              <el-upload action="#" list-type="picture-card" :on-preview="handlePreview" :on-remove="handleRemove1"
                :auto-upload="false" :file-list="bookUrllist1" multiple :on-change="handleSmallPicSuccess1">
                <i class="el-icon-plus" />
              </el-upload>
              <el-dialog :visible.sync="dialogVisibleImg" top="0" center :modal="false">
                <img width="100%" :src="special.detail" alt>
              </el-dialog>
            </el-form-item> -->
            <el-form-item label="详情:" prop="form.productUrl">
                    <!-- <el-upload :on-success="uploadSuccess" action="#" list-type="picture-card" :on-preview="handlePreview" :on-remove="handleRemove2"
                      :auto-upload="false" :file-list="bookUrllist1" multiple :on-change="handleSmallPicSuccess1">
                      <i class="el-icon-plus" />
                    </el-upload> -->
                    <el-upload   class="el_upload_above" multiple :limit="limitnum" :action="action" ref="upload"
                      list-type="picture-card" :http-request="uploadSectionFile" :auto-upload="true"
                      :file-list="bookUrllist1" :on-error="uploadFileError" :on-success="uploadFileSuccess"
                      :on-exceed="exceedFile"  :before-upload="handleBeforeUpload"  :on-remove="handleRemove2">
                      <i class="el-icon-plus" />
                    </el-upload>
                  </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="closeDialog">取消</el-button>
        <el-button size="mini" type="primary" class="title" @click="submitForm()">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { createIntergralGoods, delIntergralGoods, rechargeRecords, allCates, getIntergralCate } from '@/api/JF'
import { upload } from '@/api/upload'
import { Loading } from 'element-ui';

export default {
  data() {
    return {
      editFormVisible: false,
      dialogVisibleImg: false,
      currentPage: 1,
      pageSize: 10,
      total: 1,
      data: [],
      title: '添加',
      special: {
        id: '',
        productImage: "",
        status: 1,
        sortId: '',
        remark: '',
        integralNum: '',
        productStock: '',
        proInfo: '',
        peoNum: "",
        quota: '',
        productCate: '',
        cateId: '',
        detail: [],
        sort: '',
      },
      modulelist: [
        {
          id:0,cateName:'未付款'
        },
        {
          id:1,cateName:'已付款'
        }
      ],
      SEDJJXCX_LHFW_UserInformation_HT: {},
      bookUrllist: [],
      bookUrllist1: [],
      input3: '',
      input4: '',
      baseUrl : process.env.VUE_APP_BASE_API + "/oe_createIntergralGoods_.csp",
      limitnum: 10,
      fileList: [],
      progressPercent:0,
      action:process.env.VUE_APP_BASE_API + "/uploadCloud?filename=11111",
      ruleForm:{
        packageSize:"",
        packageUrl:"http://localhost:9529/api/uploadCloud?filename=11111"
      }

    }
  },
  created() {
    const SEDJJXCX_LHFW_UserInformation_HT = JSON.parse(decodeURIComponent(window.localStorage.getItem('SEDJJXCX_LHFW_UserInformation_HT')))
    this.SEDJJXCX_LHFW_UserInformation_HT = SEDJJXCX_LHFW_UserInformation_HT;
    // this.fetchData1()
    this.fetchData()
  },
  methods: {
    handleRemove2(file) {
      // 1.获取将要删除图片的临时路径
      const filePath = file.url;
      // 2.从pics数组中，找到图片对应的索引值
      const i = this.special.detail.findIndex((x) => x.url === filePath);
      // 3.调用splice方法，移除图片信息
      this.special.detail.splice(i, 1);
    },
    handleBeforeUpload(file){

},
    exceedFile(res, file, fileList) {
      this.$message.error('只能上传' + this.limitnum + '个文件');
    },
    uploadFileSuccess(res, file, fileList) {
      if (res.code == 0) {
        fileList.forEach((att) => {
          if (att.response) {
            let obj = {
              url: ""
            }
            if(file.response.yunUrl){
              obj.url = file.response.yunUrl
            this.special.detail.push(obj)
            }
           
          }
        })

      }

    },
    uploadFileError(res, file, fileList) {
    },
    Reload() {
      this.input3 = ''
      this.input4 = ''
      const params = {
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: 1
      }
      rechargeRecords(params).then(response => {
        this.total = response.data.totalNum

        if (response.data.code == '1') {
          this.data = response.data.list
        } else {
          this.data = response.data.list

        }
      }).catch((err) => {
        console.log();
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    change3() {
      const params = {
        keyword: this.input4,
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: 1
      }
      if (this.input3) {
        params.status = this.input3;
      }

      rechargeRecords(params).then(response => {
        this.total = response.data.totalNum

        if (response.data.code == '1') {
          this.data = response.data.list
        } else {
          this.data = response.data.list
        }
      }).catch((err) => {
        console.log();
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    change2() {
      const params = {
        status: this.input3,
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: 1
      }
      if (this.input4) {
        params.keyword = this.input4;
      }

      rechargeRecords(params).then(response => {
        if (response.data.code == '1') {
          this.total = response.data.totalNum

          this.data = response.data.list
        } else {
          this.data = response.data.list
        }
      }).catch((err) => {
        console.log();
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    fetchData1(val = 1) {
      const params = {
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: val
      }
      allCates(params).then(response => {
        this.total = response.data.totalNum

        if (response.data.code == '1') {
          this.modulelist = response.data.records
        } else {
          this.modulelist = response.data.records

        }
      }).catch((err) => {
        console.log();
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    handleCurrentChange1(val) {
      this.fetchData(val)
    },
    fetchData(val = 1) {
      const params = {
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: val
      }
      rechargeRecords(params).then(response => {
        this.total = response.data.totalNum
        if (response.data.code == '1') {
          this.data = response.data.list
        } else {
          this.data = response.data.list

        }
      })
    },
    handleEdit1() {
      this.title = '添加';
      this.editFormVisible = true;
      this.special.id = '';
    },
    handleEdit(index, row) {
      this.editFormVisible = true;
      this.title = '修改'
      this.special = row;
      if (row.productImage) {
        this.bookUrllist = [
          {
            url: row.productImage
          }
        ]
      }
      // if (row.detail) {
      //   this.bookUrllist1=JSON.parse(row.detail)
      //   // let fruits = row.detail.split(",");
      //   // this.bookUrllist1 = fruits.map(row => ({
      //   //   url: row
      //   // }));

      // }
      if (row.detail == '') {
        this.special.detail = []

          
          } else {
            this.special.detail = JSON.parse(row.detail);
            this.bookUrllist1 = this.special.detail;
          }
      console.log(this.bookUrllist);
      console.log(this.bookUrllist1);
      return
      if (row != undefined && row != 'undefined') {
        this.title = '修改'
        this.special.id = row.id
        this.special.name = row.name
      } else {
        this.title = '添加'
        this.special.id = ''
      }
    },
    // 关闭编辑、增加弹出框
    closeDialog() {

      this.editFormVisible = false;
      // this.special = {};
    },
    // 编辑、增加页面保存方法
    submitForm() {
      if (this.title == '添加') {
        this.special.productCate = this.modulelist.find(item => item.id == this.special.cateId).cateName;
      }

   

      if (this.special.integralNum == '') {
        this.$message({
          showClose: true,
          message: '请输入所需积分！',
          type: 'warning'
        });
        return
      }
      if (this.special.sort == '') {
        this.$message({
          showClose: true,
          message: '请输入排序！',
          type: 'warning'
        });
        return
      }
      if (this.special.cateId == '') {
        this.$message({
          showClose: true,
          message: '请选择模块！',
          type: 'warning'
        });
        return
      }

      if (this.special.quota == '') {
        this.$message({
          showClose: true,
          message: '请输入限购数量！',
          type: 'warning'
        });
        return
      }
    
      if (this.special.productStock == '') {
        this.$message({
          showClose: true,
          message: '请输入库存量！',
          type: 'warning'
        });
        return
      }
      if (this.special.productImage == '') {
        this.$message({
          showClose: true,
          message: '请上传产品封面图！',
          type: 'warning'
        });
        return
      }


 if (this.special.detail && this.special.detail.length == 0) {
        this.$message({
          showClose: true,
          message: '请上传详情图！',
          type: 'warning'
        });
        return
      }
   
      if(this.special.detail && this.special.detail.length !=0){
          let obj = {}
      this.special.detail = this.special.detail.reduce(function (item, next) {
        obj[next.url] ? '' : (obj[next.url] = true && item.push(next))
        return item
      }, [])
        }
      var params = new URLSearchParams();
      params.append('integralNum', this.special.integralNum);
      params.append('id', this.special.id);
      params.append('sort', this.special.sort);
      params.append('cateId', this.special.cateId);
      params.append('status', this.special.status);
      params.append('peoNum', this.special.peoNum);
      params.append('detail', JSON.stringify(this.special.detail));
      params.append('quota', this.special.quota);
      params.append('productStock', this.special.productStock);
            params.append('dbName', window.localStorage.getItem('JJHTDBnmame'));

      params.append('productImage', this.special.productImage);
      params.append('productCate', this.special.productCate);
      params.append('proInfo', this.special.proInfo);

      this.axios.post(this.baseUrl, params,).then(res => {
        if (res.data.code == '1') {
          this.editFormVisible = false
          this.special.sortId = '';
          this.special.remark = '';
          this.bookUrllist = []
          this.bookUrllist1 = []
          this.fetchData()
          this.$message({
            type: 'success',
            message: this.title == '修改' ? '修改成功！' : '创建成功！'
          })
        } else {
          this.$message({
            type: 'warning',
            message: '创建失败！'
          })
        }
      },()=>{
        this.Product.id = res.data.id;
      }).catch(err => { 错误处理逻辑 })
    


    },

    deleteUser(index, row) {
      this.$confirm('此操作将永久删除, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const params = {
          id: row.id,
                              dbName: window.localStorage.getItem('JJHTDBnmame'),

        }; delIntergralGoods(params).then(response => {
          if (response.data.code == '1') {
            this.fetchData()
          }
        })
        this.$message({
          type: 'success',
          message: '删除成功!'
        });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });

    },
    handlePreview(file) {
      // 放大
      this.special.productImage = file.productImage
      this.dialogVisibleImg = true
    },
    handleRemove1(file, fileList) {
     // 1.获取将要删除图片的临时路径
     const filePath = file.url;
      // 2.从pics数组中，找到图片对应的索引值
      const i = this.special.detail.findIndex((x) => x.url === filePath);
      // 3.调用splice方法，移除图片信息
      this.special.detail.splice(i, 1);
    },
    handleSmallPicSuccess(res, file, fileList) {
      if (file && file.length == 2) {
        file.shift()
      }
      this.uploadImg(res, 1);

    },
    handleSmallPicSuccess1(res, file, fileList) {
      if (file && file.length == 2) {
        file.shift()
      }
      this.uploadImg(res, 2);

    }
    , uploadImg(file, type) {
      this.loading = Loading.service({
        lock: true,
        text: '图片上传中........',
        // background: 'rgba(0, 0, 0, 0.7)',
        target: document.querySelector('#loadingDiv')
      })
      let formData = new FormData()
      formData.append('file', file.raw)
      upload(formData).then(response => {
        setTimeout(() => {
          this.loading.close()

        }, 2000);
        if (type == 1) {
          this.special.productImage = response.data.yunUrl;

        } else {
          let obj = {
              url: response.data.yunUrl
            }
            this.special.detail.push(obj);

        }

      }).catch((err) => {
        console.log(err);

      });


    },
  }
}
</script>

<style lang="scss" scoped>
.el-row {
  margin-bottom: 20px;
}

.el-row:last-child {
  margin-bottom: 0;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.el-button--medium {
  margin-top: 10px;
  margin-left: 600px;
  height: 45px;
  width: 100px;
}

.el-input {
  width: 200px;
}

// .el-input.inp {
//   width: auto !important;
// }
.el-input_ss1 {

  margin-top: 6px;
}
</style>
