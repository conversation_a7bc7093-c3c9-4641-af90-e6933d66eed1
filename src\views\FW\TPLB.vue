<template>
  <div class="app-container">

      <!-- 添加按钮 -->
      <el-form :inline="true" class="user-search">
          <el-form-item>
              <el-button size="mini" @click="handleEdit1" type="primary">添加</el-button>

          </el-form-item>
      </el-form>
      <!-- 表格 -->
      <el-table :data="data" border fit highlight-current-row>
          <el-table-column align="center" label="序号" width="80">
              <template slot-scope="scope">{{ scope.$index + 1 }}</template>
          </el-table-column>
       
          
          <el-table-column align="center"  label="标题">
            <template slot-scope="scope">{{ scope.row.title  }}</template>

              <!-- <template slot-scope="scope">
                  <div v-if="scope.row.module == '1'">首页</div>
                  <div v-if="scope.row.module == '2'">个人中心</div>
                  <div v-if="scope.row.module == '3'">其他</div>
              </template> -->
          </el-table-column>

         
      
      <el-table-column align="center" prop="name" label="是否上线">
              <template slot-scope="scope">
                  <el-tag v-if="scope.row.status == '1'">是</el-tag>
                  <el-tag v-if="scope.row.status == '0'" type="danger">否</el-tag>
              </template>

          </el-table-column>
          <el-table-column align="center" label="排序" >
              <template slot-scope="scope">{{ scope.row.sort }}</template>
          </el-table-column>
        
          <el-table-column fixed="right" label="操作" align="center">
              <template slot-scope="scope">
                  <el-button size="mini" @click="handleEdit(scope.$index, scope.row)">编辑</el-button>

                  <el-button size="mini" @click="deleteUser(scope.$index, scope.row)" type="danger">删除</el-button>

              </template>
          </el-table-column>
          </el-table-column>
      </el-table>
      <!-- <el-row>
      <el-pagination :current-page.sync="currentPage" background style="margin-top:30px" :page-size="pageSize"
        layout="total,prev,pager,next,jumper" :total="total" @current-change="handleCurrentChange1" />

    </el-row> -->
      <!-- 编辑界面 -->
        <!-- 编辑 -->
    <el-dialog top="2vh" :title="title" :visible="editFormVisible" @close="closeDialog">
      <el-form :model="special" label-width="90px" label-position="left">



        <div class="form-div">
          <el-form-item label="标题">
            <el-input v-model="special.title"></el-input>
          </el-form-item>
        </div>


        <div class="form-div">
          <el-form-item label="排序">
            <el-input v-model="special.sort"></el-input>
          </el-form-item>
        </div>



      


     
        <div class="form-div">
          <el-form-item label="是否上线">
            <el-radio-group v-model="special.status">
              <el-radio :label="1">是</el-radio>

              <el-radio :label="0">否</el-radio>

            </el-radio-group>
          </el-form-item>

        </div>
        <div class="form-div">
          <el-form-item label="内容">
            <wangEditor v-model="wangEditorDetail" :isClear="isClear" @change="wangEditorChange"></wangEditor>
          </el-form-item>

        </div>



      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
   
  </div>
</template>

<script>
import {delServiceProces,addServiceProces, createImage, delImage, bannerImages,bannerModules,getServiceProcesList } from '@/api/TP'
import { upload } from '@/api/upload'
import wangEditor from "@/components/wangEditor/wangEditor.vue";

export default {
    components: {
    // VueEditor,
    wangEditor
  },
  data() {
      return {
        isClear: false,//设置为true的时候，这个可以用this.wangEditorDetail=''来替代
      wangEditorDetail: "",
baseUrl : process.env.VUE_APP_BASE_API + "oe_addServiceProces_.csp",
          editFormVisible: false,
          dialogVisibleImg: false,
          data: [],
          title: '添加',
          special: {
            title:"",
            content:"",
            status:1,
            sort:.0,
             id:''
          },
          modulelist: [
              {
                  id: '1', name: '首页'
              },
              {
                  id: '2', name: '个人中心'
              },
              {
                  id: '3', name: '其他'
              }
          ],
          SEDJJXCX_LHFW_UserInformation_HT: {},
          bookUrllist: []
      }
  },
  created() {
      const SEDJJXCX_LHFW_UserInformation_HT = JSON.parse(decodeURIComponent(window.localStorage.getItem('SEDJJXCX_LHFW_UserInformation_HT')))
      this.SEDJJXCX_LHFW_UserInformation_HT = SEDJJXCX_LHFW_UserInformation_HT
      this.fetchData()
  },
  methods: {
     wangEditorChange(val) {
      this.special.content=val;
    },
    fetchData1(val = 1) {
            const params = {
                                    dbName: window.localStorage.getItem('JJHTDBnmame'),

                curPage: val
            }
            bannerModules(params).then(response => {
                if (response.data.code == '1') {
                    this.modulelist = response.data.list
                }
            })
        },
    handleCurrentChange1(val){
        this.fetchData(val)
    },
      fetchData(val = 1) {
          const params = {
                                  dbName: window.localStorage.getItem('JJHTDBnmame'),

          }
          getServiceProcesList(params).then(response => {
            console.log(response);
              if (response.data.code == '1') {
                  this.data = response.data.records
              }
          })
      },
      handleEdit1(){
        this.title = '添加';
        this.editFormVisible = true;
              this.special.id = '';
      },
      handleEdit(index, row) {

          this.editFormVisible = true;
          this.title = '修改'
      this.special = JSON.parse(JSON.stringify(row));

          this.wangEditorDetail=row.content
        
          return
          if (row != undefined && row != 'undefined') {
              this.title = '修改'
              this.special.id = row.id
              this.special.name = row.name
          } else {
              this.title = '添加'
              this.special.id = ''
          }
      },
      // 关闭编辑、增加弹出框
      closeDialog() {

          this.editFormVisible = false;
          this.deptName = '';
          this.wangEditorDetail=''
          this.special = {
            title:"",
            content:"",
            status:1,
            sort:.0
          };
      },
      // 编辑、增加页面保存方法
      submitForm() {
        
          if (this.special.title == '') {
        this.$message({
          showClose: true,
          message: '请输入标题！',
          type: 'warning'
        });
        return
      }

    
  

              var params = new URLSearchParams();
      params.append('title', this.special.title);
      params.append('id', this.special.id);
      params.append('content', this.special.content);
      params.append('status', this.special.status);
      params.append('sort', this.special.sort);
            params.append('dbName', window.localStorage.getItem('JJHTDBnmame'));


      
      this.axios.post(this.baseUrl, params,).then(res => {
        if (res.data.code == '1') {
            this.editFormVisible = false
                      this.special={
                        title:"",
            content:"",
            status:1,
            sort:.0
                      }
                      this.wangEditorDetail=''
                      this.fetchData()
                      this.$message({
                          type: 'success',
                          message: this.title == '修改' ? '修改成功！' : '创建成功！'
                      })
        } else {
          this.$message({
            type: 'warning',
            message: '创建失败！'
          })
        }
      },()=>{
        this.Product.id = res.data.id;
      }).catch(err => { 错误处理逻辑 })
    
      

      },

      deleteUser(index, row) {
          this.$confirm('此操作将永久删除, 是否继续?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
          }).then(() => {
              const params = {
                  id: row.id,
                                      dbName: window.localStorage.getItem('JJHTDBnmame'),

              }; delServiceProces(params).then(response => {
                  if (response.data.code == '1') {
                      this.fetchData()
                  }
              })
              this.$message({
                  type: 'success',
                  message: '删除成功!'
              });
          }).catch(() => {
              this.$message({
                  type: 'info',
                  message: '已取消删除'
              });
          });

      },
      handlePreview(file) {
          // 放大
          this.special.url = file.url
          this.dialogVisibleImg = true
      },
      handleRemove1(file, fileList) {
          // 移除
          const { uid } = file
          const { powUrl } = this.form
          const newPowUrl = powUrl.filter(v => {
              return uid !== powUrl.uid
          })
      },
      handleSmallPicSuccess(res, file, fileList) {
          if (file && file.length == 2) {
              file.shift()
          }
          console.log(111111);
          this.uploadImg(res);

      }, uploadImg(file, type) {
          let formData = new FormData()
          formData.append('file', file.raw)
          upload(formData).then(response => {
              this.special.url = response.data.yunUrl;
             
          }).catch((err) => {
              console.log(err);
              
          });


      },
  }
}
</script>

<style lang="scss" scoped>
.el-row {
  margin-bottom: 20px;
}

.el-row:last-child {
  margin-bottom: 0;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.el-button--medium {
  margin-top: 10px;
  margin-left: 600px;
  height: 45px;
  width: 100px;
}

.el-input {
  width: 200px;
}


// .el-input.inp {
//   width: auto !important;
// }
</style>
