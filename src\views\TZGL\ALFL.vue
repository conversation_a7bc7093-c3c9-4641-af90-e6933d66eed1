<template>
  <div class="app-container">

      <!-- 添加按钮 -->
      <el-form :inline="true" class="user-search">
          <el-form-item>
              <el-button size="mini" @click="handleEdit" type="primary">添加分类</el-button>

          </el-form-item>
      </el-form>
      <!-- 表格 -->
      <el-table :data="data" border fit highlight-current-row>
          <el-table-column align="center" label="序号">
              <template slot-scope="scope">{{scope.$index + 1}}</template>
          </el-table-column>
          <el-table-column align="center" prop="name" label="分类名称">
              <template slot-scope="scope">{{ scope.row.name }}</template>

          </el-table-column>
        
         
          <el-table-column fixed="right" label="操作" align="center">
              <template slot-scope="scope">
                  <el-button size="mini" @click="handleEdit(scope.$index, scope.row)">编辑</el-button>

                  <el-button size="mini" @click="deleteUser(scope.$index, scope.row)" type="danger">删除</el-button>

              </template>
          </el-table-column>
          </el-table-column>
      </el-table>
      <el-pagination :current-page.sync="currentPage" background style="margin-top:30px" :page-size="pageSize"
      layout="total,prev,pager,next,jumper" :total="total" @current-change="handleCurrentChange1" />

      <!-- 编辑界面 -->
      <el-dialog  :title="title" :visible.sync="editFormVisible" width="30%" @close="closeDialog">
          <el-form label-width="120px">
              <el-form-item label="分类名称:" >
                  <el-input @input="changeMessage" size="mini" v-model="authority.name" auto-complete="off"
                      placeholder="请输入分类名称"></el-input>
              </el-form-item>
              <el-form-item label="排序:" >
                  <el-input @input="changeMessage" size="mini" v-model="authority.sort" auto-complete="off"
                      placeholder="请输入排序"></el-input>
              </el-form-item>
              <el-form-item label="是否上线:" prop="authority.status">
                  <el-radio-group v-model="authority.status">
                      <el-radio :label="1">是</el-radio>

                      <el-radio :label="0">否</el-radio>

                  </el-radio-group>
              </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
              <el-button size="mini" @click="closeDialog">取消</el-button>
              <el-button size="mini" type="primary" class="title" @click="submitForm()">保存</el-button>
          </div>
      </el-dialog>
  </div>
</template>

<script>
import {createname ,delname ,fileTypeCURD} from '@/api/TP'
import { log } from 'console'

export default {
  data() {
      return {
          editFormVisible: false,
          data: [],
          currentPage:1,
          total:1,
          pageSize:10,
          title: '添加',
          authority: {
              name: '',
              sort:"",
              status: 1,
              id: '',
          },
          SEDJJXCX_LHFW_UserInformation_HT:{}
      }
  },
  created() {
      const SEDJJXCX_LHFW_UserInformation_HT = JSON.parse(decodeURIComponent(window.localStorage.getItem('SEDJJXCX_LHFW_UserInformation_HT')))
  this.SEDJJXCX_LHFW_UserInformation_HT = SEDJJXCX_LHFW_UserInformation_HT
  this.fetchData()
  },
  methods: {
      changeMessage(){
//强制刷新渲染
this.$forceUpdate()
},
      fetchData(val = 1) {
          const params = {
                                  dbName: window.localStorage.getItem('JJHTDBnmame'),

              curPage: val,
              number:10,
              flag:'query',
          }
          fileTypeCURD(params).then(response => {
              this.total=response.data.sumcount
              if (response.data.code == '1') {
                  this.data = response.data.list
              }
          })
      },
      handleCurrentChange1(val){
this.fetchData(val)
      },
      handleEdit: function (index, row) {

          this.editFormVisible = true;
          if (row != undefined && row != 'undefined') {
              this.title = '修改'
              this.authority.id = row.id
              this.authority.name = row.name
          } else {
              this.title = '添加'
              this.authority.id = ''
              this.authority.sort = ''
          }
      },
      
      // 关闭编辑、增加弹出框
      closeDialog() {
          this.editFormVisible = false;
          this.deptName = '';
          this.authority = {};
      },
      // 编辑、增加页面保存方法
      submitForm() {
          if (this.authority && this.authority.name) {
              const params = {
                  name: this.authority.name,
                  flag:'save',
                  sort: this.authority.sort,
                  status: this.authority.status,

                                      dbName: window.localStorage.getItem('JJHTDBnmame'),

                  id:this.title == '修改' ?  this.authority.id : ''
              }
             
              fileTypeCURD(params).then(response => {
                  if (response.data.code == '1') {
                      this.editFormVisible = false
                      this.authority.name = '';
                      this.authority.delFlag = '';
                      this.fetchData()
                      this.$message({
                          type: 'success',
                          message:  this.title == '修改' ?'分类修改成功！' : '分类创建成功！'

                      })
                  } else {
                      this.$message({
                          type: 'warning',
                          message: '分类创建失败！'
                      })
                  }
              })
          } else {
              this.$message({
                  showClose: true,
                  message: '请填写分类名称！',
                  type: 'warning'
              });
          }

      },
      deleteUser(index, row) {
          this.$confirm('此操作将永久删除分类, 是否继续?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
          }).then(() => {
              const params = {
                  id: row.id,
                  flag:"delete",
                                      dbName: window.localStorage.getItem('JJHTDBnmame'),

              }; fileTypeCURD(params).then(response => {
                  if (response.data.code == '1') {
                      this.fetchData()
                  }
              })
              this.$message({
                  type: 'success',
                  message: '删除成功!'
              });
          }).catch(() => {
              this.$message({
                  type: 'info',
                  message: '已取消删除'
              });
          });

      }
  }
}
</script>

<style lang="scss" scoped>
.el-row {
}

.el-row:last-child {
  margin-bottom: 0;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.el-button--medium {
  margin-top: 10px;
  margin-left: 600px;
  height: 45px;
  width: 100px;
}

.el-input {
  width: 200px;
}

// .el-input.inp {
//   width: auto !important;
// }
</style>
