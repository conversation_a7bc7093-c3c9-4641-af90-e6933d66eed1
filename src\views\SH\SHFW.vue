<template>
  <div class="app-container">

    <!-- 添加按钮 -->
    <el-form :inline="true" class="user-search">
      <el-form-item prop="Product.catld">
        <el-select size="mini" v-model="input5" placeholder="服务进度" @change="change2">
          <el-option size="mini" v-for="item in classify" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item prop="Product.catld">
        <el-select size="mini" v-model="input3" placeholder="分类" @change="change2">
          <el-option size="mini" v-for="item in modulelist" :key="item.id" :label="item.cateName"
            :value="item.id"></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-input size="mini" placeholder="请输入内容" v-model="input4" class="el-input_ss1">
          <el-button size="mini" slot="append" icon="el-icon-search" @click="change3"></el-button>
        </el-input>
      </el-form-item>
      <el-form-item>
        <!-- <el-button size="mini" type="primary" icon="el-icon-plus" @click="increase()">添加</el-button> -->

        <el-button size="mini" type="primary" @click="Reload" icon="el-icon-refresh-left">重新加载</el-button>
        <!-- <el-button @click="leading">批量导出</el-button> -->




      </el-form-item>
    </el-form>
    <!-- 表格 -->
    <el-table :data="data" border fit highlight-current-row>
      <el-table-column align="center" label="序号">
        <template slot-scope="scope">{{ scope.$index + 1 }}</template>
      </el-table-column>
     
      <el-table-column align="center" label="客户名称" width="200">
        <template slot-scope="scope">{{ scope.row.name }}</template>

        <!-- <template slot-scope="scope">
                  <div v-if="scope.row.cateId == '1'">首页</div>
                  <div v-if="scope.row.cateId == '2'">个人中心</div>
                  <div v-if="scope.row.cateId == '3'">其他</div>
              </template> -->
      </el-table-column>
     
      <el-table-column align="center" label="手机号" width="200">
        <template slot-scope="scope">{{ scope.row.phone }}</template>


      </el-table-column> 
      <el-table-column align="center" label="商家名称" width="200">
        <template slot-scope="scope">{{ scope.row.storeName }}</template>
      </el-table-column>
      <el-table-column align="center" label="提交时间" width="200">
        <template slot-scope="scope">{{ scope.row.createTime }}</template>
      </el-table-column>
   
      <el-table-column align="center" label="售后类型" width="200">
        <template slot-scope="scope">
<el-button type="danger" v-if="scope.row.type ==1" size="mini">产品异常</el-button>
          <el-button type="warning" v-if="scope.row.type ==2" size="mini">意见反馈</el-button>
        </template>
      </el-table-column> 

      <el-table-column align="center" label="问题描述" width="200">
        <template slot-scope="scope">{{ scope.row.content }}</template>
      </el-table-column>
      <el-table-column align="center" label="服务进度" width="100">
      

           <template slot-scope="scope">
            <el-button size="mini"  v-if="scope.row.status == '1'" type="success">已处理</el-button>
            <el-button size="mini"  v-if="scope.row.status == '0'" type="danger">未处理</el-button>
                  
              </template>
      </el-table-column>
     
      <!-- <el-table-column align="center" prop="name" label="封面图片">
        <template slot-scope="scope">
          <el-popover placement="bottom" trigger="hover" width="300">
            <img :src="scope.row.productImage" width="100%" />
            <img slot="reference" :src="scope.row.productImage" :alt="scope.row.productImage"
              style="max-height: 40px;max-width:40px; padding: 5px" />
          </el-popover>
        </template>

      </el-table-column> -->
     

    

    
      <el-table-column fixed="right" width="200" label="操作" align="center">
        <template slot-scope="scope">
          <el-button size="mini" type="success" @click="handleEdit( scope.row)">查看详情</el-button>


        </template>
      </el-table-column>
    </el-table>
    <el-row>
      <el-pagination :current-page.sync="currentPage" background style="margin-top:30px" :page-size="pageSize"
        layout="total,prev,pager,next,jumper" :total="total" @current-change="handleCurrentChange1" />

    </el-row>
    <!-- 查看 -->
    <el-dialog title="查看详情" :visible.sync="editFormVisible1" @close="closeDialog" width="50%">
      <el-form label-position="rigth" label-width="130px">
        <!-- <el-form-item label="客户名称:">
          <div>{{ special.name }}</div>

        </el-form-item>
        <el-form-item label="手机号:">

          <div>{{ special.phone }}</div>

        </el-form-item> -->
        <el-form-item label="客户名称:">

<div>{{ special.name }}</div>

</el-form-item>


        <el-form-item label="商家名称:">

          <div>{{ special.storeName }}</div>

        </el-form-item>

        <el-form-item label="联系电话:">

          <div>{{ special.phone }}</div>

        </el-form-item>

        <el-form-item label="提交时间:">

          <div>{{ special.createTime }}</div>

        </el-form-item>

        <el-form-item label="售后类型:">

          <div v-if="special.type ==1">产品异常</div>
          <div v-if="special.type ==2">意见反馈</div>

        </el-form-item>
        <el-form-item label="问题描述:">

<div>{{ special.content }}</div>

</el-form-item>
      
    

        <el-form-item  label="服务进度">
          <el-radio v-model="special.status" :label="'1'">已处理</el-radio>
          <el-radio v-model="special.status" :label="'0'">未处理</el-radio>
      </el-form-item>

      <el-form-item  label="反馈图片">
        <el-image :key="index1" v-for="(item, index1) in special.photo" style="width: 100px; height: 100px; margin:6px 6px;" :src="item"
              :preview-src-list="special.image">
            </el-image>
      </el-form-item>

           
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="closeDialog">取消</el-button>
        <el-button size="mini" type="primary" class="title" @click="submitForm()">确定</el-button>
      </div>
    </el-dialog>
 
  </div>
</template>

<script>
import { serviceList,updateServiceStatus } from '@/api/YY'
import { upload } from '@/api/upload'
import { Loading } from 'element-ui';

export default {
  data() {
    return {
      editFormVisible1:false,
      editFormVisible: false,
      dialogVisibleImg: false,
      currentPage: 1,
      pageSize: 10,
      total: 1,
      data: [],
      title: '添加',
      special: {
        id: '',
        productImage: "",
        status: 1,
        sortId: '',
        remark: '',
        integralNum: '',
        productStock: '',
        proInfo: '',
        peoNum: "",
        quota: '',
        productCate: '',
        cateId: '',
        detail: '',
        sort: '',
      },
      modulelist: [

      ],
      SEDJJXCX_LHFW_UserInformation_HT: {},
      bookUrllist: [],
      bookUrllist1: [],
      input3: '',
      input4: '',
      input2: '',
      input5: '',
      classify: [
        {
          id:0,name:'未处理'
        },
        {
          id:1,name:'已处理'
        }

      ],
    }
  },
  mounted() {
    this.fetchData();

  },
  created() {
    const SEDJJXCX_LHFW_UserInformation_HT = JSON.parse(decodeURIComponent(window.localStorage.getItem('SEDJJXCX_LHFW_UserInformation_HT')))
    this.SEDJJXCX_LHFW_UserInformation_HT = SEDJJXCX_LHFW_UserInformation_HT;
    this.input2 = this.$route.query.input2 ? this.$route.query.input2 : '';
    if(this.input2 ){

      this.input5=this.classify.find(item=>item.id ==this.input2 ).name
      }
    // this.fetchData1()
    // this.fetchData()
  },
  methods: {
    Reload() {
      this.input3 = ''
      this.input4 = ''
      const params = {
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: 1
      }
      serviceList(params).then(response => {
        this.total = response.data.totalNum

        if (response.data.code == '1') {
          this.data = response.data.list
        } else {
          this.data = response.data.list

        }
      }).catch((err) => {
        console.log();
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    change3() {
      const params = {
        keyword: this.input4,
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: 1
      }
      if (this.input4) {
        params.cateId = this.input3;
      }

      serviceList(params).then(response => {
        this.total = response.data.totalNum

        if (response.data.code == '1') {
          this.data = response.data.list
        } else {
          this.data = response.data.list
        }
      }).catch((err) => {
        console.log();
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    change2() {
      const params = {
        status: this.input5,
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: 1
      }
      if (this.input4) {
        params.keyword = this.input4;
      }

      serviceList(params).then(response => {
        if (response.data.code == '1') {
          this.total = response.data.totalNum

          this.data = response.data.list
        } else {
          this.data = response.data.list
          this.input2 = ''
        }
      }).catch((err) => {
        console.log();
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    fetchData1(val = 1) {
      const params = {
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: val
      }
      allCates(params).then(response => {
        this.total = response.data.totalNum

        if (response.data.code == '1') {
          this.modulelist = response.data.records
        } else {
          this.modulelist = response.data.records

        }
      }).catch((err) => {
        console.log();
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    handleCurrentChange1(val) {
      this.fetchData(val)
    },
    fetchData(val = 1) {
      const params = {
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: val
      }
      if (this.input2) {
        params.status = this.input2;
      }

      serviceList(params).then(response => {
        this.total = response.data.totalNum
        if (response.data.code == '1') {
          this.data = response.data.list
        } else {
          this.data = response.data.list

        }
      })
    },
    handleEdit1() {
      this.title = '添加';
      this.editFormVisible = true;
      this.special.id = '';
    },
    handleEdit( row) {
      
      this.editFormVisible1 = true;
      this.title = '查看详情'
      this.special = row;
      this.special.photo = JSON.parse(row.photo);

      return
      if (row.productImage) {
        this.bookUrllist = [
          {
            url: row.productImage
          }
        ]
      }
      if (row.detail) {
        this.bookUrllist1 = [
          {
            url: row.detail
          }
        ]
      }

      console.log(this.bookUrllist);
      console.log(this.bookUrllist1);
      return
      if (row != undefined && row != 'undefined') {
        this.title = '修改'
        this.special.id = row.id
        this.special.name = row.name
      } else {
        this.title = '添加'
        this.special.id = ''
      }
    },
    // 关闭编辑、增加弹出框
    closeDialog() {

      this.editFormVisible1 = false;
      this.deptName = '';
      this.special = {};
    },
    // 编辑、增加页面保存方法
    submitForm() {
    

      const params = {
        id: this.special.id,
        status: this.special.status,
        dbName: window.localStorage.getItem('JJHTDBnmame')
      };
      updateServiceStatus(params).then(response => {
        if (response.data.code == '1') {
          this.editFormVisible1 = false
          this.bookUrllist = []
          this.fetchData()
          this.$message({
            type: 'success',
            message: '修改成功！'
          })
        } else {
          this.$message({
            type: 'warning',
            message: '创建失败！'
          })
        }
      })


    },

    deleteUser(index, row) {
      this.$confirm('此操作将永久删除, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const params = {
          id: row.id,
                              dbName: window.localStorage.getItem('JJHTDBnmame'),

        }; delIntergralGoods(params).then(response => {
          if (response.data.code == '1') {
            this.fetchData()
          }
        })
        this.$message({
          type: 'success',
          message: '删除成功!'
        });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });

    },
    handlePreview(file) {
      // 放大
      this.special.productImage = file.productImage
      this.dialogVisibleImg = true
    },
    handleRemove1(file, fileList) {
      // 移除
      const { uid } = file
      const { powUrl } = this.form
      const newPowUrl = powUrl.filter(v => {
        return uid !== powUrl.uid
      })
    },
    handleSmallPicSuccess(res, file, fileList) {
      if (file && file.length == 2) {
        file.shift()
      }
      this.uploadImg(res, 1);

    },
    handleSmallPicSuccess1(res, file, fileList) {
      if (file && file.length == 2) {
        file.shift()
      }
      this.uploadImg(res, 2);

    }
    , uploadImg(file, type) {
      this.loading = Loading.service({
        lock: true,
        text: '图片上传中........',
        // background: 'rgba(0, 0, 0, 0.7)',
        target: document.querySelector('#loadingDiv')
      })
      let formData = new FormData()
      formData.append('file', file.raw)
      upload(formData).then(response => {
        setTimeout(() => {
          this.loading.close()

        }, 2000);
        if (type == 1) {
          this.special.productImage = response.data.yunUrl;

        } else {
          this.special.detail = response.data.yunUrl;

        }

      }).catch((err) => {
        console.log(err);

      });


    },
  }
}
</script>

<style lang="scss" scoped>
.el-row {
  margin-bottom: 20px;
}

.el-row:last-child {
  margin-bottom: 0;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.el-button--medium {
  margin-top: 10px;
  margin-left: 600px;
  height: 45px;
  width: 100px;
}

.el-input {
  width: 300px;
}

// .el-input.inp {
//   width: auto !important;
// }
.el-input_ss1 {

  margin-top: 6px;
}
</style>
