<template>
  <div class="app-container">
    <!-- 搜索筛选 -->
    <el-form :inline="true" class="user-search">


      <el-form-item>
        <el-select size="mini" v-model="input3" placeholder="审核状态" @change="change2">
          <el-option size="mini" v-for="item in classify" :key="item.id" :label="item.name"
            :value="item.id"></el-option>
        </el-select>


      </el-form-item>
    
      <el-form-item>
        <el-input size="mini" placeholder="请输入内容" v-model="input4" class="el-input_ss1">
          <el-button size="mini" slot="append" icon="el-icon-search" @click="change3"></el-button>
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-button size="mini" type="primary" icon="el-icon-plus" @click="addmkdxx()">添加</el-button>

        <el-button size="mini" type="primary" @click="Reload" icon="el-icon-refresh-left">重新加载</el-button>
        <el-button @click="leading1" size="mini" type="warning" icon="el-icon-download">导出数据</el-button>

        <el-button size="mini" type="success" @click="CXXX(2)">通过</el-button>
        <el-button size="mini" type="info" @click="CXXX(3)">拒绝</el-button>
        <el-button size="mini" type="danger" @click="CXXX(1)">删除</el-button>



      </el-form-item>
    </el-form>

    <el-table :data="data" border fit highlight-current-row @selection-change="handleSelectionChange">
      <el-table-column align="center" type="selection" width="55">
      </el-table-column>
      <el-table-column align="center" label="序号" width="95">
        <template slot-scope="scope">{{ scope.$index + 1 }}</template>
      </el-table-column>
      <el-table-column label="商家LOGO" align="center" width="150">

        <template slot-scope="scope">
          <el-popover placement="bottom" trigger="hover" width="300">
            <img :src="scope.row.logo" width="100%" />
            <img slot="reference" :src="scope.row.logo" :alt="scope.row.logo"
              style="max-height: 60px;max-width: 60px; padding: 5px" />
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="img" label="商家名称" width="200">
        <template slot-scope="scope">
          {{ scope.row.storeName }}
        </template>
      </el-table-column>
      <el-table-column align="center" prop="img" label="联系人" width="100">
        <template slot-scope="scope">
          {{ scope.row.storeUserName }}
        </template>
      </el-table-column>
    

      <el-table-column align="center" label="审核状态" width="100">
        <!-- <template slot-scope="scope">{{ scope.row.name }}</template> -->

        <template slot-scope="scope">
          <el-button plain v-if="scope.row.status == '0'" type="danger" size="mini">待审核</el-button>
          <el-button plain v-if="scope.row.status == '3'" type="warning" size="mini">未通过</el-button>
          <el-button plain v-if="scope.row.status == '2'" type="success" size="mini">已通过</el-button>

        </template>
      </el-table-column>
      <el-table-column align="center" width="150" label="手机号码">
        <template slot-scope="scope">{{ scope.row.phone }}</template>
      </el-table-column>
      <el-table-column align="center" label="店面位置" width="400">
        <template slot-scope="scope">{{ scope.row.address }}</template>
      </el-table-column>
      <el-table-column align="center" label="团队人数">
        <template slot-scope="scope">{{ scope.row.team_count }}人</template>
      </el-table-column>
      <el-table-column align="center" label="项目数量">
        <template slot-scope="scope">{{ scope.row.project_count }}个</template>
      </el-table-column>
      <el-table-column align="center" label="进行中">
        <template slot-scope="scope">{{ scope.row.project_count1 }}个</template>
      </el-table-column>
      <el-table-column align="center" label="已完工">
        <template slot-scope="scope">{{ scope.row.project_count2 }}个</template>
      </el-table-column>







      <!-- <el-table-column align="center" label="店铺状态" width="100">

        <template slot-scope="scope">
          <el-button plain v-if="scope.row.expireStatus == '有效期'" type="primary" size="mini">{{ scope.row.expireStatus
            }}</el-button>
          <el-button plain v-if="scope.row.expireStatus == '已到期'" type="danger" size="mini">{{ scope.row.expireStatus
            }}</el-button>

        </template>
      </el-table-column> -->
      
      <el-table-column align="center" width="200" label="入驻时间">
        <template slot-scope="scope">{{ scope.row.joinTime }}</template>
      </el-table-column><el-table-column align="center" width="200" label="过期时间">
        <template slot-scope="scope">{{ scope.row.expireTime }}</template>
      </el-table-column>
      <!-- <el-table-column align="center"   label="店长"  >
        <template slot-scope="scope">
          <el-link type="primary">{{ scope.row.role_1 }}个</el-link>
          </template>
      </el-table-column>

      <el-table-column align="center"   label="导购"  >
        <template slot-scope="scope">
          <el-link type="primary">{{ scope.row.role_2 }}个</el-link>

          </template>
      </el-table-column>

      <el-table-column align="center"   label="交付设计管家" width="100" >
        <template slot-scope="scope">
          <el-link type="primary">{{ scope.row.role_3 }}个</el-link>

          </template>
      </el-table-column>

      <el-table-column align="center"   label="交付管家" width="100" >
        <template slot-scope="scope">
          <el-link type="primary">{{ scope.row.role_5 }}个</el-link>

         </template>
      </el-table-column>

      <el-table-column align="center"   label="交付工程师" width="100" >
        <template slot-scope="scope">
          <el-link type="primary">{{ scope.row.role_6 }}个</el-link>

         </template>
      </el-table-column>

      <el-table-column align="center"   label="首席设计师" width="100" >
        <template slot-scope="scope">
          
          <el-link type="primary">{{ scope.row.role_7 }}个</el-link>
          
          </template>
      </el-table-column> -->
      <!-- fixed="right"  width="200" -->

      <el-table-column fixed="right" label="操作" align="center" width="300">
        <template slot-scope="scope">
          <!-- <el-button size="mini" type="primary" @click="handleEditzt(scope.row)">修改</el-button> -->
          <!-- <el-button size="mini" type="danger" @click="handleDelete(scope.row.id)">删除</el-button> -->
          <el-button size="mini" type="success" @click="getuser(scope.row.id)">用户</el-button>
          <el-button size="mini" type="primary" @click="team(scope.row.id)">团队</el-button>
          <el-button size="mini" type="warning" @click="handleDelete(scope.row.id)">项目</el-button>
          <el-button size="mini" type="info" @click="bjmdxx(scope.row)">编辑</el-button>

        </template>
      </el-table-column>
    </el-table>
    <el-row>
      <el-pagination :current-page.sync="currentPage" background style="margin-top:30px" :page-size="pageSize"
        layout="total,prev,pager,next,jumper" :total="totallist" @current-change="handleCurrentChange1" />

    </el-row>
    <!-- 团队 -->
    <el-dialog @close="formCancal1" width="70%" :title="dialogTitle" :visible.sync="specialShow"
      :close-on-click-modal="false" :close-on-press-escape="false">

      <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
        <el-tab-pane v-for="(tab, index) in rolelist" :key="tab.id" :label="tab.label" :name="tab.name">

          <el-table :data="roledata" border fit highlight-current-row @selection-change="handleSelectionChange">

            <el-table-column align="center" label="序号" width="95">
              <template slot-scope="scope">{{ scope.$index + 1 }}</template>
            </el-table-column>

            <el-table-column align="center" prop="name" label="头像" width="90">
              <template slot-scope="scope">
                <el-popover placement="bottom" trigger="hover" width="100">
                  <img :src="scope.row.photo" width="100%" />
                  <img slot="reference" :src="scope.row.photo" :alt="scope.row.photo"
                    style="max-height: 40px;max-width: 40px;" />
                </el-popover>
              </template>

            </el-table-column>

            <el-table-column align="center" prop="img" label="姓名">
              <template slot-scope="scope">
                {{ scope.row.name }}
              </template>
            </el-table-column>


            <el-table-column align="center" label="联系方式" width="120">
              <template slot-scope="scope">{{ scope.row.phone }}</template>
            </el-table-column>
            <el-table-column align="center" label="职位" width="200">
              <template slot-scope="scope">
                <div v-if="scope.row.role == '0'">业主</div>
                <div v-if="scope.row.role == '1'"> 经销商 </div>
                <div v-if="scope.row.role == '2'">店长</div>
                <div v-if="scope.row.role == '3'">导购</div>
                <div v-if="scope.row.role == '4'">监理</div>
                <div v-if="scope.row.role == '5'">交付管家</div>
                <div v-if="scope.row.role == '6'">瓦工</div>
                <div v-if="scope.row.role == '7'">设计师 </div>
                <div v-if="scope.row.role == '8'">分销商</div>
                <div v-if="scope.row.role == '9'">首席设计师</div>

              </template>
            </el-table-column>
            <el-table-column align="center" label="审核状态">
              <template slot-scope="scope">
                <el-button size="mini" type="warning" v-if="scope.row.status == '0'"> 待审核</el-button>
                <el-button size="mini" type="success" v-if="scope.row.status == '1'">已通过</el-button>
                <el-button size="mini" type="danger" v-if="scope.row.status == '-1'">已拒绝</el-button>

              </template>
            </el-table-column>
            <el-table-column align="center" label="服务量">
              <template slot-scope="scope">{{ scope.row.serviceCount }}个</template>
            </el-table-column>

            <el-table-column align="center" label="从业年限">
              <template slot-scope="scope">{{ scope.row.workTime }}年</template>
            </el-table-column>





            <!-- fixed="right"  width="200" -->


          </el-table>
          <!-- 在这里插入您想要循环显示的内容 -->
        </el-tab-pane>
      </el-tabs>
      <el-pagination :current-page.sync="currentPage1" background style="margin-top:30px" :page-size="pageSize"
        layout="total,prev,pager,next,jumper" :total="total1" @current-change="handleCurrentChange2" />

    </el-dialog>

    <!-- 用户 -->
    <el-dialog @close="formCancal1" width="70%" title="用户" :visible.sync="specialShow1" :close-on-click-modal="false"
      :close-on-press-escape="false">
      <el-form :inline="true" class="user-search">
        <el-form-item>
          <el-input size="mini" placeholder="请输入内容" v-model="input5" class="el-input_ss1">
            <el-button size="mini" slot="append" icon="el-icon-search" @click="change4"></el-button>
          </el-input>
        </el-form-item>
        <el-form-item>
          <!-- <el-button size="mini" type="primary" icon="el-icon-plus" @click="increase()">添加</el-button> -->

          <el-button size="mini" type="primary" @click="Reload1" icon="el-icon-refresh-left">重新加载</el-button>
          <!-- <el-button @click="leading">批量导出</el-button> -->




        </el-form-item>
      </el-form>

      <el-table :data="userLIstL" border fit highlight-current-row @selection-change="handleSelectionChange">

        <el-table-column align="center" label="序号" width="95">
          <template slot-scope="scope">{{ scope.$index + 1 }}</template>
        </el-table-column>

        <el-table-column align="center" prop="name" label="微信头像" width="90">
          <template slot-scope="scope">
            <el-popover placement="bottom" trigger="hover" width="100">
              <img :src="scope.row.wxPhoto" width="100%" />
              <img slot="reference" :src="scope.row.wxPhoto" :alt="scope.row.wxPhoto"
                style="max-height: 40px;max-width: 40px;" />
            </el-popover>
          </template>

        </el-table-column>
        <el-table-column align="center" prop="img" label="微信昵称">
          <template slot-scope="scope">
            {{ scope.row.wxName }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="所属店铺" width="200">
          <template slot-scope="scope">{{ scope.row.storeName }}</template>
        </el-table-column>
        <el-table-column align="center" prop="img" label="地址" width="300">
          <template slot-scope="scope">
            {{ scope.row.address ? scope.row.address : '暂未授权地址' }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="手机号" width="120">
          <template slot-scope="scope">{{ scope.row.phone }}</template>
        </el-table-column>

        <el-table-column align="center" label="首次访问时间" width="200">
          <template slot-scope="scope">{{ scope.row.createTime }}</template>
        </el-table-column>
        <el-table-column align="center" label="最后访问时间" width="200">
          <template slot-scope="scope">{{ scope.row.lastTime }}</template>
        </el-table-column>
        <el-table-column align="center" label="访问次数" width="100">
          <template slot-scope="scope">{{ scope.row.accessCount }}</template>
        </el-table-column>
        <el-table-column align="center" label="来源" width="200">
          <template slot-scope="scope">
            <el-button round size="mini" v-if="scope.row.storeStatus == '1001'">发现栏小程序主入口</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1005'">顶部搜索框的搜索结果页</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1006'">发现栏小程序主入口搜索框的搜索结果页</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1007'">单人聊天会话中的小程序消息卡片</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1008'">群聊会话中的小程序消息卡片</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1011'">扫描二维码</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1012'">长按图片识别二维码</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1014'">手机相册选取二维码</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1017'">前往体验版的入口页</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1019'">微信钱包</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1020'">公众号profile页相关小程序列表</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1022'">聊天顶部置顶小程序入口</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1023'">安卓系统桌面图标</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1024'">小程序profile页</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1025'">扫描一维码</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1026'">附近小程序列表</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1027'">顶部搜索框搜索结果页“使用过的小程序”列表</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1028'">我的卡包</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1029'">卡券详情页</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1031'">长按图片识别一维码</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1032'">手机相册选取一维码</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1034'">微信支付完成页</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1035'">公众号自定义菜单</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1036'">App分享消息卡片</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1037'">小程序打开小程序</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1038'">从另一个小程序返回</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1039'">摇电视</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1042'">添加好友搜索框的搜索结果页</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1044'">带shareTicket的小程序消息卡片</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1047'">扫描小程序码</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1048'">长按图片识别小程序码</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1049'">手机相册选取小程序码</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1052'">卡券的适用门店列表</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1053 '">搜一搜的结果页</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1054'">顶部搜索框小程序快捷入口</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1056'">音乐播放器菜单</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1058'">公众号文章</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1059'">体验版小程序绑定邀请页</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1064'">微信连Wifi状态栏</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1067'">公众号文章广告</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1068'">附近小程序列表广告</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1072'">二维码收款页面</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1073'">客服消息列表下发的小程序消息卡片</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1074'">公众号会话下发的小程序消息卡片</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1089'">微信聊天主界面下拉</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1090'">长按小程序右上角菜单唤出最近使用历史</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1092'">城市服务入口</el-button>
            <el-button round size="mini" v-else-if="scope.row.storeStatus == '1222'">系统获客</el-button>
          </template>

        </el-table-column>
        <el-table-column align="center" label="角色" width="200">
          <template slot-scope="scope">
            <div v-if="scope.row.role == '0'">业主</div>
            <div v-if="scope.row.role == '1'"> 经销商 </div>
            <div v-if="scope.row.role == '2'">店长</div>
            <div v-if="scope.row.role == '3'">导购</div>
            <div v-if="scope.row.role == '4'">监理</div>
            <div v-if="scope.row.role == '5'">交付管家</div>
            <div v-if="scope.row.role == '6'">瓦工</div>
            <div v-if="scope.row.role == '7'">设计师 </div>
            <div v-if="scope.row.role == '8'">分销商</div>
            <div v-if="scope.row.role == '9'">首席设计师</div>

          </template>
        </el-table-column>
        <el-table-column align="center" label="积分" width="200">
          <template slot-scope="scope">{{ scope.row.integral }}积分</template>
        </el-table-column>

        <!-- fixed="right"  width="200" -->

        <!-- <el-table-column   fixed="right"  label="操作" align="center"  width="200">
       <template slot-scope="scope">
         <el-button size="mini" type="primary" @click="handleEditzt(scope.row)">修改</el-button>
         <el-button size="mini" type="danger" @click="handleDelete(scope.row.id)">删除</el-button>

       </template>
     </el-table-column> -->
     
    
      </el-table>
      <el-row>
        <el-pagination :current-page.sync="currentPage2" background style="margin-top:30px" :page-size="pageSize"
          layout="total,prev,pager,next,jumper" :total="total2" @current-change="handleCurrentChange3" />

      </el-row>


    </el-dialog>

    <!-- 项目 -->
    <el-dialog @close="formCancal1" width="70%" title="项目" :visible.sync="specialShow2" :close-on-click-modal="false"
      :close-on-press-escape="false">
      <el-form :inline="true" class="user-search">
        <el-form-item>
          <el-input size="mini" placeholder="请输入内容" v-model="input6" class="el-input_ss1">
            <el-button size="mini" slot="append" icon="el-icon-search" @click="change5"></el-button>
          </el-input>
        </el-form-item>
        <el-form-item>
          <!-- <el-button size="mini" type="primary" icon="el-icon-plus" @click="increase()">添加</el-button> -->

          <el-button size="mini" type="primary" @click="Reload2" icon="el-icon-refresh-left">重新加载</el-button>
          <!-- <el-button @click="leading">批量导出</el-button> -->




        </el-form-item>
      </el-form>

      <el-table :data="xmlist" border fit highlight-current-row>


        <el-table-column align="center" label="序号" width="80">
          <template slot-scope="scope">{{ scope.$index + 1 }}</template>
        </el-table-column>
        <el-table-column align="center" prop="img" label="订单号" width="200">
          <template slot-scope="scope">
            {{ scope.row.orderNum }}
          </template>
        </el-table-column>
        <el-table-column align="center" width="200" label="所属门店">
          <template slot-scope="scope">{{ scope.row.storeName }}</template>
        </el-table-column>
        <el-table-column align="center" prop="img" label="业主" width="200">
          <template slot-scope="scope">
            {{ scope.row.name }}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="img" label="工地名称" width="200">
          <template slot-scope="scope">
            {{ scope.row.community }}
          </template>
        </el-table-column>
        <el-table-column align="center" width="100" label="项目状态">
          <template slot-scope="scope">
            <el-button plain v-if="scope.row.delFlag == '0'" type="primary" size="mini">上架</el-button>
            <el-button plain v-if="scope.row.delFlag == '1'" type="warning" size="mini">下架</el-button>

          </template>
        </el-table-column>
        <el-table-column align="center" width="100" label="施工状态">
          <template slot-scope="scope">
            <el-button plain v-if="scope.row.orderStatus == '1'" type="primary" size="mini">未开始</el-button>
            <el-button plain v-if="scope.row.orderStatus == '2'" type="warning" size="mini">进行中</el-button>
            <el-button plain v-if="scope.row.orderStatus == '3'" type="success" size="mini">已完工</el-button>

          </template>
        </el-table-column>

        <el-table-column align="center" width="150" label="施工进度">
          <template slot-scope="scope">
            <el-button plain v-if="scope.row.schedule == '1'" size="mini">门店接单</el-button>
            <el-button plain v-if="scope.row.schedule == '2'" size="mini">总部确认中 </el-button>
            <el-button plain v-if="scope.row.schedule == '3'" size="mini">施工中</el-button>
            <el-button plain v-if="scope.row.schedule == '4'" size="mini">施工完成</el-button>
            <el-button plain v-if="scope.row.schedule == '5'" size="mini">项目验收中</el-button>
            <el-button plain v-if="scope.row.schedule == '6'" size="mini">终生质保</el-button>

          </template>
        </el-table-column>
        <el-table-column align="center" width="200" label="联系电话">
          <template slot-scope="scope">{{ scope.row.phone }}</template>
        </el-table-column>
        <el-table-column align="center" width="500" label="地址">
          <template slot-scope="scope">{{ scope.row.address }}</template>
        </el-table-column>

        <el-table-column prop="workTime" align="center" width="200" label="施工时间" :formatter="formatDate">
        </el-table-column>

        <el-table-column prop="predictTime" align="center" width="200" label="预计完成时间" :formatter="formatDate">
        </el-table-column>
        <el-table-column prop="endTime" align="center" width="200" label="实际完成时间" :formatter="formatDate">
        </el-table-column>
        <!-- <el-table-column align="center" label="总部监理">
        <template slot-scope="scope">{{ scope.row.regulatorName }}</template>
      </el-table-column>
      <el-table-column align="center" label="设计师">
        <template slot-scope="scope">{{ scope.row.stylistName }}</template>
      </el-table-column>
      <el-table-column align="center" label="交付工程师" width="100">
        <template slot-scope="scope">{{ scope.row.bricklayerName }}</template>
      </el-table-column>
      <el-table-column align="center" label="交付管家">
        <template slot-scope="scope">{{ scope.row.stewardName }}</template>
      </el-table-column> -->
        <!-- fixed="right"  width="200" -->

        <el-table-column fixed="right" label="操作" align="center" width="150">
        <template slot-scope="scope">
          <el-button size="mini" type="success" @click="OpenRecord(scope.row)">查看打卡记录</el-button>

        </template>
      </el-table-column>
      </el-table>
      <el-row>
        <el-pagination :current-page.sync="currentPage3" background style="margin-top:30px" :page-size="pageSize"
          layout="total,prev,pager,next,jumper" :total="total3" @current-change="handleCurrentChange4" />

      </el-row>

    </el-dialog>

    <!-- 编辑 -->
    <el-dialog :close-on-click-modal='false' top="2vh" :title="title" :visible="specialShow3" @close="formCancal1">
      <el-form :model="MD" label-width="90px" label-position="left">



        <div class="form-div">
          <el-form-item label="商家名称">
            <el-input v-model="MD.storeName"></el-input>
          </el-form-item>
        </div>


        <div class="form-div">
          <el-form-item label="联系人">
            <el-input v-model="MD.storeUserName"></el-input>
          </el-form-item>
        </div>
        <div class="form-div">
          <el-form-item label="手机号码">
            <el-input v-model="MD.phone"></el-input>
          </el-form-item>
        </div>



        <div class="form-div">
          <el-form-item label="店面位置">
            <el-input v-model="MD.address"></el-input>

            <el-button type="primary" @click="GETdzk('form')" size="small">打开地图选择店面位置经纬度</el-button>

          </el-form-item>
        </div>


        <div class="form-div">
          <el-form-item label="LOGO">
            <el-upload action="#" list-type="picture-card" :on-preview="handlePreview" :on-remove="handleRemove"
              :auto-upload="false" :file-list="bookUrllist" multiple :on-change="handleSmallPicSuccess">
              <i class="el-icon-plus" />
            </el-upload>
            <el-dialog :visible.sync="dialogVisibleImg" top="0" center :modal="false">
              <img width="100%" :src="MD.logo" alt>
            </el-dialog>

          </el-form-item>
        </div>
        <div class="form-div">
          <el-form-item label="门店状态">
            <el-radio-group v-model="MD.status">
              <el-radio :label="0">待审核</el-radio>

              <el-radio :label="2">已通过</el-radio>
              <el-radio :label="3">未通过</el-radio>
            </el-radio-group>
          </el-form-item>

        </div>



      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="specialShow3=false">取 消</el-button>
        <el-button type="primary" @click="submitSure">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 地图 -->
    <el-dialog @close="cancelForm" style="height: 78vh;" top="6vh" title="请选择位置~" size="80%" :visible.sync="drawer">
      <div class="demo-drawer__content">
        <el-form ref="form" label-width="80px">

          <el-form-item label="地址">
            <el-input id="ipt" clear="inp" v-model="wxz.add" placeholder="请选择地址" @change="changeIpt"></el-input>

          </el-form-item>


        </el-form>

        <div id="container" ref="amap"></div>

        <div class="dialog-footer">
          <el-button @click="cancelForm">取 消</el-button>
          <el-button type="primary" @click="closeDrawer()"> 确 定
          </el-button>
        </div>
      </div>
    </el-dialog>
   <!-- 打开记录 -->
   <el-dialog height='300' @close="formCancal22" width="70%" top="4vh" title="打卡记录" :visible.sync="OpenRecordshow"
      :close-on-click-modal="false" :close-on-press-escape="false">
      <div class="import-dialog" v-if="OpenRecordlist && OpenRecordlist.length">
        <el-timeline>
          <el-timeline-item v-for="(activity, index) in OpenRecordlist" :key="index" color="#0bbd87" :size="activity.size"
            :timestamp="activity.createTime" placement="top">

            <el-card>
              <div style="display: flex;align-items: center;
  justify-content: space-between;">

                <div>
                  <h4> {{ activity.schedule }}</h4>
                </div>
                <div style="display: flex;align-items: center;
  ">

                  <div v-if="activity.userName">
                    <h4> {{ activity.userName }}-</h4>
                  </div>
                  <div>
                    <h4 v-if="activity.role == '0'">业主</h4>
                    <h4 v-if="activity.role == '1'">经销商</h4>
                    <h4 v-if="activity.role == '2'">店长</h4>
                    <h4 v-if="activity.role == '3'">导购</h4>
                    <h4 v-if="activity.role == '4'">监理</h4>
                    <h4 v-if="activity.role == '6'">瓦工</h4>
                    <h4 v-if="activity.role == '7'">设计师 </h4>
                  </div>

                </div>
              </div>
              <p v-if="activity.product">应用产品：{{ activity.product }}</p>
              <p v-if="activity.workArea">施工区域：{{ activity.workArea }}</p>

              <p v-if="activity.content">工地情况：{{ activity.content }}</p>


              <div>
                <h4 v-if="activity.image && activity.image.length"> 施工图片</h4>
              </div>
              <el-image :key="index1" v-for="(item, index1) in activity.image"
                style="width: 100px; height: 100px; margin:6px 6px;" :src="item" :preview-src-list="activity.image">
              </el-image>
              <div>
                <h4 v-if="activity.video && activity.video.length"> 施工视频</h4>
              </div>
              <div style="display: flex;align-items: center; flex-wrap: wrap;
  ">
                <div v-for="(video, index2) in activity.video" :key="index2">
                  <video :src="video" style="width: 300px;height:200px; margin:6px 6px;" class="video-avatar"
                    controls></video>
                </div>
              </div>
              <div @click="deleterecord(activity.id)" style="float: right;     padding-bottom: 20px;
  ">

                  <div v-if="activity.userName">
                  </div>
                  <div>
                    <el-button size="mini" type="danger" plain>删除</el-button>
                  </div>

                </div>
            </el-card>


          </el-timeline-item>
        </el-timeline>

      </div>
<div v-else style='text-align: center; height: 400px; line-height: 400px'>
  暂无打卡记录
</div>
      


    </el-dialog>
  </div>
</template>

<script>

import { allStoreList, teamList, exportStores } from '@/api/MD'
import { checkStoreApply,delAllStore, delSchedule, updateProjectUser, checkAccept, projectSchedule, updateAllStore, projectList, useProduct, hqComfirm, projectTeams, soldOutProject } from '@/api/XM'

import { userinfos } from '@/api/RY'
import AMapLoader from '@amap/amap-jsapi-loader'

import { upload } from '@/api/upload'
import { formatDate_RQ, Url } from '@/utils/time'
import { Loading } from 'element-ui';
// import VueEditor from 'vue-word-editor'
import 'quill/dist/quill.snow.css'
import { log } from 'console';
export default {
  components: {
    // VueEditor,

  },
  data() {
    return {
      OpenRecordshow: false,
      OpenRecordlist: [],
      OpenRecordID: '',
      OpenRecordcurPage: 1,
      roledata1: [],
      userLIstL: [],
      specialShow1: false,
      specialShow3: false,
      currentPage2: 1
      , userID: '',
      XMID: '',
      xmlist: [],
      total3: 1,
      input6: '',
      currentPage3: 1,
      specialShow2: false,
      curPage: 1,
      roleID: 2,
      storeId: '',
      activeName: 'TZ',
      currentPage1: 1,
      roledata: [],
      rolelist: [
        { label: '店长', name: 'TZ', id: 2 },
        { label: '导购', name: 'DG', id: 3 },
        { label: '设计师', name: 'MDSJS', id: 7 },
        { label: '瓦工', name: 'WG', id: 6 },
        { label: '监理', name: 'JFGCS', id: 4 },
      ],
      classify: [
        { id: 0, name: '待审核' },
        { id: 3, name: '未通过' },
        { id: 2, name: '已通过' }
      ],
      classify1: [
        { id: 0, name: '未登录' },
        { id: 1, name: '已登录' },
      ],
      input6: '',
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }],
      },
      dialogTitle: '新增文件',
      ewmShowgg: false,
      specialShow: false,
      specialShowgg: false,
      formList: {},
      data: [],
      pageSize: 10,
      value1: "",
      value2: "",
      input2: '',
      input3: '',
      input4: '',
      input5: '',
      currentPage: 1,
      handleSelectionChangeList: [],
      bookUrllist: [],
      dialogVisibleImg: false,
      formData: {
        startTime: '',
        endTime: ""
      },
      Product: {
        typeId: '',
        name: '',
        linkUrl: '',
        fileType: '',
        fileName: ''
      },
      total: 1,
      total1: 1,
      total2: 1,
      valnum: 1,
      SEDJJXCX_LHFW_UserInformation_HT: {},
      baseUrl: process.env.VUE_APP_BASE_API + "/uploadCloud?filename=11111",
      baseUrl1: process.env.VUE_APP_BASE_API + "/oe_createDatum_.csp",
      //上传后的文件列表
      fileList: [],
      // 允许的文件类型
      fileType: ["pdf", "doc", "docx", "xls", "xlsx", "txt", "png", "jpg", "bmp", "jpeg"],
      // 运行上传文件大小，单位 M
      fileSize: 50,
      // 附件数量限制
      fileLimit: 1,
      //请求头
      headers: { "Content-Type": "multipart/form-data" },
      MD: {
        storeName: '',
        storeUserName: '',
        phone: '',
        address: '',
        positionLongitude: '',
        positionLatitude: '',
        logo: '',
        status: '',
      },
      title:'', 
      totallist:1 ,    drawer: false,
      wxz: {
        add: "",
        Q: '',
        R: ''
      },
    }
  },
  mounted() {
  },
  created() {
    const SEDJJXCX_LHFW_UserInformation_HT = JSON.parse(decodeURIComponent(window.localStorage.getItem('SEDJJXCX_LHFW_UserInformation_HT')))
    this.SEDJJXCX_LHFW_UserInformation_HT = SEDJJXCX_LHFW_UserInformation_HT
    this.getGoods();
    // this.fetchData();
  },
  methods: {
    formCancal22(){
      this.OpenRecordshow=false;
      this.OpenRecordID='';
      this.OpenRecordlist=[]
    },
    OpenRecord(row) {
      this.OpenRecordID = row.id;
      const params = {
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        id: row.id,
        curPage: this.OpenRecordcurPage
      }


      projectSchedule(params).then(response => {
        this.OpenRecordshow = true;

        if (response.data.code == '1') {
          response.data.list.forEach((arr) => {
            if (arr.image) {
              arr.image = JSON.parse(arr.image)

            }
            if (arr.video) {
              arr.video = JSON.parse(arr.video)

            }
          })
          this.OpenRecordlist = response.data.list
          this.total4 = response.data.totalNum;
          // this.data.forEach((item) => {
          //   this.SupervisorList.forEach((att) => {
          //     if (item.phone == att.phone) {
          //       this.$refs.multipleTable.toggleRowSelection(att, true)
          //     }
          //   })
          // })
        } else {
          this.OpenRecordlist = response.data.list

        }
      })
    },
    CXXX(type) {
      if (this.handleSelectionChangeList && this.handleSelectionChangeList.length) {
        const list = [];
        for (var i of this.handleSelectionChangeList) {
          list.push(i.id)
        }
        var a = list.join(",");

        if (type == 1) {
          this.$confirm('此操作将永久删除店铺信息, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            var params = {
                                  dbName: window.localStorage.getItem('JJHTDBnmame'),

              id: a,

            }

            delAllStore(params).then(response => {
              if (response.data.code == '1') {
                this.getGoods()
              this.handleSelectionChangeList=[];

              }
            })
            this.$message({
              type: 'success',
              message: '删除成功!'
            });
          }).catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            });
          });


        } else {

          for (var i of this.handleSelectionChangeList) {
          if (i.status ==2) {
            this.$message({
              type: 'warning',
              message: i.storeName + '已通过审核!'

            })
            return
          }else if(i.status ==3){
            this.$message({
              type: 'warning',
              message: i.storeName + '已拒绝!'

            })
            return
          }
          
        }
        console.log(a);
          var params = {
                                dbName: window.localStorage.getItem('JJHTDBnmame'),

            listid: a,
            status: type
          }
          checkStoreApply(params).then(response => {
            if (response.data.code == '1') {
              this.$message({
                type: 'success',
                message: response.data.msg

              })
              this.getGoods()
              this.handleSelectionChangeList=[];

            }
          })

        }

      } else {
        var msg = '请选择你通过的选项！'
        if (type == 3) {
          msg = '请选择你拒绝的选项！'
        } else if (type == 1) {
          msg = '请选择你删除的选项！'

        }
        this.$message({
          type: 'warning',
          message: msg
        })
      }
    },
    handlePreview(file) {
      this.MD.logo = file.url
      this.dialogVisibleImg = true
    },
    handleSmallPicSuccess(res, file, fileList) {
      if (file && file.length == 2) {
        file.shift()
      }
      this.uploadImg(res, 2);

    },
    uploadImg(file, type) {
      this.loading = Loading.service({
        lock: true,
        text: '图片上传中........',
        // background: 'rgba(0, 0, 0, 0.7)',
        target: document.querySelector('#loadingDiv')
      })
      let formData = new FormData()
      formData.append('file', file.raw)
      upload(formData).then(response => {
        setTimeout(() => {
          this.loading.close()

        }, 2000);
        this.MD.logo = response.data.yunUrl;


      }).catch((err) => {
        console.log(err);

      });


    },
    changeIpt(poiPicker) {
      let that = this
      // console.log(that,'that')
      // console.log(poiPicker);
      that.poiPicker = poiPicker

      var marker = new AMap.Marker();

      var infoWindow = new AMap.InfoWindow({
        offset: new AMap.Pixel(0, -20)
      });

      try {
        poiPicker.on('poiPicked', function (poiResult) {
          let addData = poiResult;
console.log(poiResult)
          var source = poiResult.source,
            poi = poiResult.item,
            info = {
              name: poi.name,
              address:poi.district+ poi.address
            };


          marker.setMap(that.map);
          infoWindow.setMap(that.map);

          marker.setPosition(poi.location);
          infoWindow.setPosition(poi.location);

          infoWindow.setContent('POI信息: <pre>' + JSON.stringify(info, null, 2) + '</pre>');

          infoWindow.open(that.map, marker.getPosition());

          that.map.setCenter(marker.getPosition());
          if (addData.item.district) {
            that.wxz.add = addData.item.district + addData.item.address;

          } else {
            that.wxz.add = addData.item.address;

          }
          console.log(that.wxz.add);
          that.wxz.Q = addData.item.location.Q;
          that.wxz.R = addData.item.location.R;


        })

      } catch (error) {
        console.log(error);
      }


      // //点击选中地址
      // AMap.event.addListener(placeSearch,'markerClick',function(e){
      //   // let that = this
      //   if(e){
      //     let selectAddress = e
      //     //  console.log(e);
      //   that.$emit('getAddress',selectAddress)
      //   }
      // })
    },
    initMap() {
      const that = this
      AMapLoader.load({
        key: "175fd402b05207cb098e19762fced0c3", // 申请好的Web端开发者Key，首次调用 load 时必填
        //2.0版本太卡了 ，所以使用的1.4.0版本  其插件也有不同  如：ToolBar
        version: "1.4.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
        resizeEnable: true,
        // plugins: [
        //   "AMap.ToolBar", //工具条
        //   "AMap.Scale", // 比例尺
        //   "AMap.Geolocation", //定位
        //   "AMap.Autocomplete", //输入提示插件
        //   "AMap.PlaceSearch", //POI搜索插件
        // ], // 需要使用的的插件列表，如比例尺'AMap.Scale'等
        AMapUI: {
          version: '1.0',
          plugins: ['misc/PathSimplifier', 'overlay/SimpleMarker']//SimpleMarker设置自定义图标，PathSimplifier轨迹展示组件
        }
      })
        .then((AMap) => {
          // console.log(AMap);
          this.map = new AMap.Map("container", {
            //设置地图容器id
            viewMode: "3D", //是否为3D地图模式
            zoom: 11, //初始化地图级别
            center: that.mrcenter, //初始化地图中心点位置
          });

          AMapUI.loadUI(['misc/PoiPicker'], function (PoiPicker) {

            var poiPicker = new PoiPicker({
              //city:'北京',
              input: 'ipt'
            });

            that.changeIpt(poiPicker)
          });


          // console.log(this.map);
          // console.log(AMap);
        })
        .catch((e) => {
          console.log(e);
        });
    },
    cancelForm() {
      this.drawer = false;
      this.wxz = {
        add: '',
        Q: '',
        R: '',
      }
    },
    GETdzk() {
      this.initMap()
      this.drawer = true;
    },
    closeDrawer() {
      this.MD.address = this.wxz.add;
      this.MD.positionLongitude = this.wxz.R;
      this.MD.positionLatitude = this.wxz.Q;
      // this.mrcenter = [this.wxz.R, this.wxz.Q,];
      this.drawer = false;
    },
    // 编辑
    bjmdxx(row) {
      this.specialShow3 = true;
      this.title='修改门店信息'
      this.MD = JSON.parse(JSON.stringify(row));
      this.bookUrllist = [
        {
          url: row.logo
        }
      ]

    },
    // 批量导出
    leading1() {
      const params = {

                            dbName: window.localStorage.getItem('JJHTDBnmame'),


      }
      exportStores(params).then(response => {
        if (response.data.code == '1') {


          this.abb(response.data.url).then((blob) => {
            let fileName = '所有门店.xlsx'
            this.downloadFile(blob, fileName)

          })
          this.$message({
            type: 'success',
            message: '导出成功！'

          })
        } else {
          this.$message({
            type: 'warning',
            message: '导出失败'
          })
        }
      })



    },
    abb(url) {
      return new Promise((resolve) => {
        const aa = new XMLHttpRequest();
        aa.open("GET", url, true);
        aa.responseType = "blob";
        aa.onload = () => {
          if (aa.status === 200) {
            resolve(aa.response);
          }
        };
        aa.send();
      });
    }, downloadFile(url, fileName) {
      const aLink = document.createElement("a");
      var blob = new Blob([url]);
      aLink.href = URL.createObjectURL(blob);
      aLink.setAttribute("download", fileName); // 
      document.body.appendChild(aLink);
      aLink.click();
      document.body.removeChild(aLink);
      window.URL.revokeObjectURL(aLink.href);

    },
    formatDate(row, column, cellValue) {
      return cellValue.slice(0, 10);
    },
    handleCurrentChange3(val) {
      const params = {
        storeId: this.userID,
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: val
      }
      userinfos(params).then(response => {
        this.specialShow1 = true;
        this.total1 = response.data.totalNum;
        if (response.data.code === '1') {
          this.userLIstL = response.data.list
          // this.value2 = '';
          // this.input4 = ''
        } else {
          this.userLIstL = response.data.list

          // this.value2 = '';
        }
      })
    },
    getuser(row) {
      this.userID = row
      const params = {
        storeId: this.userID,
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: this.curPage
      }
      if (this.input5) {
        params.keyword = this.input5
      }
      userinfos(params).then(response => {
        this.specialShow1 = true;
        this.total2 = response.data.totalNum;
        if (response.data.code === '1') {
          this.userLIstL = response.data.list
          // this.value2 = '';
          // this.input4 = ''
        } else {
          this.userLIstL = response.data.list

          // this.value2 = '';
        }
      })
    },
    change4() {
      const params = {
        storeId: this.userID,
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: this.curPage,
        keyword: this.input5
      }
      userinfos(params).then(response => {
        this.specialShow1 = true;
        this.total2 = response.data.totalNum;
        this.currentPage2 = 1;
        if (response.data.code === '1') {
          this.userLIstL = response.data.list
          // this.value2 = '';
          // this.input4 = ''
        } else {
          this.userLIstL = response.data.list

          // this.value2 = '';
        }
      })
    },
    Reload1() {
      this.input5 = '';
      this.getuser();
    },
    handleClick(tab, event) {

      let role = tab.label == '店长' ? 2 : tab.label == '导购' ? 3 : tab.label == '设计师' ? 7 : tab.label == '监理' ? 4 : tab.label == '瓦工' ? 6 : 9
      
      console.log(role);
      this.roleID = role;
      const params = {
        role: role,
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: this.curPage,
        storeId: this.storeId,
      }
      teamList(params).then(response => {
        this.specialShow = true;
        this.total1 = response.data.totalNum;
        if (response.data.code === '1') {
          this.roledata = response.data.list
          // this.value2 = '';
          // this.input4 = ''
        } else {
          this.roledata = response.data.list

          // this.value2 = '';
        }
      })
    },
    team(row) {
      this.dialogTitle = '团队'
      this.storeId = row
      const params = {
        storeId: this.storeId,
        role: this.roleID,
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: this.curPage
      }
      teamList(params).then(response => {
        this.specialShow = true;
        this.total1 = response.data.totalNum;
        if (response.data.code === '1') {
          this.roledata = response.data.list
          // this.value2 = '';
          // this.input4 = ''
        } else {
          this.roledata = response.data.list

          // this.value2 = '';
        }
      })
    },
    handleCurrentChange2(val) {
      this.curPage = val
      this.team()
    },
    change2() {
      const params = {
        status: this.input3,
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: 1
      }



      if (this.input4) {
        params.keyword = this.input4;
      }
      if (this.input6) {
        params.isLogin = this.input6;
      }
      allStoreList(params).then(response => {
        this.totallist = response.data.totalNum;

        this.currentPage = 1;
        if (response.data.code === '1') {
          this.data = response.data.list
          // this.value2 = '';
          // this.input4 = ''
        } else {
          this.data = response.data.list

          // this.value2 = '';
        }
      })
    },
    change6() {
      const params = {
        isLogin: this.input6,
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: 1
      }


      if (this.input3) {
        params.status = this.input3;
      }

      if (this.input4) {
        params.keyword = this.input4;
      }

      allStoreList(params).then(response => {
        this.total = response.data.totalNum;
        this.currentPage = 1;
        if (response.data.code === '1') {
          this.data = response.data.list
          // this.value2 = '';
          // this.input4 = ''
        } else {
          this.data = response.data.list

          // this.value2 = '';
        }
      })
    },
    //上传文件之前
    beforeUpload(file) {
      this.Product.fileName = file.name;
      if (file.type != "" || file.type != null || file.type != undefined) {
        //截取文件的后缀，判断文件类型
        const FileExt = file.name.replace(/.+\./, "").toLowerCase();
        //计算文件的大小
        const isLt5M = file.size / 1024 / 1024 < 500; //这里做文件大小限制
        //如果大于50M
        if (!isLt5M) {
          this.$message.error('上传文件大小不能超过 500MB!')
          return false;
        }
        //如果文件类型不在允许上传的范围内
        if (this.fileType.includes(FileExt)) {
          return true;
        }
        else {
          this.$message.error("上传文件格式不正确!");
          return false;
        }
      }
    },
    //上传了的文件给移除的事件，由于我没有用到默认的展示，所以没有用到
    handleRemove() {
    },
    //这是我自定义的移除事件
    handleClose(i) {
      this.fileList.splice(i, 1);//删除上传的文件
      if (this.fileList.length == 0) {//如果删完了
        this.fileflag = true;//显示url必填的标识
        this.$set(this.rules.url, 0, { required: true, validator: this.validatorUrl, trigger: 'blur' })//然后动态的添加本地方法的校验规则
      }
    },
    //超出文件个数的回调
    handleExceed() {
      this.$message({
        type: 'warning',
        message: '超出最大上传文件数量的限制！'
      }); return
    },
    //上传文件的事件
    uploadFile(item) {
      this.loading = Loading.service({
        lock: true,
        text: '文件上传中........',
        // background: 'rgba(0, 0, 0, 0.7)',
        target: document.querySelector('#loadingDiv')
      })
      //上传文件的需要formdata类型;所以要转
      let FormDatas = new FormData()
      FormDatas.append('file', item.file);
      upload(FormDatas).then(res => {
        if (res.data.code == '0') {
          setTimeout(() => {
            this.loading.close()

          }, 2000);
          this.Product.linkUrl = res.data.yunUrl;
          this.Product.fileType = res.data.ext;
          // this.Product.filename=res.data.fileNameOld;
        }
      })

      // 	})
    },
    //上传成功后的回调
    handleSuccess() {

    },
    wangEditorChange(val) {
      this.Product.detail = val;
    },
    cancelDialog() { },
    submitSure() {

      const params = {

                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        storeName: this.MD.storeName,
        storeUserName: this.MD.storeUserName,
        phone: this.MD.phone,
        address: this.MD.address,
        positionLongitude: this.MD.positionLongitude,
        positionLatitude: this.MD.positionLatitude,
        logo: this.MD.logo,
        id: this.MD.id,
        status: this.MD.status,
      }

      updateAllStore(params).then((res) => {

        if (res.data.code === '1') {
          this.$message.success('修改成功')

          this.specialShow3 = false;
          this.currentPage=1

          this.getGoods();

        } else {
          this.Details = []

        }

      }).catch((err) => {
        console.log(err);
      })
    },
    // 关闭
    formCancal1() {

      this.curPage = 1;
      this.roleID = 2;
      this.storeId = '';
      this.userID = '';
      this.currentPage1 = 1;
      this.currentPage2 = 1;
      this.activeName = 'TZ';
      this.specialShow3 = false
      this.specialShow=false
      this.specialShow1=false
      this.specialShow2=false
    },
    // 添加
    increase() {
      this.specialShow = true;
    },
    // 重新加载
    Reload() {
      this.value1 = '';
      this.value2 = '';
      this.input2 = '';
      this.input3 = '';
      this.input4 = '';
      this.input5 = '';
      this.input6 = '';
      this.currentPage = 1;
      this.getGoods(1);

    },
    fetchData(val = 1) {
      const params = {
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: val
      }
      allDatumType(params).then(response => {
        if (response.data.code == '1') {
          this.classify = response.data.list
        }
      })
    },

    getGoods(val = 1) {
      const params = {
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: val
      }
      if (this.value2 && this.value2[0] && this.value2[1]) {
        params.starTime = formatDate_RQ(this.value2[0]);
        params.endTime = formatDate_RQ(this.value2[1]);
      }

      if (this.input3) {
        params.catId = this.input3;
      }
      if (this.input4) {
        params.keyword = this.input4;
      }
      if (this.input2) {
        params.status = this.input2;
      }

      allStoreList(params).then(response => {
        this.totallist = response.data.totalNum;
        if (response.data.code == '1') {
          this.data = response.data.list
        } else {
          this.data = response.data.list

        }
      })
    },
    handleCurrentChange1(val) {
      this.valnum = val;
      this.getGoods(val)

    },
    handleCurrentChange(val) {
    },
    // 删除文件
    change5() {
      const params = {
        storeId: this.userID,
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: this.curPage,
        keyword: this.input6
      }
      projectList(params).then(response => {
        this.specialShow2 = true;
        this.total3 = response.data.totalNum;
        if (response.data.code === '1') {
          this.xmlist = response.data.list
          // this.value2 = '';
          // this.input4 = ''
        } else {
          this.xmlist = response.data.list

          // this.value2 = '';
        }
      })
    },
    Reload2() {
      this.input6 = '';
      const params = {
        storeId: this.XMID,
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: 1
      }
      if (this.input5) {
        params.keyword = this.input5
      }
      projectList(params).then(response => {
        this.specialShow2 = true;
        this.total3 = response.data.totalNum;
        if (response.data.code === '1') {
          this.xmlist = response.data.list
          // this.value2 = '';
          // this.input4 = ''
        } else {
          this.xmlist = response.data.list

          // this.value2 = '';
        }
      })
    },
    handleCurrentChange4(row) {
      const params = {
        storeId: this.XMID,
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: row
      }
      if (this.input5) {
        params.keyword = this.input5
      }
      projectList(params).then(response => {
        this.specialShow2 = true;
        this.total3 = response.data.totalNum;
        if (response.data.code === '1') {
          this.xmlist = response.data.list
          // this.value2 = '';
          // this.input4 = ''
        } else {
          this.xmlist = response.data.list

          // this.value2 = '';
        }
      })
    },
    handleDelete(row) {
      this.XMID = row
      const params = {
        storeId: this.XMID,
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: this.curPage
      }
      if (this.input5) {
        params.keyword = this.input5
      }
      projectList(params).then(response => {
        this.specialShow2 = true;
        this.total3 = response.data.totalNum;
        if (response.data.code === '1') {
          this.xmlist = response.data.list
          // this.value2 = '';
          // this.input4 = ''
        } else {
          this.xmlist = response.data.list

          // this.value2 = '';
        }
      })

    },
    handleSelectionChange(val) {
      this.handleSelectionChangeList = val;
    },

    handlePreview(file) {
      this.Product.coverPic = file.url
      this.dialogVisibleImg = true
    },
    handlePreview1(file) {
      this.specifications.url = file.url
      this.dialogVisibleImg = true
    },
    handleRemove1(file, fileList) {
      // 移除

    },




    //添加文件
    RechargeRole() {

      if (this.Product.catld == '') {
        this.$message({
          showClose: true,
          message: '请选择分类！',
          type: 'warning'
        });
        return
      }
      if (this.Product.name == '') {
        this.$message({
          showClose: true,
          message: '请输入文件名称！',
          type: 'warning'
        });
        return
      }

      if (this.Product.linkUrl == '') {
        this.$message({
          showClose: true,
          message: '请上传对应文件！',
          type: 'warning'
        });
        return
      }
      console.log(this.Product);
      var params = new URLSearchParams();
      params.append('name', this.Product.name);
      params.append('linkUrl', this.Product.linkUrl);
            params.append('dbName', window.localStorage.getItem('JJHTDBnmame'));

      params.append('typeId', this.Product.typeId);
      params.append('fileType', this.Product.fileType);
      params.append('fileName', this.Product.fileName);
      params.append('id', this.Product.id ? this.Product.id : '');
      this.axios.post(this.baseUrl1, params,).then(res => {
        if (res.data.code == '1') {
          this.$message({
            type: 'success',
            message: this.dialogTitle == '修改文件' ? '修改成功' : '添加成功'

          })
          this.getGoods(this.valnum)
          this.specialShowgg = false
          this.specialShow = false;
          this.Product = {}
          this.bookUrllist = []
          this.tableOption = [];
        } else {
          this.$message({
            type: 'warning',
            message: this.dialogTitle == '修改文件' ? '修改失败' : '添加失败'

          })
        }
      }).catch(err => {
      })
      return

    },
    // 修改文件
    handleEditzt(row) {
      this.wangEditorDetail = row.detail
      this.dialogTitle = '修改文件';
      this.specialShow = true;
      this.Product = row;
      this.Product.fileName = row.linkUrl
      console.log(row);
    },

    // 时间筛选
    change() {
      this.formData.startTime = formatDate_RQ(this.value2[0])
      this.formData.endTime = formatDate_RQ(this.value2[1])
      const params = {
        dbName: "mati", curPage: 1,
        starTime: this.formData.startTime,
        endTime: this.formData.endTime,
      }
      if (this.input2) {
        params.status = this.input2;
      }
      if (this.input3) {
        params.catId = this.input3;
      }
      if (this.input4) {
        params.keyword = this.input4;
      }
      allStoreList(params).then(response => {
        this.total = response.data.totalNum;

        this.currentPage = 1;
        if (response.data.code === '1') {
          this.formData.startTime = ''
          this.formData.endTime = ''
          this.data = response.data.list
          // this.value2 = '';
        } else {
          this.data = response.data.list

          // this.value2 = '';
        }
      })
    },

    // 名字筛选
    change3() {
      const params = {
        keyword: this.input4,
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: 1
      }

      if (this.input2) {
        params.status = this.input2;
      }


      allStoreList(params).then(response => {
        this.total = response.data.totalNum;

        this.currentPage = 1;
        if (response.data.code === '1') {
          this.data = response.data.list
          // this.value2 = '';
          // this.input4 = ''
        } else {
          this.data = response.data.list

          // this.value2 = '';
          // this.input4 = ''
        }
      })
    },
    // 批量导出
    // leading(){
    //   if (this.handleSelectionChangeList && this.handleSelectionChangeList.length) {
    //     let url = 'https://cdn.juesedao.cn/huiya/682fe11b0a2648d19f07e9d8b78279c2'
    //     const a = document.createElement('a')
    //     a.href = url
    //     a.download = '测试'// 下载后文件名
    //     a.style.display = 'none'
    //     document.body.appendChild(a)
    //     a.click() // 点击下载
    //     document.body.removeChild(a) // 下载完成移除元素

    //   } else {
    //     this.$message({
    //       type: 'warning',
    //       message: '请选择你导出的选项'
    //     })
    //   }
    // },



    handleCheck(index, row) {
      this.IMGindex = index;
    },
    addmkdxx(){
      this.qcsj()
      this.specialShow3=true
      this.title='新建门店信息'


    },
    qcsj(){
    this.MD={
     storeName: '',
        storeUserName: '',
        phone: '',
        address: '',
        positionLongitude: '',
        positionLatitude: '',
        logo: '',
        status: '',
        id:''
    }

  }




  },
};
</script>

<style lang="scss" scoped>
.el-link.is-underline::after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  height: 0;
  bottom: 0;
  border-bottom: 1px solid #1890ff;
}

.el-row {
  margin-bottom: 20px;
}

.el-row:last-child {
  margin-bottom: 0;
}

.el-tag+.el-tag {
  margin-left: 10px;
}



.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}

.input-new-tag {
  width: 300px;
  margin-left: 10px;
  vertical-align: bottom;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.el-button--medium {
  margin-top: 10px;
  margin-left: 600px;
  height: 45px;
  width: 100px;
}

.el-input_ss {
  width: 200px;
}

.el-input_ss1 {

  margin-top: 6px;
}

.btn_jia {
  position: relative;
  margin-left: 8px;
  margin-top: 20px;
}

.btn_jia:hover {
  font-weight: 600;
  text-decoration: underline;
}

.btn_jia::after {
  content: "";
  width: 2px;
  height: 20px;
  background: #000;
  position: absolute;
  top: 10px;
  right: 33px;
}

.status_shop {
  margin-right: 8px;
}

// 分页
.paging {
  width: 100%;
  height: 100%;
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

// 功能按钮
.function_btn_box {
  width: 100%;
  display: flex;
  margin-right: 10px;
  margin-bottom: 20px;
}

.input-with-select {
  margin-left: 50px;
}

.float_rigth {
  float: right;
}

// .el-input.inp {
//   width: auto !important;
// }</style>
<style lang="scss" scoped>
.dashboard {
  &-container {
    margin: 30px;
  }

  &-text {
    font-size: 30px;
    line-height: 46px;
  }
}
</style>

<style scoped>
#container {
  padding: 0px;
  margin: 0px;
  width: 100%;
  height: 400px;
}

.dialog-footer {
  display: flex;
  justify-content: end;
  margin-top: 20px;
}

.dashboard-container {
  width: 50%;
  margin: 0 auto;
  margin-top: 20px;
}
</style>
