<template>
    <div class="app-container">

        <!-- 添加按钮 -->
        <el-form :inline="true" class="user-search">
            <el-form-item>
                <!-- <el-button size="mini" @click="handleEdit" type="primary">添加</el-button> -->

            </el-form-item>
        </el-form>
        <!-- 表格 -->
        <el-table :data="data" border fit highlight-current-row>
            <el-table-column align="center" label="序号">
                <template slot-scope="scope">{{ scope.$index + 1 }}</template>
            </el-table-column>
            <el-table-column align="center" prop="module" label="姓名">
                <template slot-scope="scope">{{ scope.row.name }}</template>

            </el-table-column>
            <el-table-column align="center" prop="module" label="电话">
                <template slot-scope="scope">{{ scope.row.phone }}</template>

            </el-table-column>
            <el-table-column align="center" prop="module" label="地址">
                <template slot-scope="scope">{{ scope.row.address }}</template>

            </el-table-column>
            <el-table-column align="center" prop="module" label="留言内容">
                <template slot-scope="scope">{{ scope.row.sex }}</template>

            </el-table-column>
           
            <el-table-column align="center" prop="name" label="是否联系">
              <template slot-scope="scope">
                  <el-tag v-if="scope.row.status == '1'">是</el-tag>
                  <el-tag v-if="scope.row.status == '0'" type="danger">否</el-tag>
              </template>

          </el-table-column>

            <el-table-column fixed="right" label="操作" align="center">
                <template slot-scope="scope">
                    <el-button size="mini" @click="handleEdit(scope.$index, scope.row)">编辑</el-button>

                    <el-button size="mini" @click="deleteUser(scope.$index, scope.row)" type="danger">删除</el-button>

                </template>
            </el-table-column>
            </el-table-column>
        </el-table>
        <el-pagination :current-page.sync="currentPage" background style="margin-top:30px" :page-size="pageSize"
            layout="total,prev,pager,next,jumper" :total="total" @current-change="handleCurrentChange1" />

        <!-- 编辑界面 -->
        <el-dialog :title="title" :visible.sync="editFormVisible" width="30%" @close="closeDialog">
            <el-form label-width="80px">
                <el-form-item label="姓名:">
                    <el-input  size="mini" v-model="authority.name" auto-complete="off"
                        placeholder="请输入姓名"></el-input>
                </el-form-item>
                <el-form-item label="电话:">
                    <el-input  size="mini" v-model="authority.phone" auto-complete="off"
                        placeholder="请输入电话"></el-input>
                </el-form-item><el-form-item label="地址:">
                    <el-input  size="mini" v-model="authority.address" auto-complete="off"
                        placeholder="请输入地址"></el-input>
                </el-form-item><el-form-item label="留言内容:">
                    <el-input  type="textarea"  size="mini" v-model="authority.sex" auto-complete="off"
                        placeholder="请输入留言内容"></el-input>
                </el-form-item>
                <el-form-item label="是否联系:">
                    <el-radio-group v-model="authority.status">
                        <el-radio :label="1">是</el-radio>

                        <el-radio :label="0">否</el-radio>

                    </el-radio-group>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button size="mini" @click="closeDialog">取消</el-button>
                <el-button size="mini" type="primary" class="title" @click="submitForm()">保存</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { delModule, contactUs } from '@/api/TP'
import { log } from 'console'

export default {
    data() {
        return {
            editFormVisible: false,
            data: [],
            currentPage: 1,
            total: 1,
            pageSize: 10,
            title: '添加',
            authority: {
                name: '',
                phone: '',
                address: '',
                sex: '',
                status: 1,
                id: '',
            },
            SEDJJXCX_LHFW_UserInformation_HT: {}
        }
    },
    created() {
        const SEDJJXCX_LHFW_UserInformation_HT = JSON.parse(decodeURIComponent(window.localStorage.getItem('SEDJJXCX_LHFW_UserInformation_HT')))
        this.SEDJJXCX_LHFW_UserInformation_HT = SEDJJXCX_LHFW_UserInformation_HT
        this.fetchData()
    },
    methods: {
        changeMessage() {
            //强制刷新渲染
            this.$forceUpdate()
        },
        fetchData(val = 1) {
            const params = {
                                    dbName: window.localStorage.getItem('JJHTDBnmame'),

                curPage: val,
                number:10,
                flag:'query'
            }
            contactUs(params).then(response => {
                this.total = response.data.sumcount
                if (response.data.code == '1') {
                    this.data = response.data.list
                }
            })
        },
        handleCurrentChange1(val) {
            this.fetchData(val)
        },
        handleEdit: function (index, row) {

            this.editFormVisible = true;
            if (row != undefined && row != 'undefined') {
                this.title = '修改'
                this.authority=row
            } else {
                this.title = '添加'
                this.authority={
                    name: '',
                phone: '',
                address: '',
                sex: '',
                status: 1,
                id: '',
                }
            }
        },

        // 关闭编辑、增加弹出框
        closeDialog() {
            this.editFormVisible = false;
            this.deptName = '';
            this.authority = {};
        },
        // 编辑、增加页面保存方法
        submitForm() {
                const params = {
                    name: this.authority.name,
                    phone: this.authority.phone,
                    address: this.authority.address,
                    sex: this.authority.sex,
                    status: this.authority.status,
                    flag: 'save',
                                        dbName: window.localStorage.getItem('JJHTDBnmame'),

                    id: this.title == '修改' ? this.authority.id : ''
                }

                contactUs(params).then(response => {
                    if (response.data.code == '1') {
                        this.editFormVisible = false
                        this.fetchData()
                        this.$message({
                            type: 'success',
                            message: this.title == '修改' ? '修改成功！' : '创建成功！'

                        })
                    } else {
                        this.$message({
                            type: 'warning',
                            message: '创建失败！'
                        })
                    }
                })

        },
        deleteUser(index, row) {
            this.$confirm('此操作将永久删除, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                const params = {
                    id: row.id,
                    flag:'delete',
                                        dbName: window.localStorage.getItem('JJHTDBnmame'),

                }; contactUs(params).then(response => {
                    if (response.data.code == '1') {
                        this.fetchData()
                    }
                })
                this.$message({
                    type: 'success',
                    message: '删除成功!'
                });
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });

        }
    }
}
</script>

<style lang="scss" scoped>
.el-row {
    margin-bottom: 20px;
}

.el-row:last-child {
    margin-bottom: 0;
}

.avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.avatar-uploader .el-upload:hover {
    border-color: #409eff;
}

.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}

.avatar {
    width: 178px;
    height: 178px;
    display: block;
}

.el-button--medium {
    margin-top: 10px;
    margin-left: 600px;
    height: 45px;
    width: 100px;
}

.el-input {
}

// .el-input.inp {
//   width: auto !important;
// }</style>
