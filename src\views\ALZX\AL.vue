<template>
  <div class="dashboard-container">

    <el-form :inline="true" class="user-search">

      <el-form-item label="上线状态:">
        <el-select size="small" placeholder="上线状态" @change="change1" v-model="input1">
          <el-option label="上线" value="1"></el-option>
          <el-option label="下线" value="0"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-input size="small" placeholder="请输入内容" v-model="input4" class="el-input_ss1">
          <el-button size="small" slot="append" icon="el-icon-search" @click="change1"></el-button>
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-button size="mini" type="primary" @click="addincrease('ruleForm')">添加</el-button>

        <el-button size="mini" type="info" @click="getgetselectCase1('1')">重新加载</el-button>

        <!-- 搜索筛选 -->


      </el-form-item>
    </el-form>

    <el-table border :data="data" fit highlight-current-row @selection-change="handleSelectionChange">

      <el-table-column align="center" type="index" label="序号" width="100" />



      <el-table-column label="案例封面图" align="center" width="100">
        <template slot-scope="scope">
          <el-popover placement="bottom" trigger="hover" width="300">
            <img :src="scope.row.zhuye" width="100%" />
            <img slot="reference" :src="scope.row.zhuye" :alt="scope.row.zhuye"
              style="max-height: 30px;max-width: 30px; " />
          </el-popover>
        </template>
</el-table-column>
      <el-table-column align="center" label="案例名称">
        <template slot-scope="scope">{{ scope.row.name }}</template>
      </el-table-column>
      <el-table-column align="center" label="城市">
        <template slot-scope="scope">{{ scope.row.chengshi ? scope.row.chengshi : '暂无' }}</template>
      </el-table-column>



      <el-table-column align="center" label="上线状态" width="100">
        <template slot-scope="scope">
          <el-tag size="medium" effect="success" v-if="scope.row.status == '1'">
            上线
          </el-tag>

          <el-tag size="medium" v-if="scope.row.status == '0'" effect="danger">
            未上线
          </el-tag>
        </template>
      </el-table-column>




      <el-table-column align="center" label="创建时间">
        <template slot-scope="scope">{{ scope.row.createTime }}</template>
      </el-table-column>







      <el-table-column fixed="right" label="操作" width="200" align="center">
        <template slot-scope="scope">
          <el-button size="mini" icon="el-icon-edit" type="primary" @click="handleEditzt(scope.row)">编辑</el-button>
          <el-button type="warning" icon="el-icon-delete" size="mini"
            @click="deleteclassification(scope.row)">删除</el-button>

        </template>
      </el-table-column>
    </el-table>
    <el-row>
      <el-pagination class="pagdw" :current-page.sync="currentPage" background :page-size="pageSize"
        layout="total,prev,pager,next,jumper" :total="total" @current-change="handleCurrentChange1" />

    </el-row>

    <!-- 编辑界面 -->
    <el-dialog @close="closeDialog" :title="title" :visible.sync="editFormVisible" width="60%" top="6vh">

      <div class="import-dialog">
        <el-row>
          <el-col :span="24">
            <el-form label-width="120px" ref="editForm">
              <el-form-item label="案例名称:" prop="form.name">
                <el-input size="small" v-model="form.name" auto-complete="off" placeholder="请输入案例名称"></el-input>
              </el-form-item>

            </el-form>
          </el-col>

        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form label-width="120px" ref="editForm">
              <el-form-item label="城市:" prop="form.chengshi">
                <el-input size="small" v-model="form.chengshi" auto-complete="off" placeholder="请输入城市"></el-input>
              </el-form-item>

            </el-form>
          </el-col>

        </el-row>



        <el-row>
          <el-col :span="24"><el-form label-width="120px" ref="editForm">
              <el-form-item label="是否上线:" prop="form.status">
                <el-radio v-model="form.status" :label="1">是</el-radio>
                <el-radio v-model="form.status" :label="0">否</el-radio>
              </el-form-item>

            </el-form>
          </el-col>

        </el-row>



        <el-row>

          <el-col :span="24"><el-form label-width="120px" ref="editForm">
              <el-form-item label="排序:" prop="form.sort">
                <el-input size="small" v-model="form.sort" auto-complete="off" placeholder="请输入排序"></el-input>
              </el-form-item>

            </el-form>
          </el-col>
        </el-row>

        <el-row>


          <el-col :span="24">
            <el-form label-width="120px" ref="editForm">
              <el-form-item label="案例简介:" prop="form.productModel">
                <wangEditor v-model="wangEditorDetail" :isClear="isClear" @change="wangEditorChange"></wangEditor>

              </el-form-item>

            </el-form>
          </el-col>

        </el-row>
        <el-row>
          <el-col :span="24" v-for="(item, index) in classification" :key="item.id">
            <el-form label-width="120px" ref="editForm">
              <el-form-item :label=item.name>
                <el-checkbox-group v-model="checkList[index]" @change="getshow(index)">
                  <!-- <el-checkbox :value=items.id :label=items.name v-for="(items,indexs) in item.children" :key="items.id"></el-checkbox> -->
                  <el-checkbox :label=items.id v-for="(items, indexs) in item.children" :key="items.id">{{ items.name
                    }}</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-form>
          </el-col>

        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form label-width="120px" ref="editForm">

              <el-form-item label="案例封面图:" prop="form.zhuye">
                <el-upload action="#" list-type="picture-card" :on-preview="handlePreview" :on-remove="handleRemove1"
                  :auto-upload="false" :file-list="bookUrllist" multiple :on-change="handleSmallPicSuccess">
                  <i class="el-icon-plus" />
                </el-upload>
                <el-dialog :visible.sync="dialogVisibleImg" top="0" center :modal="false">
                  <img width="100%" :src="form.zhuye" alt>
                </el-dialog>
              </el-form-item>
            </el-form>


          </el-col>

        </el-row>



        <el-row>
          <el-col :span="24">
            <el-form label-width="120px" ref="editForm">

              <el-form-item label="空间效果图:" prop="form.productImages">
                <el-upload action="#" list-type="picture-card" :on-preview="handlePreview1" :on-remove="handleRemove1"
                  :auto-upload="false" :file-list="bookUrllist3" multiple :on-change="handleSmallPicSuccess1">
                  <i class="el-icon-plus" />
                </el-upload>


                <el-dialog :visible.sync="dialogVisibleImg3" top="0" center :modal="false">
                  <img width="100%" :src="GJ_img" alt>
                </el-dialog>
              </el-form-item>
            </el-form>
          </el-col>

        </el-row>

        <el-row>
          <el-col :span="24">

            <el-form label-position="top" label-width="120px" ref="editForm">

              <el-form-item label="案例选择产品" prop="uploadFile">

                <el-transfer style="text-align: left; display: inline-block" v-model="value4" filterable
                  :right-default-checked="this.value4" :titles="['未选中', '已选中']" :button-texts="['取消选中', '选中产品']"
                  :format="{
                    noChecked: '${total}',
                    hasChecked: '${checked}/${total}'
                  }" @change="handleChange" :data="data1">
                  <span slot-scope="{ option }">{{ option.key }} - {{ option.label }}</span>
                </el-transfer>


              </el-form-item>
              <!-- <el-form-item label="选择文件">
  <el-upload class="upload-demo" ref="upload" :action="action" :on-preview="handlePreview"
    :on-remove="handleRemove8" :before-upload="beforeUpload" multiple :limit="1" :on-change="fileChange"
    :on-success="handleSuccess" :on-exceed="handleExceed" :file-list="fileList">
    <el-button size="small" type="primary">上传文件</el-button>
    <span slot="tip" class="text">请上传.pdf文件</span>
  </el-upload>

</el-form-item> -->
            </el-form>



          </el-col>

        </el-row>



      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="RechargeRole('form')">确 定</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { Loading } from 'element-ui';
import { upload } from '@/api/upload'
import wangEditor from "@/components/wangEditor/wangEditor.vue";
import { log } from 'console';

export default {
  components: {
    // VueEditor,
    wangEditor
  },
  data() {

    return {
      data1: [],
      value: [],
      value4: [],
      filterMethod(query, item) {
        return item.pinyin.indexOf(query) > -1;
      },
      isClear: false,//设置为true的时候，这个可以用this.wangEditorDetail=''来替代
      wangEditorDetail: "",
      title: '',
      editFormVisible: false,
      classification: [],

      data: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      SEDJJXCX_LHFW_UserInformation_HT: {},
      gg: {
        name: '',
        displayOrder: '',
        status: 1

      },
      AL: {
        name: "",
        displayOrder: '',
        level: '',
        pId: '',
        status: 1
      },
      form: {
        name: '',
        caseDetail: '',
        chengshi: '',
        projectAddress: '',
        mianji: '',
        vrAddress: '',
        caseVideo: '',
        zhuye: '',
        images: [],
        status: 1,
        paixu: 0,
        typeId: []
      },
      bookUrllist2: [],
      bookUrllist1: [],
      Subitemshow: false,
      //上传后的视频列表
      fileList: [],
      // 允许的视频类型
      fileType: ["pdf", "doc", "docx", "xls", "xlsx", "txt", "png", "jpg", "bmp", "jpeg", 'mp4', 'ogg', 'flv', 'avi', 'wmv', 'rmvb', 'mov'],
      // 运行上传视频大小，单位 M
      fileSize: 50,
      // 附件数量限制
      fileLimit: 1,
      //请求头
      headers: { "Content-Type": "multipart/form-data" },
      dialogVisibleImg: false,
      curPage: 1,
      currentPage: 1,
      total: 1,
      pageSize: 10,
      bookUrllist: [],
      bookUrllist1: [],
      bookUrllist2: [],
      bookUrllist3: [],
      bookUrllist4: [],
      form: {
        name: '',
        productModel: '',
        productVr: '',
        productSynopsis: '',
        productImage: '',
        productImages: [],
        status: 0,
        productView: '',
        productId: [],
        preview: '',
        productUrl: [],
        photo: [],
        productPaixu: 0,
        qrImage: '',
        style: '',
        space: '',
        colour: '',
        space: '',
        series: '',
        longpic: '',
        formNum: '',
        yuliuone: '',
        productId: '',
        caseProductId: '',
        sort: '',
        topFlag: 0
      },
      productUrlindex: 1,
      productImageslindex: 1,
      curPage: 1,
      limitnum: 10,
      checkList: [
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
      ],
      action: process.env.VUE_APP_BASE_API + "/uploadCloud?filename=11111",
      tableData: [{
        namem: '',
        introduction: '',
        kongjianimages: [],
        producttype: '',


      }],
      IMGindex: 0,
      GJ_img: '',
      gg_img: '',
      input1: '',
      input2: '',
      input3: '',
      input4: '',
      GGlist: [],
      XLList: [],
      dialogVisibleImg1: false,
      dialogVisibleImg2: false,
      dialogVisibleImg3: false,
      dialogVisibleImg4: false,
      isHandlingChange: false, // 添加一个标识来判断是否正在处理change事件
      xz_lingth: ''

    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    }
  },
  methods: {
    handleChange(value, direction, movedKeys) {
      this.form.productId = movedKeys.join()
      this.form.caseProductId = movedKeys.join()
      console.log(value, direction, movedKeys);
    },
    generateData() {


      var params = new URLSearchParams();
            params.append('dbName', window.localStorage.getItem('JJHTDBnmame'));



      this.$store.dispatch('CP/getAllProduct', params).then((res) => {
        if (res.code == '1') {
          var a = [];

          res.record.forEach((item, index) => {
            a.push({
              label: item.name + '-' + item.productModel,
              key: item.id,
              pinyin: res.record[index]
            });

          });
          this.data1 = a

        } else {
          this.data1 = []
        }

      }).catch(() => {
      })



    },
    wangEditorChange(e) {
      this.form.caseDetail = e;
    },
    // 删除案例
    deleteclassification(row) {
      this.$confirm('此操作将永久删除案例, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        var params = new URLSearchParams();
        params.append('id', row.id);
              params.append('dbName', window.localStorage.getItem('JJHTDBnmame'));


        this.$store.dispatch('CP/delCase', params).then((res) => {

          if (res.code >= 0) {

            this.$message({
              type: 'success',
              message: '删除成功！'
            })
            this.getqueryCaseType();

          } else {

          }

        }).catch(() => {
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });

    },
    getgetselectCase1() {
      this.input1 = ''
      this.input2 = ''
      this.input3 = ''
      this.input4 = ''
      this.curPage = 1;
      this.getqueryCaseType()
    },
    // 上下架筛选
    change1(row) {


      var params = new URLSearchParams();
            params.append('dbName', window.localStorage.getItem('JJHTDBnmame'));

      params.append('curPage', this.curPage);
      console.log(this.input1);
      console.log(this.input2);
      if (this.input1) {
        params.append('status', this.input1);

      }

      if (this.input2) {
        params.append('size', this.input2);

      }
      if (this.input3) {
        params.append('series', this.input3);

      }
      if (this.input4) {
        params.append('name', this.input4);
      }


      this.$store.dispatch('CP/getproductList', params).then((res) => {
        this.total = res.totalNum;
        this.currentPage = 1;

        if (res.code == '1') {
          this.data = res.list
        } else {
          this.data = []
        }

      }).catch(() => {
        this.loading = false
      })
    },

    onPauser() {
      this.playing = false

    }, onPaly() {

      this.playing = true
    },
    getshow(e) {
      // if (this.checkList[e].length > 1) {
      //   this.checkList[e].splice(0, 1)
      // }
    },
    addincrease() {
      this.editFormVisible = true;
      this.title = '添加';
      this.wangEditorDetail = ''
      this.form = {
        name: '',
        productModel: '',
        productVr: '',
        productSynopsis: '',
        productImage: '',
        productImages: [],
        status: 0,
        productView: '',
        productId: [],
        preview: '',
        productUrl: [],
        photo: [],
        productPaixu: 0,
        qrImage: '',
        style: '',
        space: '',
        colour: '',
        space: '',
        series: '',
        longpic: '',
        formNum: '',
        yuliuone: '',
        productId: '',
        caseProductId: '',
        sort: '',
        topFlag: 0
      };

      this.activeName = '1'
      this.checkList = [
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
      ]
    },
    // 编辑
    handleEditzt(row) {
      this.editFormVisible = true;
      this.form = row;

      if (row.space) {
        let ids = row.space.split(',');
        this.classification.forEach((item) => {
          if(item.children){
item.children.forEach((b, index1) => {
            ids.forEach((a, index) => {

              if (a == b.id) {
                this.checkList[index].push(Number(a))
              }

            })

          })
          }
          
        })
console.log(this.checkList);
      }
      // if (row.style || row.space || row.colour) {
      //   if (row.style) {
      //     row.style = row.style.split(",").map(Number)

      //   }
      //   if (row.space) {
      //     row.space = row.space.split(",").map(Number)

      //   }
      //   if (row.colour) {
      //     row.colour = row.colour.split(",").map(Number)

      //   }
      // }



      // if ("sndwz" == 'ymsyzweb' || "sndwz" == 'msweb') {
      //   if (row.colour && row.colour.length > 0) {


      //     this.checkList[0] = row.colour
      //   }
      //   if (row.space && row.space.length > 0) {

      //     this.checkList[1] = row.space

      //   }
      // } else {
      //   if (row.style && row.style.length > 0) {


      //     this.checkList[0] = row.style
      //   }
      //   if (row.colour && row.colour.length > 0) {

      //     this.checkList[1] = row.colour

      //   }
      //   if (row.space && row.space.length > 0) {

      //     this.checkList[2] = row.space

      //   }
      // }


      this.title = '编辑';
      if (row.productId) {
        this.value4 = row.productId.split(',').map(Number)

      }
      this.wangEditorDetail = row.caseDetail;
      if (row.images) {
        this.tableData = JSON.parse(row.images);
        // this.tableData.forEach((item) => {
        //   item.kongjianimages = JSON.parse(item.kongjianimages);
        // })
      }
      // this.formimages = res.records;
      if (row.zhuye) {
        this.bookUrllist = [
          {
            url: row.zhuye
          }
        ]
      }





      if (row.photo == '') {

        if (row.photo == '') {
          this.form.photo = [];
        } else {
          this.form.photo = JSON.parse(row.photo);
          this.bookUrllist3 = this.form.photo;
        }
      } else {
        this.form.photo = JSON.parse(row.photo);
        this.bookUrllist3 = this.form.photo;
      }






    },
    //上传视频之前
    beforeUpload1(file) {
      if (file.type != "" || file.type != null || file.type != undefined) {
        //截取视频的后缀，判断视频类型
        const FileExt = file.name.replace(/.+\./, "").toLowerCase();
        //计算视频的大小
        const isLt5M = file.size / 1024 / 1024 < 500; //这里做视频大小限制
        //如果大于50M
        if (!isLt5M) {
          this.$message.error('上传视频大小不能超过 500MB!')
          return false;
        }
        //如果视频类型不在允许上传的范围内
        if (this.fileType.includes(FileExt)) {
          return true;
        }
        else {
          this.$message.error("上传视频格式不正确!");
          return false;
        }
      }
    },
    //上传了的视频给移除的事件，由于我没有用到默认的展示，所以没有用到
    handleRemove() {
    },
    //这是我自定义的移除事件
    handleClose(i) {
      this.fileList.splice(i, 1);//删除上传的视频
      if (this.fileList.length == 0) {//如果删完了
        this.fileflag = true;//显示url必填的标识
        this.$set(this.rules.url, 0, { required: true, validator: this.validatorUrl, trigger: 'blur' })//然后动态的添加本地方法的校验规则
      }
    },
    //超出视频个数的回调
    handleExceed() {
      this.$message({
        type: 'warning',
        message: '超出最大上传视频数量的限制！'
      }); return
    },
    //上传视频的事件
    uploadFile(item) {
      this.loading = Loading.service({
        lock: true,
        text: '视频上传中........',
        // background: 'rgba(0, 0, 0, 0.7)',
        target: document.querySelector('#loadingDiv')
      })
      //上传视频的需要formdata类型;所以要转
      let FormDatas = new FormData()
      FormDatas.append('file', item.file);
      upload(FormDatas).then(res => {
        if (res.data.code == '0') {
          setTimeout(() => {
            this.loading.close()

          }, 2000);
          this.form.caseVideo = res.data.yunUrl;
          this.$set(this.form, "videoUrl", this.form.caseVideo + '/' + 1)
          console.log(this.form.caseVideo);
          // this.Product.filename=res.data.fileNameOld;
        }
      })

      // 	})
    },
    //上传成功后的回调
    handleSuccess() {

    },
    // 添加案例
    RechargeRole() {

      // if (!this.form.name) {
      //   this.$message({
      //     showClose: true,
      //     message: '请输入案例名称！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (!this.form.productModel) {
      //   this.$message({
      //     showClose: true,
      //     message: '请输入案例型号！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (!this.form.formNum) {
      //   this.$message({
      //     showClose: true,
      //     message: '请输入案例面数！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (!this.form.productVr) {
      //   this.$message({
      //     showClose: true,
      //     message: '请输入案例vr链接！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (!this.form.productView) {
      //   this.$message({
      //     showClose: true,
      //     message: '请输入案例视频链接！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (!this.form.productSynopsis) {
      //   this.$message({
      //     showClose: true,
      //     message: '请输入案例简介！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (this.checkList && this.checkList[0].length == 0) {
      //   this.$message({
      //     showClose: true,
      //     message: '请选择案例！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (this.checkList && this.checkList[1].length == 0) {
      //   this.$message({
      //     showClose: true,
      //     message: '请选择系列！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (this.checkList && this.checkList[2].length == 0) {
      //   this.$message({
      //     showClose: true,
      //     message: '请选择色彩！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (this.checkList && this.checkList[3].length == 0) {
      //   this.$message({
      //     showClose: true,
      //     message: '请选择规格！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (this.checkList && this.checkList[4].length == 0) {
      //   this.$message({
      //     showClose: true,
      //     message: '请选择系列！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (!this.form.productImage) {
      //   this.$message({
      //     showClose: true,
      //     message: '请上传案例封面图！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (!this.form.productUrl) {
      //   this.$message({
      //     showClose: true,
      //     message: '请上传案例单片图！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (!this.form.qrImage) {
      //   this.$message({
      //     showClose: true,
      //     message: '请上传二维码！',
      //     type: 'warning'
      //   });
      //   return
      // }
      // if (this.form.productImages && this.form.productImages.length == 0) {
      //   this.$message({
      //     showClose: true,
      //     message: '请上传案例展示图！',
      //     type: 'warning'
      //   });
      //   return
      // }

      console.log(this.form);
      // if(this.title =='添加'){
      if (this.form.photo && this.form.photo.length != 0) {
        let obj = {}
        this.form.photo = this.form.photo.reduce(function (item, next) {
          obj[next.url] ? '' : (obj[next.url] = true && item.push(next))
          return item
        }, [])
      }

      this.tableData.forEach((item) => {
        item.images = JSON.stringify(item.images)
      })


      var hb = [...this.checkList[0], ...this.checkList[1], ...this.checkList[2], ...this.checkList[3]]

      this.form.photo = this.form.photo && this.form.photo.length == 0 ? '' : JSON.stringify(this.form.photo)
      var params = new URLSearchParams();

            params.append('dbName', window.localStorage.getItem('JJHTDBnmame'));

      params.append('name', this.form.name ? this.form.name : '');
      params.append('zhuye', this.form.zhuye ? this.form.zhuye : '');
      params.append('projectAddress', this.form.projectAddress ? this.form.projectAddress : '');
      params.append('mianji', this.form.mianji ? this.form.mianji : '');
      params.append('chengshi', this.form.chengshi ? this.form.chengshi : '');
      params.append('caseDetail', this.form.caseDetail ? this.form.caseDetail : '');
      params.append('caseVideo', this.form.caseVideo ? this.form.caseVideo : '');
      params.append('status', '1');
      params.append('topFlag', this.form.topFlag ? this.form.topFlag : '');
      params.append('style', this.form.style ? this.form.style : '');
      params.append('photo', this.form.photo ? this.form.photo : '');
      params.append('space', hb && hb.length > 0 ? hb.join() : '');
      // params.append('images', JSON.stringify(this.tableData));
      params.append('id', this.form.id ? this.form.id : '');
      params.append(' caseProductId', this.form.caseProductId ? this.form.caseProductId : '');
      params.append('productId', this.form.productId ? this.form.productId : '');


      this.$store.dispatch('CP/getcreateCase', params).then((res) => {

        if (res.code == '1') {
          this.$message({
            type: 'success',
            message: this.title == '编辑' ? '修改成功' : '添加成功'

          })
          this.getqueryCaseType();
          this.editFormVisible = false;

        } else {
        }

      }).catch(() => {
        this.loading = false
      })
    },
    uploadSectionFile() { },
    // 删除规格
    handleClick(index, row) {
      if (index == '0') {
        this.$message({
          type: 'warning',
          message: '规格中最后一项不可删除！'
        })
      } else {
        this.tableData.splice(index, 1)
      }
    },
    handleUploadAgain() {
      this.isHandlingChange = false

    },
    handleCheck(index, row) {
      console.log(index);
      this.IMGindex = index;

    },
    handlePreview11(file) {
      this.dialogVisibleImg4 = true
      this.gg_img = file.url

    },

    handlePreviewgg(file) {
      this.dialogVisibleImg3 = true
      this.GJ_img = file.url

    },
    handlePreview(file) {
      this.form.zhuye = file.url
      this.dialogVisibleImg = true
    },
    handlePreview1(file) {
      this.GJ_img = file.url
      this.dialogVisibleImg3 = true
    },
    handlePreviewcp(file) {
      this.form.productUrl = file.url
      this.dialogVisibleImg1 = true
    },

    // 点击取消
    formCancal() {
      this.specialShowgg = false;
      // this.$refs.vueEditor.editor.root.innerHTML = '';
      // this.tableOption = [];
      // this.bookUrllist = [];
      if (this.tableData && this.tableData[0] && this.tableData[0].num == '') {
        // this.Product.useAttr = false;
      }
      if (this.dialogTitle == '新增商品') {
        this.tableData = [{
          namem: '',
          introduction: '',
          kongjianimages: [],
          producttype: '',
        }]
      }

    },
    handleCurrentChange() { },
    // 添加一行
    add() {

      let obj = {
        namem: '',
        introduction: '',
        kongjianimages: [],
        producttype: '',

      }
      this.tableData.push(obj)
    },
    // 关于上传pdf部分 start
    handleSuccess(res, file) {  // 上传成功的钩子
      this.form.yuliuone = file.response.yunUrl

    },
    fileChange(file, fileList) {  //文件状态改变时的钩子，添加文件、上传成功和上传失败时都会被调用

      this.fileList = fileList;

    },
    8(file, fileList) {//文件列表移除文件时的钩子
      this.form.yuliuone = ''

    },
    handlePreview1111(file) {//点击文件列表中已上传的文件时的钩子
    },
    handleExceed(files, fileList) {
      this.$message.warning(`当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);
    },
    beforeUpload(file) {//文件上传之前的钩子函数
      this.file = file;
      this.fileName = file.name;
      // this.fileSize = file.size;
      const extension = file.name.split('.').slice(-1) == 'pdf'
      if (!extension) {
        this.$message.warning('上传模板只能是pdf格式!')
        return false
      }
      // let reader = new FileReader();
      // reader.readAsDataURL(file);
      // console.log(new FileReader().readAsDataURL(file),'reader.readAsDataURL(file)')
      // console.log(reader.result,'reader.result')

      // let that = this;
      // reader.onload = function() {
      //   that.fileData = reader.result;
      // };
      // console.log(that.fileData,'that.fileData')
      // return false; // 返回false不会自动上传
    },
    // 删除文件之前的钩子
    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`);
    },
    formCancal2() { },
    formCancal1() { },
    uploadFileError(res, file, fileList) {
    },
    handleBeforeUpload(file) {

    },
    uploadFileSuccess(res, file, fileList) {
      if (res.code == 0) {
        fileList.forEach((att) => {
          if (att.response) {
            let obj = {
              url: ""
            }
            if (file.response.yunUrl) {
              this.form.productUrl = file.response.yunUrl
            }

          }
        })

      }

    },
    uploadFileSuccess1(res, file, fileList) {
      console.log(211111);

      // this.form.productImages = [];
      if (res.code == 0) {
        fileList.forEach((att) => {
          console.log(att);
          if (att.response) {
            let obj = {
              url: ""
            }
            obj.url = file.response.yunUrl;
            this.form.photo.push(obj)
          }
        })
      }




    },
    exceedFile(res, file, fileList) {
      this.$message.error('只能上传' + this.limitnum + '个文件');
    },
    handleSmallPicSuccess(res, file, fileList) {
      if (file && file.length == 2) {
        file.shift()
      }
      this.uploadImg(res, 1);

    },
    handleSmallPicSuccess1(res, file, fileList) {

      this.uploadImg(res, 2);

    },
    handleSmallPicSuccessgg(res, file, fileList) {
      this.xz_lingth = file.length


      // 处理上传成功逻辑
      this.uploadImg(res, 6);
      // ...

      // 标记已经上传过文件



    },
    handleSmallPicSuccess1(res, file, fileList) {

      this.uploadImg(res, 2);

    }, handleSmallPicSuccess2(res, file, fileList) {
      if (file && file.length == 2) {
        file.shift()
      }
      this.uploadImg(res, 3);

    }, handleSmallPicSuccess3(res, file, fileList) {

      this.uploadImg(res, 4);

    },
    handleSmallPicSuccess4(res, file, fileList) {

      this.uploadImg(res, 5);

    },
    uploadImg(file, type) {
      let that = this;
      let formData = new FormData()
      formData.append('file', file.raw)
      that.loading = Loading.service({
        lock: true,
        text: '上传中...',
        // background: 'rgba(0, 0, 0, 0.7)',
        target: document.querySelector('#loadingDiv')
      })
      upload(formData).then((res) => {
        if (res.data.code == 0) {
          if (type == 1) {
            that.loading.close();

            that.form.zhuye = res.data.yunUrl;
          } else if (type == 2) {
            that.loading.close();
            let obj = {
              url: res.data.yunUrl,
              name: res.data.fileName
            }
            that.form.photo.push(obj);

          } else if (type == 3) {
            that.loading.close();

            that.form.qrImage = res.data.yunUrl;
          } else if (type == 4) {
            that.loading.close();

            let obj = {
              url: res.data.yunUrl
            }

            that.form.productImages.push(obj);
          } else if (type == 5) {
            that.loading.close();

            that.form.longpic = res.data.yunUrl;;
          } else if (type == 6) {

            let aa = {
              url: res.data.yunUrl
            }
            this.tableData[this.IMGindex].kongjianimages.push(aa)
            if (this.xz_lingth == this.tableData[this.IMGindex].kongjianimages.length) {
              that.loading.close();

            }
          }


        }

      }).catch((err) => {
        console.log(err);
      })


    },
    tableRowClassName({ row, rowIndex }) {
      if (rowIndex === 1) {
        return 'warning-row';
      } else if (rowIndex === 3) {
        return 'success-row';
      }
      return '';
    },
    handleRemove1(file) {

    },
    handleRemove8() { },
    handleRemove2(file) {
      // 1.获取将要删除图片的临时路径
      const filePath = file.url;
      // 2.从pics数组中，找到图片对应的索引值
      const i = this.form.productUrl.findIndex((x) => x.url === filePath);
      // 3.调用splice方法，移除图片信息
      this.form.productUrl.splice(i, 1);
    },
    handleRemove3() { },
    handleRemove4(file) {
      // 1.获取将要删除图片的临时路径
      const filePath = file.url;
      // 2.从pics数组中，找到图片对应的索引值
      const i = this.form.productImages.findIndex((x) => x.url === filePath);
      // 3.调用splice方法，移除图片信息
      this.form.productImages.splice(i, 1);

    },
    handleRemove5() {
      this.form.longpic = '';
    },
    closeDialog() {
      this.form = {};
      this.activeName = '1';
      this.bookUrllist = []
      this.bookUrllist1 = []
      this.bookUrllist2 = []
      this.bookUrllist3 = []
    },
    // 查询案例分类
    getproductTypeList() {
      var params = new URLSearchParams();
            params.append('dbName', window.localStorage.getItem('JJHTDBnmame'));



      this.$store.dispatch('CP/getqueryCaseTypes', params).then((res) => {

        if (res.code >= 0) {

          this.classification = res.data[0].children;
          this.classification1 = res.data[0].children[0].children;
          res.data[0].children.forEach((item) => {
            if (item.name == '规格') {
              this.GGlist = item.children
            }
            if (item.name == '系列') {
              this.XLList = item.children
            }
          })
        } else {
          this.classification = []
          this.classification1 = []
        }

      }).catch(() => {
        this.loading = false
      })
    },

    // 查询案例分类
    getqueryCaseType() {
      var params = new URLSearchParams();
            params.append('dbName', window.localStorage.getItem('JJHTDBnmame'));

      params.append('curPage', this.curPage);


      this.$store.dispatch('CP/getcaseList', params).then((res) => {
        this.total = res.totalNum;
        if (res.code == '1') {
          this.data = res.list
        } else {
          this.data = []
        }

      }).catch(() => {
        this.loading = false
      })
    },
    handleCurrentChange1(val) {
      this.curPage = val;
      this.getqueryCaseType()
    },
    handleSelectionChange() { },


  },
  created() {
    const SEDJJXCX_LHFW_UserInformation_HT = JSON.parse(decodeURIComponent(window.localStorage.getItem('SEDJJXCX_LHFW_UserInformation_HT')))
    this.SEDJJXCX_LHFW_UserInformation_HT = SEDJJXCX_LHFW_UserInformation_HT;

    this.getqueryCaseType();
    this.getproductTypeList();
    this.generateData()


  },
  mounted() {

  }
}
</script>

<style lang="scss">
.el-table thead {
  color: #000000 !important;
}

.el-tree-node__label {
  font-weight: 900 !important;
}

.el-tree-node__content {
  font-weight: 900 !important;

}

.dashboard {
  &-container {
    margin: 30px;
  }

  &-text {
    font-size: 30px;
    line-height: 46px;
  }
}

.item {
  margin-bottom: 18px;
}



.pagdw {
  float: right;
  margin-top: 46px;
}

.el-input_ss1 {
  margin-top: 4px;
}
</style>
