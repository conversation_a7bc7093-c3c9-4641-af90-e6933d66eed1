import request from '@/utils/request'

export function IntergralOrder(params) {
  return request({
    url: 'oe_intergralOrder_.csp',
    method: 'post',
    params
  })
}
export function AddRemark(params) {
  return request({
    url: 'oe_addRemark_.csp',
    method: 'post',
    params
  })
}
export function OrderSend(params) {
  return request({
    url: 'oe_orderSend_.csp',
    method: 'post',
    params
  })
}


export function OrderComment(params) {
  return request({
    url: 'oe_orderComment_.csp',
    method: 'get',
    params
  })
}
export function DeleteComments(params) {
  return request({
    url: 'oe_deleteComments_.csp',
    method: 'post',
    params
  })
}
export function HideComments(params) {
  return request({
    url: 'oe_hideComments_.csp',
    method: 'post',
    params
  })
}
export function ReturnComment(params) {
  return request({
    url: 'oe_returnComment_.csp',
    method: 'post',
    params
  })
}
export function AddComment(params) {
  return request({
    url: 'oe_addComment_.csp',
    method: 'post',
    params
  })
}
export function GoodsSearch(params) {
  return request({
    url: 'oe_goodsSearch_.csp',
    method: 'get',
    params
  })
}

export function UpdateOrderAddress(params) {
  return request({
    url: 'oe_updateOrderAddress_.csp',
    method: 'post',
    params
  })
}

export function exportOrders(params) {
  return request({
    url: 'oe_exportOrders_.csp',
    method: 'get',
    params
  })
}

export function cancelOrder(params) {
  return request({
    url: 'oe_cancelOrder_.csp',
    method: 'get',
    params
  })
}
export function goodsDetail(params) {
  return request({
    url: 'oe_goodsDetail_.csp',
    method: 'get',
    params
  })
}