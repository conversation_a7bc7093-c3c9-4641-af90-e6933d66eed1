<template>
  <div class="app-container">
    <!-- 搜索筛选 -->
    <el-form :inline="true" class="user-search">
     
     
  
      <el-form-item>
        <el-input size="mini" placeholder="请输入内容" v-model="input4" class="el-input_ss1">
          <el-button size="mini" slot="append" icon="el-icon-search" @click="change3"></el-button>
        </el-input>
      </el-form-item>
      <el-form-item>
        <!-- <el-button size="mini" type="primary" icon="el-icon-plus" @click="increase()">添加</el-button> -->

        <el-button size="mini" type="primary" @click="Reload" icon="el-icon-refresh-left">重新加载</el-button>
        <!-- <el-button @click="leading">批量导出</el-button> -->

        <el-button size="mini" type="success" @click="addadopt" icon="el-icon-check">通过</el-button>
        <el-button size="mini" type="danger" @click="addrefuse" icon="el-icon-close">拒绝</el-button>




      </el-form-item>
    </el-form>

    <el-table :data="data" border fit highlight-current-row @selection-change="handleSelectionChange">
      <el-table-column align="center" type="selection" width="55">
      </el-table-column>
      <el-table-column align="center" label="序号" width="95">
        <template slot-scope="scope">{{scope.$index + 1}}</template>
      </el-table-column>
               
      <el-table-column align="center" prop="name" label="头像" width="90">
              <template slot-scope="scope">
        <el-popover placement="bottom" trigger="hover" width="100">
          <img :src="scope.row.photo" width="100%" />
          <img slot="reference" :src="scope.row.photo" :alt="scope.row.photo"
            style="max-height: 40px;max-width: 40px;" />
        </el-popover>
      </template>

          </el-table-column>
      <el-table-column align="center" prop="img" label="微信昵称" >
        <template slot-scope="scope">
          {{ scope.row.wxName ? scope.row.wxName : '暂无' }}
        </template>
      </el-table-column>
      <el-table-column align="center" prop="img" label="姓名" width="200">
        <template slot-scope="scope">
          {{ scope.row.name }}
        </template>
      </el-table-column>
      <el-table-column align="center"  label="所属店铺" width="300">
        <template slot-scope="scope">{{ scope.row.storeName }}</template>
      </el-table-column>
     
      <el-table-column align="center"  label="联系方式" width="120">
        <template slot-scope="scope">{{ scope.row.phone }}</template>
      </el-table-column>
      <el-table-column align="center"  label="职位" width="200">
        <template slot-scope="scope">
          <div v-if="scope.row.role == '0'">业主</div>
          <div v-if="scope.row.role == '1'"> 经销商 </div>
          <div v-if="scope.row.role == '2'">店长</div>
          <div v-if="scope.row.role == '3'">导购</div>
          <div v-if="scope.row.role == '4'">总部监理</div>
          <div v-if="scope.row.role == '5'">交付管家</div>
          <div v-if="scope.row.role == '6'">交付工程师</div>
          <div v-if="scope.row.role == '7'">门店设计师 </div>
          <div v-if="scope.row.role == '8'">分销商</div>
          <div v-if="scope.row.role == '9'">外部设计师</div>
        
        </template>
      </el-table-column>
      <el-table-column align="center"  label="审核状态" width="100">
        <template slot-scope="scope">
          <el-button size="mini" type="warning"  v-if="scope.row.status == '0'"> 待审核</el-button>
          <el-button  size="mini" type="success"  v-if="scope.row.status == '1'">已通过</el-button>
          <el-button size="mini" type="danger"  v-if="scope.row.status == '2'">已拒绝</el-button>
          
        </template>
      </el-table-column>
      <el-table-column align="center"  label="服务量" >
        <template slot-scope="scope">{{ scope.row.serviceCount ? scope.row.serviceCount :'0' }}个</template>
      </el-table-column>

      <el-table-column align="center"  label="从业年限" >
        <template slot-scope="scope">{{ scope.row.workTime ? scope.row.workTime :'0' }}年</template>
      </el-table-column>

    
    
      
      
      <!-- fixed="right"  width="200" -->

      <el-table-column   fixed="right"  label="操作" align="center"  width="200">
        <template slot-scope="scope">
          <el-button size="mini" type="primary" @click="handleEditzt(scope.row)">修改</el-button>
          <el-button size="mini" type="danger" @click="handleDelete(scope.row.id)">删除</el-button>

        </template>
      </el-table-column>
    </el-table>
    <el-row>
      <el-pagination :current-page.sync="currentPage" background style="margin-top:30px" :page-size="pageSize"
        layout="total,prev,pager,next,jumper" :total="total" @current-change="handleCurrentChange1" />

    </el-row>
     <!-- 编辑界面 -->
     <el-dialog :title="title" :visible.sync="editFormVisible" @close="closeDialog" width="50%">
      <el-form label-position="rigth" label-width="100px">
     
        
        <el-form-item label="姓名:">
          <el-input style="width: 100%;" size="mini" v-model="special.name" auto-complete="off"
            placeholder="请输入姓名"></el-input>
        </el-form-item>
        <el-form-item label="头像:">
          <el-upload action="#" list-type="picture-card" :on-preview="handlePreview" :on-remove="handleRemove1"
            :auto-upload="false" :file-list="bookUrllist" multiple :on-change="handleSmallPicSuccess">
            <i class="el-icon-plus" />
          </el-upload>
          <el-dialog :visible.sync="dialogVisibleImg" top="0" center :modal="false">
            <img width="100%" :src="special.photo" alt>
          </el-dialog>
        </el-form-item>





        <el-form-item label="联系方式:">
          <el-input style="width: 100%;" size="mini" v-model="special.phone" auto-complete="off"
            placeholder="请输入联系方式"></el-input>
        </el-form-item>
       
        
        <el-form-item label="从业年限:">
          <el-input style="width: 100%;" size="mini" v-model="special.workTime" auto-complete="off"
            placeholder="请输入从业年限"></el-input>
        </el-form-item>
     
    
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="closeDialog">取消</el-button>
        <el-button size="mini" type="primary" class="title" @click="submitForm()">保存</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>

import {  supervisorList,editBrickInfo ,delBrick,checkApplys} from '@/api/RY'
import { upload } from '@/api/upload'
import { formatDate_RQ, Url } from '@/utils/time'
import { Loading } from 'element-ui';
// import VueEditor from 'vue-word-editor'
import 'quill/dist/quill.snow.css'
import { log } from 'console';
export default {
  components: {
    // VueEditor,

  },
  data() {
    return {
      title: '修改',

      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }],
      },
      dialogTitle: '新增文件',
      ewmShowgg: false,
      specialShow: false,
      specialShowgg: false,
      formList: {},
      data: [],
      pageSize: 10,
      value1: "",
      value2: "",
      input2: '',
      input3: '',
      input4: '',
      currentPage: 1,
      handleSelectionChangeList: [],
      bookUrllist: [],
      dialogVisibleImg: false,
      formData: {
        startTime: '',
        endTime: ""
      },
      Product: {
        typeId: '',
        name: '',
        linkUrl: '',
        fileType: '',
        fileName: ''
      },
      total: 1,
      valnum: 1,
      SEDJJXCX_LHFW_UserInformation_HT: {},
      baseUrl: process.env.VUE_APP_BASE_API + "/uploadCloud?filename=11111",
      baseUrl1: process.env.VUE_APP_BASE_API + "/oe_createDatum_.csp",
      //上传后的文件列表
      fileList: [],
      // 允许的文件类型
      fileType: ["pdf", "doc", "docx", "xls", "xlsx", "txt", "png", "jpg", "bmp", "jpeg"],
      // 运行上传文件大小，单位 M
      fileSize: 50,
      // 附件数量限制
      fileLimit: 1,
      //请求头
      headers: { "Content-Type": "multipart/form-data" },
      classify:[
        {
          id:0,name:"业主"
        },{
          id:1,name:"经销商"
        },{
          id:2,name:"店长 "
        },{
          id:3,name:"导购"
        },{
          id:4,name:"总部监理"
        },{
          id:5,name:"交付管家"
        },{
          id:6,name:"交付工程师"
        },{
          id:7,name:"门店设计师 "
        },{
          id:8,name:"分销商"
        },{
          id:9,name:"外部设计师"
        },
      ],
      special:{
        photo:'',
        wxName:'',
        name:'',
        phone:'',
        serviceCount:'',
        workTime:'',
        evaluate:'',
      },
      bookUrllist:[],
      dialogVisibleImg:false,
      editFormVisible:false
    }
  },
  mounted() {
  },
  created() {
    const SEDJJXCX_LHFW_UserInformation_HT = JSON.parse(decodeURIComponent(window.localStorage.getItem('SEDJJXCX_LHFW_UserInformation_HT')))
    this.SEDJJXCX_LHFW_UserInformation_HT = SEDJJXCX_LHFW_UserInformation_HT
    this.getGoods();
    // this.fetchData();
  },
  methods: {
    addadopt() {
      if (this.handleSelectionChangeList && this.handleSelectionChangeList.length) {
        const list = [];
        for (var i of this.handleSelectionChangeList) {
          list.push(i.id)
          if (i.status == 1 || i.status == 2) {
            this.$message({
              type: 'warning',
              message: i.name + '已审核过!'

            })
            return
          }
        }
        var a = list.join(",");
        const params = {
          status: 1,
          listid: a,

          dbName: window.localStorage.getItem('JJHTDBnmame')
        };
        checkApplys(params).then(response => {
          if (response.data.code == '1') {
            this.$message({
              type: 'success',
              message: '已通过'
            })
    this.getGoods();

          } else {
            this.$message({
              type: 'warning',
              message: '通过失败！'
            })
          }
        })

      } else {
        this.$message({
          type: 'warning',
          message: '请选择你通过的用户'
        })
      }
      // },
    },
    addrefuse() {
      if (this.handleSelectionChangeList && this.handleSelectionChangeList.length) {
        const list = [];

        for (var i of this.handleSelectionChangeList) {
          list.push(i.id)
          if (i.status == 1 || i.status == 2) {
            this.$message({
              type: 'warning',
              message: i.name + '已审核过!'

            })
            return
          }
        }
        var a = list.join(",");
        const params = {
          status: 2,
          listid: a,

          dbName: window.localStorage.getItem('JJHTDBnmame')
        };
        checkApplys(params).then(response => {
          if (response.data.code == '1') {
            this.$message({
              type: 'success',
              message: '已拒绝'
            })
    this.getGoods();

          } else {
            this.$message({
              type: 'warning',
              message: '拒绝失败！'
            })
          }
        })
      } else {
        this.$message({
          type: 'warning',
          message: '请选择你拒绝的用户'
        })
      }

    },
     // 编辑、增加页面保存方法
     submitForm() {
  


      const params = {
        id: this.special.id,
        name: this.special.name,
        photo: this.special.photo,
        phone: this.special.phone,
        serviceCount: this.special.serviceCount,
        workTime: this.special.workTime,
        evaluate: this.special.evaluate,
     
        dbName: window.localStorage.getItem('JJHTDBnmame')
      };
      editBrickInfo(params).then(response => {
        console.log(response);
        if (response.data.code == '1') {
          this.editFormVisible = false
          this.bookUrllist = []
          this.getGoods()
          this.$message({
            type: 'success',
            message: this.title == '修改' ? '修改成功！' : '创建成功！'
          })
        } else {
          this.$message({
            type: 'warning',
            message: '创建失败！'
          })
        }
      })


    },
    handleSmallPicSuccess(res, file, fileList) {
      if (file && file.length == 2) {
        file.shift()
      }
      this.uploadImg(res, 1);

    },
  
     uploadImg(file, type) {
      this.loading = Loading.service({
        lock: true,
        text: '图片上传中........',
        // background: 'rgba(0, 0, 0, 0.7)',
        target: document.querySelector('#loadingDiv')
      })
      let formData = new FormData()
      formData.append('file', file.raw)
      upload(formData).then(response => {
        setTimeout(() => {
          this.loading.close()

        }, 2000);
        if (type == 1) {
          this.special.photo = response.data.yunUrl;

        } else {
          this.special.detail = response.data.yunUrl;

        }

      }).catch((err) => {
        console.log(err);

      });


    },
      // 关闭编辑、增加弹出框
      closeDialog() {

this.editFormVisible = false;
this.deptName = '';
this.special = {};
},
    change2(){
      const params = {
        role: this.input3,
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: 1
      }
  

     
      if (this.input4) {
        params.keyword = this.input4;
      }

      supervisorList(params).then(response => {
        this.total = response.data.totalNum;
        this.currentPage = 1;
        if (response.data.code === '1') {
          this.data = response.data.list
          // this.value2 = '';
          this.input4 = ''
        } else {
          this.data = response.data.list

          // this.value2 = '';
          this.input4 = ''
        }
      })
    },
    //上传文件之前
    beforeUpload(file) {
      this.Product.fileName = file.name;
      if (file.type != "" || file.type != null || file.type != undefined) {
        //截取文件的后缀，判断文件类型
        const FileExt = file.name.replace(/.+\./, "").toLowerCase();
        //计算文件的大小
        const isLt5M = file.size / 1024 / 1024 < 500; //这里做文件大小限制
        //如果大于50M
        if (!isLt5M) {
          this.$message.error('上传文件大小不能超过 500MB!')
          return false;
        }
        //如果文件类型不在允许上传的范围内
        if (this.fileType.includes(FileExt)) {
          return true;
        }
        else {
          this.$message.error("上传文件格式不正确!");
          return false;
        }
      }
    },
    //上传了的文件给移除的事件，由于我没有用到默认的展示，所以没有用到
    handleRemove() {
    },
    //这是我自定义的移除事件
    handleClose(i) {
      this.fileList.splice(i, 1);//删除上传的文件
      if (this.fileList.length == 0) {//如果删完了
        this.fileflag = true;//显示url必填的标识
        this.$set(this.rules.url, 0, { required: true, validator: this.validatorUrl, trigger: 'blur' })//然后动态的添加本地方法的校验规则
      }
    },
    //超出文件个数的回调
    handleExceed() {
      this.$message({
        type: 'warning',
        message: '超出最大上传文件数量的限制！'
      }); return
    },
    //上传文件的事件
    uploadFile(item) {
      this.loading = Loading.service({
        lock: true,
        text: '文件上传中........',
        // background: 'rgba(0, 0, 0, 0.7)',
        target: document.querySelector('#loadingDiv')
      })
      //上传文件的需要formdata类型;所以要转
      let FormDatas = new FormData()
      FormDatas.append('file', item.file);
      upload(FormDatas).then(res => {
        if (res.data.code == '0') {
          setTimeout(() => {
            this.loading.close()

          }, 2000);
          this.Product.linkUrl = res.data.yunUrl;
          this.Product.fileType = res.data.ext;
          // this.Product.filename=res.data.fileNameOld;
        }
      })

      // 	})
    },
    //上传成功后的回调
    handleSuccess() {

    },
    wangEditorChange(val) {
      this.Product.detail = val;
    },
    // 关闭
    formCancal1(){
      // this.Product.typeId='';
      // this.Product.name='';
      // this.Product.linkUrl='';
      // this.Product.fileType='';
      this.Product={}
      // this.Product.filename='';
    },
// 添加
    increase(){
      this.specialShow=true;
    },
    // 重新加载
    Reload() {
      this.value1 = '';
      this.value2 = '';
      this.input2 = '';
      this.input3 = '';
      this.input4 = '';
      this.input5 = '';
      this.getGoods(1);

    },
    fetchData(val = 1) {
      // const params = {
      //                       dbName: window.localStorage.getItem('JJHTDBnmame'),

      //   curPage: val
      // }
      // allDatumType(params).then(response => {
      //   if (response.data.code == '1') {
      //     this.classify = response.data.records
      //   }
      // })
    },

    getGoods(val = 1) {
      const params = {
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: val,
        role:8
      }
      if (this.value2 && this.value2[0] && this.value2[1]) {
        params.starTime = formatDate_RQ(this.value2[0]);
        params.endTime = formatDate_RQ(this.value2[1]);
      }

      if (this.input3) {
        params.catId = this.input3;
      }
      if (this.input4) {
        params.name = this.input4;
      }
      if (this.input2) {
        params.status = this.input2;
      }

      supervisorList(params).then(response => {
        this.total = response.data.totalNum;
        if (response.data.code == '1') {
          this.data = response.data.list
        } else {
          this.data = response.data.list

        }
      })
    },
    handleCurrentChange1(val) {
      this.valnum = val;
      this.getGoods(val)

    },
    handleCurrentChange(val) {
    },
    // 删除文件
    handleDelete(row) {
      this.$confirm('此操作将永久删除金牌工程师', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          const params = {
                                dbName: window.localStorage.getItem('JJHTDBnmame'),

            id: row
          }
          delBrick(params).then(response => {
            if (response.data.code == '1') {
              this.$message.success('删除成功')

              this.getGoods()
            }
          })
        })
        .catch((err) => {
          console.log(err)
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    handleSelectionChange(val) {
      this.handleSelectionChangeList = val;
    },

    handlePreview(file) {
      this.Product.coverPic = file.url
      this.dialogVisibleImg = true
    },
    handlePreview1(file) {
      this.specifications.url = file.url
      this.dialogVisibleImg = true
    },
    handleRemove1(file, fileList) {
      // 移除

    },




    //添加文件
    RechargeRole() {

      if (this.Product.catld == '') {
        this.$message({
          showClose: true,
          message: '请选择分类！',
          type: 'warning'
        });
        return
      }
      if (this.Product.name == '') {
        this.$message({
          showClose: true,
          message: '请输入文件名称！',
          type: 'warning'
        });
        return
      }

      if (this.Product.linkUrl == '') {
        this.$message({
          showClose: true,
          message: '请上传对应文件！',
          type: 'warning'
        });
        return
      }
      console.log(this.Product);
      var params = new URLSearchParams();
      params.append('name', this.Product.name);
      params.append('linkUrl', this.Product.linkUrl);
            params.append('dbName', window.localStorage.getItem('JJHTDBnmame'));

      params.append('typeId', this.Product.typeId);
      params.append('fileType', this.Product.fileType);
      params.append('fileName', this.Product.fileName);
      params.append('id', this.Product.id ? this.Product.id : '');
      this.axios.post(this.baseUrl1, params,).then(res => {
        if (res.data.code == '1') {
          this.$message({
            type: 'success',
            message: this.dialogTitle == '修改文件' ? '修改成功' : '添加成功'

          })
          this.getGoods(this.valnum)
          this.specialShowgg = false
          this.specialShow = false;
          this.Product = {}
          this.bookUrllist = []
          this.tableOption = [];
          this.input5 = '';
        } else {
          this.$message({
            type: 'warning',
            message: this.dialogTitle == '修改文件' ? '修改失败' : '添加失败'

          })
        }
      }).catch(err => {
      })
      return

    },
    // 修改文件
    handleEditzt(row) {
      this.dialogTitle = '修改';
      this.editFormVisible = true;
      this.special = JSON.parse(JSON.stringify(row));
      if (row.photo) {
        this.bookUrllist = [
          {
            url: row.photo
          }
        ]
      }
    },

    // 时间筛选
    change() {
      this.formData.startTime = formatDate_RQ(this.value2[0])
      this.formData.endTime = formatDate_RQ(this.value2[1])
      const params = {
        dbName: "mati", curPage: 1,
        starTime: this.formData.startTime,
        endTime: this.formData.endTime,
      }
      if (this.input2) {
        params.status = this.input2;
      }
      if (this.input3) {
        params.catId = this.input3;
      }
      if (this.input4) {
        params.name = this.input4;
      }
      supervisorList(params).then(response => {
        this.total = response.data.totalNum;
        this.currentPage = 1;
        if (response.data.code === '1') {
          this.formData.startTime = ''
          this.formData.endTime = ''
          this.data = response.data.records
          // this.value2 = '';
        } else {
          this.data = response.data.records

          // this.value2 = '';
        }
      })
    },
   
    // 名字筛选
    change3() {
      const params = {
        keyword: this.input4,
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: 1,
        role:8
      }
      if (this.value2 && this.value2[0] && this.value2[1]) {
        params.starTime = formatDate_RQ(this.value2[0]);
        params.endTime = formatDate_RQ(this.value2[1]);
      }

      if (this.input2) {
        params.status = this.input2;
      }
      if (this.input3) {
        params.catId = this.input3;
      }

      supervisorList(params).then(response => {
        this.total = response.data.totalNum;
        this.currentPage = 1;
        if (response.data.code === '1') {
          this.data = response.data.list
          // this.value2 = '';
        } else {
          this.data = response.data.list

          // this.value2 = '';
          // this.input4 = ''
        }
      })
    },
    // 批量导出
    // leading(){
    //   if (this.handleSelectionChangeList && this.handleSelectionChangeList.length) {
    //     let url = 'https://cdn.juesedao.cn/huiya/682fe11b0a2648d19f07e9d8b78279c2'
    //     const a = document.createElement('a')
    //     a.href = url
    //     a.download = '测试'// 下载后文件名
    //     a.style.display = 'none'
    //     document.body.appendChild(a)
    //     a.click() // 点击下载
    //     document.body.removeChild(a) // 下载完成移除元素

    //   } else {
    //     this.$message({
    //       type: 'warning',
    //       message: '请选择你导出的选项'
    //     })
    //   }
    // },
   


    handleCheck(index, row) {
      this.IMGindex = index;
    },

    change5() {
      if (this.input5) {
        this.tableOption.push({
          'label': this.input5
        })

        // this.tableData[0].attr_list = this.tableOption
        // return
        const params = {
          dbName: 'mati',
          attrGroup: this.input5
        }
        QueryAttrGroup(params).then(response => {
          if (response.data.code == '1') {
            this.attrGroup_ID.push(response.data.record.id);
            for (let i in this.tableData) {
              this.tableData[i].attr_list.push({
                attr_group_id: response.data.record.id,//表头id
                attr_group_name: this.input5,//表头
                attr_id: "",
                attr_name: ''
              })
            }

          }
        })
      } else {
        this.$message({
          type: 'warning',
          message: '请填写规格'
        })
      }

    },
    // change6(index, row) {
    //   const params = {
    //     dbName: 'mati',
    //     attrGroup: 5,
    //     attrName: row.specifications
    //   }
    //   QueryAttr(params).then(response => {
    //     if (response.data.code == '1') {
    //       this.tableData[index].attr_list.push({
    //         attr_id: response.data.record.id,
    //         attr_name: row.specifications,
    //       })
    //     }
    //   })
    // },
    change6(index, i) {
      console.log(this.attrGroup_ID);
      const params = {
        dbName: 'mati',
        attrGroup: this.attrGroup_ID[i], // this.attrGroupID,
        // attrGroup: this.attrGroupID, // this.attrGroupID,
        attrName: this.tableData[index].attr_list[i].attr_name
      }
      QueryAttr(params).then(response => {
        if (response.data.code == '1') {
          this.tableData[index].attr_list[i].attr_id = response.data.record.id;
        }
      })
    },
    
   




  },
};
</script>

<style lang="scss" scoped>
.el-row {
  margin-bottom: 20px;
}

.el-row:last-child {
  margin-bottom: 0;
}

.el-tag+.el-tag {
  margin-left: 10px;
}



.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}

.input-new-tag {
  width: 300px;
  margin-left: 10px;
  vertical-align: bottom;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.el-button--medium {
  margin-top: 10px;
  margin-left: 600px;
  height: 45px;
  width: 100px;
}

.el-input_ss {
  width: 200px;
}

.el-input_ss1 {
 
  margin-top: 6px;
}

.btn_jia {
  position: relative;
  margin-left: 8px;
  margin-top: 20px;
}

.btn_jia:hover {
  font-weight: 600;
  text-decoration: underline;
}

.btn_jia::after {
  content: "";
  width: 2px;
  height: 20px;
  background: #000;
  position: absolute;
  top: 10px;
  right: 33px;
}

.status_shop {
  margin-right: 8px;
}

// 分页
.paging {
  width: 100%;
  height: 100%;
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

// 功能按钮
.function_btn_box {
  width: 100%;
  display: flex;
  margin-right: 10px;
  margin-bottom: 20px;
}

.input-with-select {
  margin-left: 50px;
}

.float_rigth {
  float: right;
}

// .el-input.inp {
//   width: auto !important;
// }
</style>
