<template>
  <div class="app-container">
    <!-- 搜索筛选 -->
    <el-form :inline="true" class="user-search">


      <el-form-item prop="Product.catld">
        <el-select size="mini" v-model="input3" placeholder="项目状态" @change="change2">
          <el-option size="mini" v-for="item in classify" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="Product.catld">
        <el-select size="mini" v-model="input6" placeholder="施工进度" @change="change7">
          <el-option size="mini" v-for="item in classify1" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input size="mini" placeholder="请输入内容" v-model="input4" class="el-input_ss1">
          <el-button size="mini" slot="append" icon="el-icon-search" @click="change3"></el-button>
        </el-input>
      </el-form-item>
      <el-form-item>
        <!-- <el-button size="mini" type="primary" icon="el-icon-plus" @click="increase()">添加</el-button> -->

        <el-button size="mini" type="primary" @click="Reload" icon="el-icon-refresh-left">重新加载</el-button>
        <!-- <el-button @click="leading">批量导出</el-button> -->
        <el-button @click="grounding" size="mini" type="warning" icon="el-icon-upload2">批量上架</el-button>
        <el-button @click="Offshelf" size="mini" type="warning" icon="el-icon-download">批量下架</el-button>
        <!-- <el-button @click="Confirmacceptance" size="mini" type="success" icon="el-icon-thumb">确定验收</el-button> -->




      </el-form-item>
    </el-form>

    <el-table :data="data" border fit highlight-current-row @selection-change="handleSelectionChange1">
      <el-table-column align="center" type="selection" width="55">
      </el-table-column>

      <el-table-column align="center" label="序号" width="80">
        <template slot-scope="scope">{{ scope.$index + 1 }}</template>
      </el-table-column>
      <el-table-column align="center" prop="img" label="订单号" width="200">
        <template slot-scope="scope">
          {{ scope.row.orderNum }}
        </template>
      </el-table-column>
      <el-table-column align="center" width="200" label="所属门店">
        <template slot-scope="scope">{{ scope.row.storeName }}</template>
      </el-table-column>
      <el-table-column align="center" prop="img" label="业主" width="200">
        <template slot-scope="scope">
          {{ scope.row.name }}
        </template>
      </el-table-column>
      <el-table-column align="center" prop="img" label="工地名称" width="200">
        <template slot-scope="scope">
          {{ scope.row.community }}
        </template>
      </el-table-column>
      <el-table-column align="center" width="100" label="项目状态">
        <template slot-scope="scope">
          <el-button plain v-if="scope.row.delFlag == '0'" type="primary" size="mini">上架</el-button>
          <el-button plain v-if="scope.row.delFlag == '1'" type="warning" size="mini">下架</el-button>

        </template>
      </el-table-column>
      <el-table-column align="center" width="100" label="施工状态">
        <template slot-scope="scope">
          <el-button plain v-if="scope.row.orderStatus == '1'" type="primary" size="mini">未开始</el-button>
          <el-button plain v-if="scope.row.orderStatus == '2'" type="warning" size="mini">进行中</el-button>
          <el-button plain v-if="scope.row.orderStatus == '3'" type="success" size="mini">已完工</el-button>

        </template>
      </el-table-column>

      <el-table-column align="center" width="150" label="施工进度">
        <template slot-scope="scope">
          <el-button plain v-if="scope.row.schedule == '1'" size="mini">量好尺寸</el-button>
          <el-button plain v-if="scope.row.schedule == '2'" size="mini">出好设计 </el-button>
          <el-button plain v-if="scope.row.schedule == '3'" size="mini">跟好现场</el-button>
          <el-button plain v-if="scope.row.schedule == '4'" size="mini">做好美缝</el-button>
          <el-button plain v-if="scope.row.schedule == '5'" size="mini">搞好清洁</el-button>
          <el-button plain v-if="scope.row.schedule == '6'" size="mini">拍好美片</el-button>
          <el-button plain v-if="scope.row.schedule == '7'" size="mini">完成</el-button>

        </template>
      </el-table-column>
      <el-table-column align="center" width="200" label="联系电话">
        <template slot-scope="scope">{{ scope.row.phone }}</template>
      </el-table-column>
      <el-table-column align="center" width="500" label="地址">
        <template slot-scope="scope">{{ scope.row.address }}</template>
      </el-table-column>

      <el-table-column prop="workTime" align="center" width="200" label="施工时间" :formatter="formatDate">
      </el-table-column>

      <el-table-column prop="predictTime" align="center" width="200" label="预计完成时间" :formatter="formatDate">
      </el-table-column>
      <el-table-column prop="endTime" align="center" width="200" label="实际完成时间" :formatter="formatDate">
      </el-table-column>
      <!-- <el-table-column align="center" label="总部监理">
        <template slot-scope="scope">{{ scope.row.regulatorName }}</template>
      </el-table-column>
      <el-table-column align="center" label="设计师">
        <template slot-scope="scope">{{ scope.row.stylistName }}</template>
      </el-table-column>
      <el-table-column align="center" label="交付工程师" width="100">
        <template slot-scope="scope">{{ scope.row.bricklayerName }}</template>
      </el-table-column>
      <el-table-column align="center" label="交付管家">
        <template slot-scope="scope">{{ scope.row.stewardName }}</template>
      </el-table-column> -->
      <!-- fixed="right"  width="200" -->

      <el-table-column fixed="right" label="操作" align="center" width="250">
        <template slot-scope="scope">
          <el-tooltip class="item" effect="dark" content="查看详情" :hide-after="2000" placement="top">
            <el-button type="primary" icon="el-icon-tickets" circle @click="Detail(scope.row)" size="small">
            </el-button>
          </el-tooltip>
          <el-tooltip class="item" effect="dark" content="打卡记录" :hide-after="2000" placement="top">
            <el-button type="success" icon="el-icon-s-claim" circle @click="OpenRecord(scope.row)" size="small">
            </el-button>
          </el-tooltip>
          <el-tooltip class="item" effect="dark" content="团队" :hide-after="2000" placement="top">
            <el-button type="info" icon="el-icon-user" circle @click="team(scope.row)" size="small">
            </el-button>
          </el-tooltip>
          <!-- v-if="scope.row.orderStatus == '1'" -->
          <!-- <el-tooltip  v-if="scope.row.schedule == '2'" class="item" effect="dark" content="审核" :hide-after="2000" placement="top">
            <el-button v-if="scope.row.schedule == '2'" type="danger" icon="el-icon-edit-outline" circle @click="toexamine(scope.row)" size="small">
            </el-button>
          </el-tooltip> -->
          <el-tooltip class="item" effect="dark" content="产品" :hide-after="2000" placement="top">
            <el-button type="warning" icon="el-icon-price-tag" circle @click="product(scope.row.id)" size="small">
            </el-button>
          </el-tooltip>
          <!-- <el-button size="mini" type="primary" @click="Detail(scope.row)">查看详情</el-button>
          <el-button size="mini" type="info" @click="OpenRecord(scope.row)">打卡记录</el-button>
          <el-button size="mini" type="success" @click="team(scope.row)">团队</el-button>
          <el-button size="mini" type="warning" v-if="scope.row.orderStatus == '1'"
            @click="toexamine(scope.row)">审核</el-button>
          <el-button size="mini" type="danger" @click="product(scope.row.id)">产品</el-button> -->

        </template>
      </el-table-column>
    </el-table>
    <el-row>
      <el-pagination :current-page.sync="currentPage" background style="margin-top:30px" :page-size="pageSize"
        layout="total,prev,pager,next,jumper" :total="total" @current-change="handleCurrentChange1" />

    </el-row>
    <!-- 产品 -->
    <el-dialog @close="formCancal1" width="70%" title="所用产品" :visible.sync="specialShow" :close-on-click-modal="false"
      :close-on-press-escape="false">
      <el-table :data="productlist" border fit>

        <el-table-column align="center" label="序号" width="95">
          <template slot-scope="scope">{{ scope.$index + 1 }}</template>
        </el-table-column>
        <el-table-column align="center" prop="img" label="产品名称">
          <template slot-scope="scope">
            {{ scope.row.productName }}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="name" label="产品图" width="150">
          <template slot-scope="scope">
            <el-popover placement="bottom" trigger="hover" width="1000">
              <img :src="scope.row.productImage" width="100%" />
              <img slot="reference" :src="scope.row.productImage" :alt="scope.row.productImage"
                style="max-height: 80px;max-width: 80px;" />
            </el-popover>
          </template>

        </el-table-column>




        <el-table-column align="center" label="产品型号">
          <template slot-scope="scope">{{ scope.row.productModel }}</template>
        </el-table-column>
        <el-table-column align="center" label="产品规格">
          <template slot-scope="scope">{{ scope.row.productSize }}</template>
        </el-table-column>

        <el-table-column align="center" label="铺贴空间">
          <template slot-scope="scope">{{ scope.row.space }}</template>
        </el-table-column>

        <!-- fixed="right"  width="200" -->


      </el-table>

    </el-dialog>

    <!-- 查看 -->
    <el-dialog title="查看详情" top="4vh" :visible.sync="editFormVisible1" @close="closeDialog" width="50%">
    
    
      <el-descriptions class="margin-top" :column="1" :size="size" border>
  
    <el-descriptions-item>
      <template slot="label">
        订单编号
      </template>
      {{ special.orderNum }}
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">
        所属门店
      </template>
      {{ special.storeName }}
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">
        业主
      </template>
      {{ special.name }}

    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">
        联系电话
      </template>
      {{ special.phone }}

    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">
        地址
      </template>
      {{ special.address }}

    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">
        施工时间
      </template>
      {{ special.workTime }}

    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">
        施工状态
      </template>
      <el-tag v-if="special.orderStatus == '1'" size="mini" type="primary">未开始</el-tag>
            <el-tag v-if="special.orderStatus == '2'" size="mini" type="danger">进行中</el-tag>
            <el-tag v-if="special.orderStatus == '3'" size="mini" type="warning">已完工</el-tag>

    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">
        施工进度
      </template>
      <el-tag size="mini" v-if="special.schedule == '1'">量好尺寸</el-tag>
            <el-tag size="mini" v-if="special.schedule == '2'">出好设计</el-tag>
            <el-tag size="mini" v-if="special.schedule == '3'">跟好现场</el-tag>
            <el-tag size="mini" v-if="special.schedule == '4'">做好美缝</el-tag>
            <el-tag size="mini" v-if="special.schedule == '5'">搞好清洁</el-tag>
            <el-tag size="mini" v-if="special.schedule == '6'">拍好美片</el-tag>
            <el-tag size="mini" v-if="special.schedule == '7'">已完成</el-tag>

    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">
        预计完成时间
      </template>
      {{ special.predictTime }}

    </el-descriptions-item><el-descriptions-item>
      <template slot="label">
        实际完成时间
      </template>
      {{ special.endTime ?special.endTime : '暂未完成' }}

    </el-descriptions-item><el-descriptions-item>
      <template slot="label">
        团队成员
      </template>
      {{ special.bricklayerName ? special.bricklayerName : '暂无' }}

    </el-descriptions-item>
    
  </el-descriptions>

       
        



        <div slot="footer" class="dialog-footer">
          <el-button size="mini" @click="closeDialog">关闭</el-button>
          <!-- <el-button size="mini" type="primary" class="title" @click="submitForm()">保存</el-button> -->
        </div>

    </el-dialog>
    <!-- 审核 -->
    <el-dialog title="审核" top="4vh" :visible.sync="editFormVisible" @close="closeDialog" width="50%">
      <div class="import-dialog">
        <el-form label-position="rigth" label-width="150px">
          <el-form-item label="订单编号:">
            <div>{{ toexaminesj.orderNum }}</div>

          </el-form-item>
          <el-form-item label="所属门店:">
            <div>{{ toexaminesj.storeName }}</div>

          </el-form-item>
          <el-form-item label="业主:">
            <div>{{ toexaminesj.name }}</div>

          </el-form-item>



          <el-form-item label="联系电话:">
            <div>{{ toexaminesj.phone }}</div>

          </el-form-item>

          <el-form-item label="地址:">
            <div>{{ toexaminesj.address }}</div>

          </el-form-item>


          <el-form-item label="施工状态">
            <el-button plain v-if="toexaminesj.orderStatus == '1'" type="primary" size="mini">门店接单</el-button>
            <el-button plain v-if="toexaminesj.orderStatus == '2'" type="danger" size="mini">总部审核</el-button>
            <el-button plain v-if="toexaminesj.orderStatus == '3'" type="warning" size="mini">施工中</el-button>
            <el-button plain v-if="toexaminesj.orderStatus == '4'" type="success" size="mini">已完工</el-button>

          </el-form-item>
          <el-form-item label="户型:">
            <div>{{ toexaminesj.houseType }}</div>

          </el-form-item>
          <el-form-item label="面积:">
            <div>{{ toexaminesj.houseArea }}m²</div>

          </el-form-item>
          <el-form-item label="预计完成时间:">
            <div>{{ toexaminesj.predictTime }}</div>

          </el-form-item>
          <el-form-item label="监理:">
            <el-input :disabled="true" style="width: 70%;" size="mini" v-model="toexaminesj.regulatorName"
              auto-complete="off" placeholder="请选择总部监理"></el-input>
            <el-button style="margin-left:10px;" type="primary" size="mini" @click="SelectSupervisor(1, 2)">请选择</el-button>
          </el-form-item>
          <el-form-item label="设计师:">
            <div>{{ toexaminesj.stylistName }}</div>

          </el-form-item>
          <el-form-item label="团队成员:">
            <div>{{ toexaminesj.bricklayerName }}</div>

          </el-form-item>
          



        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button v-if=" toexaminesj.schedule == '2' " type="warning" size="mini" @click="addtoReject()">驳回</el-button>
          <el-button size="mini" type="primary" class="title" @click="Reviewedandconfirmed()">通过</el-button>
        </div>

      </div>

    </el-dialog>

    <!-- 总部监理列表 -->
    <el-dialog @close="formCancal1" width="70%" title="总部监理列表" :visible.sync="Supervisor">
      <el-table :row-key="getRowKeys" ref="multipleTable" highlight-current-row :data="SupervisorList" border
        @selection-change="handleSelectionChange">
        <el-table-column :reserve-selection="true" align="center" type="selection" width="55">
        </el-table-column>
        <el-table-column align="center" label="序号" width="95">
          <template slot-scope="scope">{{ scope.$index + 1 }}</template>
        </el-table-column>

        <el-table-column align="center" prop="name" label="头像" width="90">
          <template slot-scope="scope">
            <el-popover placement="bottom" trigger="hover" width="100">
              <img :src="scope.row.photo" width="100%" />
              <img slot="reference" :src="scope.row.photo" :alt="scope.row.photo"
                style="max-height: 40px;max-width: 40px;" />
            </el-popover>
          </template>

        </el-table-column>
        <el-table-column align="center" prop="img" label="微信昵称">
          <template slot-scope="scope">
            {{ scope.row.wxName ? scope.row.wxName : '暂未登录' }}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="img" label="姓名">
          <template slot-scope="scope">
            {{ scope.row.name }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="所属店铺" width="200">
          <template slot-scope="scope">{{ scope.row.storeName ? scope.row.storeName : '暂未登录' }}</template>
        </el-table-column>

        <el-table-column align="center" label="联系方式" width="120">
          <template slot-scope="scope">{{ scope.row.phone }}</template>
        </el-table-column>
        <el-table-column align="center" label="职位" width="200">
          <template slot-scope="scope">
            <div v-if="scope.row.role == '0'">业主</div>
            <div v-if="scope.row.role == '1'">经销商</div>
            <div v-if="scope.row.role == '2'">店长</div>
            <div v-if="scope.row.role == '3'">导购</div>
            <div v-if="scope.row.role == '4'">监理</div>
            <div v-if="scope.row.role == '6'">瓦工</div>
            <div v-if="scope.row.role == '7'">设计师 </div>

          </template>
        </el-table-column>
        <el-table-column align="center" label="审核状态" width="100">
          <template slot-scope="scope">
            <el-button size="mini" type="warning" v-if="scope.row.status == '0'"> 待审核</el-button>
            <el-button size="mini" type="success" v-if="scope.row.status == '1'">已通过</el-button>
            <el-button size="mini" type="danger" v-if="scope.row.status == '-1'">已拒绝</el-button>

          </template>
        </el-table-column>
        <el-table-column align="center" label="服务量">
          <template slot-scope="scope">{{ scope.row.serviceCount }}个</template>
        </el-table-column>


        <el-table-column align="center" label="备注" width="200">
          <template slot-scope="scope">{{ scope.row.remark }}</template>
        </el-table-column>



        <!-- fixed="right"  width="200" -->


      </el-table>

      <div style="display: flex;align-items: center;
  justify-content: space-between;margin-top:30px">
        <el-pagination :current-page.sync="currentPage3" background :page-size="pageSize"
          layout="total,prev,pager,next,jumper" :total="total3" @current-change="handleCurrentChange1" />
        <div slot="footer" class="dialog-footer">
          <el-button size="mini" type="primary" class="title" @click="Determinesupervision()">确定</el-button>
        </div>

      </div>
    </el-dialog>

    <!-- 团队 -->
    <el-dialog @close="formCancal1" width="70%" title="团队" :visible.sync="specialShow1" :close-on-click-modal="false"
      :close-on-press-escape="false">

      <el-table :data="roledata1" border fit highlight-current-row @selection-change="handleSelectionChange">

        <el-table-column align="center" label="序号" width="95">
          <template slot-scope="scope">{{ scope.$index + 1 }}</template>
        </el-table-column>

        <el-table-column align="center" prop="name" label="头像" width="90">
          <template slot-scope="scope">
            <el-popover placement="bottom" trigger="hover" width="100">
              <img :src="scope.row.photo" width="100%" />
              <img slot="reference" :src="scope.row.photo" :alt="scope.row.photo"
                style="max-height: 40px;max-width: 40px;" />
            </el-popover>
          </template>

        </el-table-column>

        <el-table-column align="center" prop="img" label="姓名">
          <template slot-scope="scope">
            {{ scope.row.name }}
          </template>
        </el-table-column>


        <el-table-column align="center" label="联系方式" width="120">
          <template slot-scope="scope">{{ scope.row.phone }}</template>
        </el-table-column>
        <el-table-column align="center" label="职位" width="200">
          <template slot-scope="scope">
            <div v-if="scope.row.role == '0'">业主</div>
            <div v-if="scope.row.role == '1'">经销商</div>

            <div v-if="scope.row.role == '2'">店长</div>
            <div v-if="scope.row.role == '3'">导购</div>
            <div v-if="scope.row.role == '4'">监理</div>
            <div v-if="scope.row.role == '6'">瓦工</div>
            <div v-if="scope.row.role == '7'">设计师 </div>

          </template>
        </el-table-column>

        <el-table-column align="center" label="服务量">
          <template slot-scope="scope">{{ scope.row.serviceCount }}个</template>
        </el-table-column>

        <el-table-column align="center" label="工作年限">
          <template slot-scope="scope">{{ scope.row.workTime }}</template>
        </el-table-column>




      </el-table>


    </el-dialog>

    <!-- 打开记录 -->
    <el-dialog height='300' @close="formCancal1" width="70%" top="4vh" title="打卡记录" :visible.sync="OpenRecordshow"
      :close-on-click-modal="false" :close-on-press-escape="false">
      <div class="import-dialog">
        <el-timeline>
          <el-timeline-item v-for="(activity, index) in OpenRecordlist" :key="index" color="#0bbd87" :size="activity.size"
            :timestamp="activity.createTime" placement="top">

            <el-card>
              <div style="display: flex;align-items: center;
  justify-content: space-between;">

                <div>
                  <h4> {{ activity.schedule }}</h4>
                </div>
                <div style="display: flex;align-items: center;
  ">

                  <div v-if="activity.userName">
                    <h4> {{ activity.userName }}-</h4>
                  </div>
                  <div>
                    <h4 v-if="activity.role == '0'">业主</h4>
                    <h4 v-if="activity.role == '1'">经销商</h4>
                    <h4 v-if="activity.role == '2'">店长</h4>
                    <h4 v-if="activity.role == '3'">导购</h4>
                    <h4 v-if="activity.role == '4'">监理</h4>
                    <h4 v-if="activity.role == '6'">瓦工</h4>
                    <h4 v-if="activity.role == '7'">设计师 </h4>
                  </div>

                </div>
              </div>
              <p v-if="activity.product">应用产品：{{ activity.product }}</p>
              <p v-if="activity.workArea">施工区域：{{ activity.workArea }}</p>

              <p v-if="activity.content">工地情况：{{ activity.content }}</p>


              <div>
                <h4 v-if="activity.image && activity.image.length"> 施工图片</h4>
              </div>
              <el-image :key="index1" v-for="(item, index1) in activity.image"
                style="width: 100px; height: 100px; margin:6px 6px;" :src="item" :preview-src-list="activity.image">
              </el-image>
              <div>
                <h4 v-if="activity.video && activity.video.length"> 施工视频</h4>
              </div>
              <div style="display: flex;align-items: center; flex-wrap: wrap;
  ">
                <div v-for="(video, index2) in activity.video" :key="index2">
                  <video :src="video" style="width: 300px;height:200px; margin:6px 6px;" class="video-avatar"
                    controls></video>
                </div>
              </div>
              <div @click="deleterecord(activity.id)" style="float: right;     padding-bottom: 20px;
  ">

                  <div v-if="activity.userName">
                  </div>
                  <div>
                    <el-button size="mini" type="danger" plain>删除</el-button>
                  </div>

                </div>
            </el-card>


          </el-timeline-item>
        </el-timeline>

      </div>




    </el-dialog>
  </div>
</template>

<script>

import {toReject,delSchedule,updateProjectUser, checkAccept, projectSchedule, projectList, useProduct, hqComfirm, projectTeams, soldOutProject } from '@/api/XM'
import { supervisorList } from '@/api/RY'
import { upload } from '@/api/upload'
import { formatDate_RQ, Url } from '@/utils/time'
import { Loading } from 'element-ui';
// import VueEditor from 'vue-word-editor'
import 'quill/dist/quill.snow.css'
import { log } from 'console';
import { types } from 'util'
export default {
  components: {
    // VueEditor,

  },
  data() {
    return {
      OpenRecordshow: false,
      OpenRecordlist: [],
      OpenRecordID: '',
      OpenRecordcurPage: 1,
      specialShow1: false,
      roledata1: [],
      currentPage3: 1,
      currentPage4: 1,
      total3: 1,
      total4: 1,
      Supervisorid: '',
      SupervisorList: [],
      SupervisorxzList: [],
      Supervisor: false,
      special: {},
      editFormVisible: false,
      editFormVisible1: false,
      toexaminesj: {},
      productlist: [],
      workId: '',
      total1: 1,
      currentPage1: 1,
      specialShow: false,
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }],
      },
      dialogTitle: '新增文件',
      ewmShowgg: false,
      specialShow: false,
      specialShowgg: false,
      formList: {},
      data: [],
      pageSize: 10,
      value1: "",
      value2: "",
      input2: '',
      input3: '',
      input4: '',
      currentPage: 1,
      handleSelectionChangeList: [],
      bookUrllist: [],
      dialogVisibleImg: false,
      formData: {
        startTime: '',
        endTime: ""
      },
      Product: {
        typeId: '',
        name: '',
        linkUrl: '',
        fileType: '',
        fileName: ''
      },
      orderStatus:'',
      total: 1,
      valnum: 1,
      SEDJJXCX_LHFW_UserInformation_HT: {},
      baseUrl: process.env.VUE_APP_BASE_API + "/uploadCloud?filename=11111",
      baseUrl1: process.env.VUE_APP_BASE_API + "/oe_createDatum_.csp",
      //上传后的文件列表
      fileList: [],
      // 允许的文件类型
      fileType: ["pdf", "doc", "docx", "xls", "xlsx", "txt", "png", "jpg", "bmp", "jpeg"],
      // 运行上传文件大小，单位 M
      fileSize: 50,
      // 附件数量限制
      fileLimit: 1,
      //请求头
      headers: { "Content-Type": "multipart/form-data" },
      classify: [
        {
          id:1,name:'未开始'
        },
        {
          id:2,name:'进行中'
        },{
          id:3,name:'已完工'
        }

      ],
      input6:'',
      classify1: [
        {
          id:1,name:'量好尺寸'
        },
        {
          id:2,name:'出好设计'
        },{
          id:3,name:'跟好现场'
        },{
          id:4,name:'做好美缝'
        },{
          id:5,name:'搞好清洁'
        },{
          id:6,name:'拍好美片'
        },{
          id:7,name:'已完成'
        }

      ]
    }
  },
  mounted() {
    this.getGoods();

  },
  created() {
    const SEDJJXCX_LHFW_UserInformation_HT = JSON.parse(decodeURIComponent(window.localStorage.getItem('SEDJJXCX_LHFW_UserInformation_HT')))
    this.SEDJJXCX_LHFW_UserInformation_HT = SEDJJXCX_LHFW_UserInformation_HT;
        this.schedule = this.$route.query.schedule ? this.$route.query.schedule : 0;
        this.orderStatus = this.$route.query.orderStatus ? this.$route.query.orderStatus : 0;
    if(this.schedule){
      this.input6=this.classify1.find(item=>item.id ==this.schedule ).name

    }
  },
  methods: {
    handleSelectionChange1(val) {
      this.handleSelectionChangeList = val;
    },
    formatDate(row, column, cellValue) {
      return cellValue.slice(0, 10);
    },
    formatDate1(row) {
      return row.slice(0, 10);
    },
    Detail(row) {
      this.editFormVisible1 = true;
      this.special = row;
      this.special.workTime = this.formatDate1(this.special.workTime)
      this.special.predictTime = this.formatDate1(this.special.predictTime)
      this.special.endTime = this.formatDate1(this.special.endTime)
    },
    OpenRecord(row) {
      this.OpenRecordID = row.id;
      const params = {
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        id: row.id,
        curPage: this.OpenRecordcurPage
      }


      projectSchedule(params).then(response => {
        this.OpenRecordshow = true;

        if (response.data.code == '1') {
          response.data.list.forEach((arr) => {
            if (arr.image) {
              arr.image = JSON.parse(arr.image)

            }
            if (arr.video) {
              arr.video = JSON.parse(arr.video)

            }
          })
          this.OpenRecordlist = response.data.list
          this.total4 = response.data.totalNum;
          // this.data.forEach((item) => {
          //   this.SupervisorList.forEach((att) => {
          //     if (item.phone == att.phone) {
          //       this.$refs.multipleTable.toggleRowSelection(att, true)
          //     }
          //   })
          // })
        } else {
          this.OpenRecordlist = response.data.list

        }
      })
    },
    team(row) {
      const params = {
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        workId: row.id
      }


      projectTeams(params).then(response => {
        this.specialShow1 = true;

        if (response.data.code == '1') {
          this.roledata1 = response.data.records
          // this.data.forEach((item) => {
          //   this.SupervisorList.forEach((att) => {
          //     if (item.phone == att.phone) {
          //       this.$refs.multipleTable.toggleRowSelection(att, true)
          //     }
          //   })
          // })
        } else {
          this.roledata1 = response.data.records

        }
      })
    },
    Reviewedandconfirmed() {
      const params = {
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        userId: this.Supervisorid,
        id: this.toexaminesj.id,
      }


      hqComfirm(params).then(response => {
        if (response.data.code == '1') {
          this.editFormVisible = false;
          this.currentPage = 1;
          this.getGoods();

          this.$message({
            type: 'success',
            message: '审核通过'

          })
        } else {
          this.$message({
            type: 'warning',
            message: '失败'

          })
        }
      })

    },
    submitForm() {
      const params = {
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        userId: this.Supervisorid,
        id: this.special.id,
      }


      updateProjectUser(params).then(response => {
        if (response.data.code == '1') {
          this.editFormVisible1 = false;
          this.currentPage = 1;
          this.getGoods();

          this.$message({
            type: 'success',
            message: '修改通过'

          })
        } else {
          this.$message({
            type: 'warning',
            message: '失败'

          })
        }
      })

    },
    getRowKeys(row) {//唯一值，一般都未id
      return row.regulator;
    },

    Determinesupervision() {
      if ((this.SupervisorxzList && !this.SupervisorxzList.length)) {
        this.$message({
          type: 'warning',
          message: '请选择总部监理'
        })
      } else {
        // if (this.SupervisorxzList[0].wxName) {
        this.Supervisor = false;
        this.toexaminesj.regulatorName = this.SupervisorxzList[0].name;
        this.special.regulatorName = this.SupervisorxzList[0].name;
        this.Supervisorid = this.SupervisorxzList[0].userId;
        // } else {
        //   this.$message({
        //     type: 'warning',
        //     message: '该总部监理未登录，请先登录！'
        //   })
        // }

      }
    },

    handleSelectionChange(list) {
      console.log(this.$refs.multipleTable.selection);
      // if(this.$refs.multipleTable.selection.wxName){
      if (list.length > 1) {
        this.$refs.multipleTable.clearSelection()
        this.$refs.multipleTable.toggleRowSelection(list[list.length - 1])
      }
      this.SupervisorxzList = list[list.length - 1] ? [list[list.length - 1]] : []
      // }else{
      //   // this.$refs.multipleTable.toggleRowSelection()
      //   this.$message({
      //     type: 'warning',
      //     message: '该总部监理未登录，请先登录！'
      //   })
      // }

    },
    SelectSupervisor(val = 1, type) {
      console.log(type)
      const params = {
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: val,
        role:4
      }


      supervisorList(params).then(response => {
        this.total3 = response.data.totalNum;
        this.Supervisor = true;

        if (response.data.code == '1') {

          this.SupervisorList = response.data.list;
          if (type == 1) {

            this.$nextTick(() => {
              response.data.list.forEach((att) => {
                att.regulator = att.userId
                if (this.special.regulator == att.regulator) {
                  this.$refs.multipleTable.toggleRowSelection(att, true)
                }
              })
            })


          } else {
            this.$nextTick(() => {
              response.data.list.forEach((att) => {
                att.regulator = att.userId
                if (this.toexaminesj.regulator == att.regulator) {
                  this.$refs.multipleTable.toggleRowSelection(att, true)
                }
              })
            })


          }

        } else {
          this.SupervisorList = response.data.list

        }
      })
    },
    closeDialog() {
      this.toexaminesj = {}
      this.special = {}
      this.editFormVisible = false;
      this.editFormVisible1 = false;
    },
    toexamine(row) {
      this.editFormVisible = true;
      this.toexaminesj = row;
    },
    product(row) {
      this.workId = row
      const params = {
        workId: this.workId,
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

      }
      useProduct(params).then(response => {
        this.specialShow = true;
        if (response.data.code === '1') {
          this.productlist = response.data.records
          // this.value2 = '';
          // this.input4 = ''
        } else {
          this.productlist = response.data.records

          // this.value2 = '';
        }
      })
    },
    change2() {
      const params = {
        orderStatus: this.input3,
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: 1
      }



      if (this.input4) {
        params.keyword = this.input4;
      }

      projectList(params).then(response => {
        this.total = response.data.totalNum;
        this.currentPage = 1;
        if (response.data.code === '1') {
          this.data = response.data.list
          // this.value2 = '';
          this.input4 = ''
        } else {
          this.data = response.data.list

          // this.value2 = '';
          this.input4 = ''
        }
      })
    },
    change7() {
      const params = {
        schedule: this.input6,
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: 1
      }



      if (this.input4) {
        params.keyword = this.input4;
      }

      projectList(params).then(response => {
        this.total = response.data.totalNum;
        this.currentPage = 1;
        if (response.data.code === '1') {
          this.data = response.data.list
          // this.value2 = '';
        } else {
          this.data = response.data.list

          // this.value2 = '';
        }
      })
    },
    //上传文件之前
    beforeUpload(file) {
      this.Product.fileName = file.name;
      if (file.type != "" || file.type != null || file.type != undefined) {
        //截取文件的后缀，判断文件类型
        const FileExt = file.name.replace(/.+\./, "").toLowerCase();
        //计算文件的大小
        const isLt5M = file.size / 1024 / 1024 < 500; //这里做文件大小限制
        //如果大于50M
        if (!isLt5M) {
          this.$message.error('上传文件大小不能超过 500MB!')
          return false;
        }
        //如果文件类型不在允许上传的范围内
        if (this.fileType.includes(FileExt)) {
          return true;
        }
        else {
          this.$message.error("上传文件格式不正确!");
          return false;
        }
      }
    },
    //上传了的文件给移除的事件，由于我没有用到默认的展示，所以没有用到
    handleRemove() {
    },
    //这是我自定义的移除事件
    handleClose(i) {
      this.fileList.splice(i, 1);//删除上传的文件
      if (this.fileList.length == 0) {//如果删完了
        this.fileflag = true;//显示url必填的标识
        this.$set(this.rules.url, 0, { required: true, validator: this.validatorUrl, trigger: 'blur' })//然后动态的添加本地方法的校验规则
      }
    },
    //超出文件个数的回调
    handleExceed() {
      this.$message({
        type: 'warning',
        message: '超出最大上传文件数量的限制！'
      }); return
    },
    //上传文件的事件
    uploadFile(item) {
      this.loading = Loading.service({
        lock: true,
        text: '文件上传中........',
        // background: 'rgba(0, 0, 0, 0.7)',
        target: document.querySelector('#loadingDiv')
      })
      //上传文件的需要formdata类型;所以要转
      let FormDatas = new FormData()
      FormDatas.append('file', item.file);
      upload(FormDatas).then(res => {
        if (res.data.code == '0') {
          setTimeout(() => {
            this.loading.close()

          }, 2000);
          this.Product.linkUrl = res.data.yunUrl;
          this.Product.fileType = res.data.ext;
          // this.Product.filename=res.data.fileNameOld;
        }
      })

      // 	})
    },
    //上传成功后的回调
    handleSuccess() {

    },
    wangEditorChange(val) {
      this.Product.detail = val;
    },
    // 关闭
    formCancal1() {
      // this.Product.typeId='';
      // this.Product.name='';
      // this.Product.linkUrl='';
      // this.Product.fileType='';
      this.Product = {}
      // this.Product.filename='';
    },
    // 添加
    increase() {
      this.specialShow = true;
    },
    // 重新加载
    Reload() {
      this.value1 = '';
      this.value2 = '';
      this.input2 = '';
      this.input3 = '';
      this.input4 = '';
      this.input5 = '';
      this.input6 = '';
      this.schedule = '';
      this.currentPage = 1;
      this.getGoods(1);

    },
    fetchData(val = 1) {
      const params = {
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: val
      }
      allDatumType(params).then(response => {
        if (response.data.code == '1') {
          this.classify = response.data.list
        }
      })
    },

    getGoods(val = 1) {
      const params = {
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: val
      }
      if (this.value2 && this.value2[0] && this.value2[1]) {
        params.starTime = formatDate_RQ(this.value2[0]);
        params.endTime = formatDate_RQ(this.value2[1]);
      }

      if (this.input3) {
        params.catId = this.input3;
      }
      if (this.input4) {
        params.name = this.input4;
      }
      if (this.input2) {
        params.status = this.input2;
      }
      if(this.schedule){
        params.schedule = this.schedule;
        
      }
      if(this.orderStatus){
        params.orderStatus = this.orderStatus;

      }

      projectList(params).then(response => {
        this.total = response.data.totalNum;
        if (response.data.code == '1') {
          this.data = response.data.list
        } else {
          this.data = response.data.list

        }
      })
    },
    handleCurrentChange1(val) {
      this.valnum = val;
      this.getGoods(val)

    },
    handleCurrentChange(val) {
    },
    addtoReject(){
      this.$confirm('此操作将驳回项目', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          const params = {
                                dbName: window.localStorage.getItem('JJHTDBnmame'),

            idList: this.toexaminesj.id
          }
          toReject(params).then(response => {
            if (response.data.code == '1') {
              this.$message.success('驳回成功')
              this.editFormVisible=false;
              this.getGoods()
              this.currentPage=1;
            }
          })
          .catch((err) => {
          console.log(err);
          
        })
        })
        .catch((err) => {
          console.log(err);
          this.$message({
            type: 'info',
            message: '已取消驳回'
          })
        })
    },
    deleterecord(row){
      this.$confirm('此操作将永久删除记录', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          const params = {
                                dbName: window.localStorage.getItem('JJHTDBnmame'),

            id: row
          }
          delSchedule(params).then(response => {
            if (response.data.code == '1') {
              this.$message.success('删除成功')
              this.OpenRecordshow=false;
              this.getGoods()
              this.currentPage=1;
            }
          })
        })
        .catch((err) => {
          console.log(err);
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    // 删除文件
    handleDelete(row) {
      this.$confirm('此操作将永久删除文件', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          const params = {
                                dbName: window.localStorage.getItem('JJHTDBnmame'),

            listid: row
          }
          DeleteGoods(params).then(response => {
            if (response.data.code == '1') {
              this.$message.success('删除成功')

              this.getGoods()
            }
          })
        })
        .catch((err) => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },


    handlePreview(file) {
      this.Product.coverPic = file.url
      this.dialogVisibleImg = true
    },
    handlePreview1(file) {
      this.specifications.url = file.url
      this.dialogVisibleImg = true
    },
    handleRemove1(file, fileList) {
      // 移除

    },




    //添加文件
    RechargeRole() {

      if (this.Product.catld == '') {
        this.$message({
          showClose: true,
          message: '请选择分类！',
          type: 'warning'
        });
        return
      }
      if (this.Product.name == '') {
        this.$message({
          showClose: true,
          message: '请输入文件名称！',
          type: 'warning'
        });
        return
      }

      if (this.Product.linkUrl == '') {
        this.$message({
          showClose: true,
          message: '请上传对应文件！',
          type: 'warning'
        });
        return
      }
      console.log(this.Product);
      var params = new URLSearchParams();
      params.append('name', this.Product.name);
      params.append('linkUrl', this.Product.linkUrl);
            params.append('dbName', window.localStorage.getItem('JJHTDBnmame'));

      params.append('typeId', this.Product.typeId);
      params.append('fileType', this.Product.fileType);
      params.append('fileName', this.Product.fileName);
      params.append('id', this.Product.id ? this.Product.id : '');
      this.axios.post(this.baseUrl1, params,).then(res => {
        if (res.data.code == '1') {
          this.$message({
            type: 'success',
            message: this.dialogTitle == '修改文件' ? '修改成功' : '添加成功'

          })
          this.getGoods(this.valnum)
          this.specialShowgg = false
          this.specialShow = false;
          this.Product = {}
          this.bookUrllist = []
          this.tableOption = [];
          this.input5 = '';
        } else {
          this.$message({
            type: 'warning',
            message: this.dialogTitle == '修改文件' ? '修改失败' : '添加失败'

          })
        }
      }).catch(err => {
      })
      return

    },
    // 修改文件
    handleEditzt(row) {
      this.wangEditorDetail = row.detail
      this.dialogTitle = '修改文件';
      this.specialShow = true;
      this.Product = row;
      this.Product.fileName = row.linkUrl
      console.log(row);
    },

    // 时间筛选
    change() {
      this.formData.startTime = formatDate_RQ(this.value2[0])
      this.formData.endTime = formatDate_RQ(this.value2[1])
      const params = {
        dbName: "mati", curPage: 1,
        starTime: this.formData.startTime,
        endTime: this.formData.endTime,
      }
      if (this.input2) {
        params.status = this.input2;
      }
      if (this.input3) {
        params.catId = this.input3;
      }
      if (this.input4) {
        params.name = this.input4;
      }
      projectList(params).then(response => {
        this.total = response.data.totalNum;
        this.currentPage = 1;
        if (response.data.code === '1') {
          this.formData.startTime = ''
          this.formData.endTime = ''
          this.data = response.data.list
          // this.value2 = '';
        } else {
          this.data = response.data.list

          // this.value2 = '';
        }
      })
    },

    // 名字筛选
    change3() {
      const params = {
        keyword: this.input4,
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: 1
      }
      if (this.value2 && this.value2[0] && this.value2[1]) {
        params.starTime = formatDate_RQ(this.value2[0]);
        params.endTime = formatDate_RQ(this.value2[1]);
      }

      if (this.input2) {
        params.status = this.input2;
      }
      if (this.input3) {
        params.catId = this.input3;
      }

      projectList(params).then(response => {
        this.total = response.data.totalNum;
        this.currentPage = 1;
        if (response.data.code === '1') {
          this.data = response.data.list
          // this.value2 = '';
          this.input4 = ''
        } else {
          this.data = response.data.list

          // this.value2 = '';
          this.input4 = ''
        }
      })
    },

    // 批量上架
    grounding() {
      if (this.handleSelectionChangeList && this.handleSelectionChangeList.length) {
        const list = [];
        for (var i of this.handleSelectionChangeList) {
          list.push(i.id)
        }
        var a = list.join(",");
        const params = {
                              dbName: window.localStorage.getItem('JJHTDBnmame'),

          idList: a,
          status: '0'

        }
        soldOutProject(params).then(response => {
          if (response.data.code == '1') {
            this.$message({
              type: 'success',
              message: response.data.msg

            })
            this.getGoods();


          }
        })
      } else {
        this.$message({
          type: 'warning',
          message: '请选择你上架的选项'
        })
      }
    },


    // 批量下架
    Offshelf() {
      if (this.handleSelectionChangeList && this.handleSelectionChangeList.length) {
        const list = [];
        for (var i of this.handleSelectionChangeList) {
          list.push(i.id)
        }
        var a = list.join(",");
        const params = {
                              dbName: window.localStorage.getItem('JJHTDBnmame'),

          idList: a,
          status: '1',
        }
        soldOutProject(params).then(response => {
          if (response.data.code == '1') {
            this.$message({
              type: 'success',
              message: response.data.msg

            })
            this.getGoods();


          }
        })
      } else {
        this.$message({
          type: 'warning',
          message: '请选择下架的选项'
        })
      }
    },
    // 确定验收
    Confirmacceptance() {
      if (this.handleSelectionChangeList && this.handleSelectionChangeList.length) {
        const list = [];
        for (var i of this.handleSelectionChangeList) {
          list.push(i.id)
          if (i.schedule != 5) {
            this.$message({
              type: 'warning',
              message: i.community + '未验收完成!'

            })
            return
          }

        }

        var a = list.join(",");
        const params = {
                              dbName: window.localStorage.getItem('JJHTDBnmame'),

          idList: a,
        }
        checkAccept(params).then(response => {
          if (response.data.code == '1') {
            this.$message({
              type: 'success',
              message: response.data.msg

            })
            this.getGoods();


          }
        })
      } else {
        this.$message({
          type: 'warning',
          message: '请选择确定验收的选项'
        })
      }
    },
    handleCheck(index, row) {
      this.IMGindex = index;
    },

    change5() {
      if (this.input5) {
        this.tableOption.push({
          'label': this.input5
        })

        // this.tableData[0].attr_list = this.tableOption
        // return
        const params = {
          dbName: 'mati',
          attrGroup: this.input5
        }
        QueryAttrGroup(params).then(response => {
          if (response.data.code == '1') {
            this.attrGroup_ID.push(response.data.record.id);
            for (let i in this.tableData) {
              this.tableData[i].attr_list.push({
                attr_group_id: response.data.record.id,//表头id
                attr_group_name: this.input5,//表头
                attr_id: "",
                attr_name: ''
              })
            }

          }
        })
      } else {
        this.$message({
          type: 'warning',
          message: '请填写规格'
        })
      }

    },
    // change6(index, row) {
    //   const params = {
    //     dbName: 'mati',
    //     attrGroup: 5,
    //     attrName: row.specifications
    //   }
    //   QueryAttr(params).then(response => {
    //     if (response.data.code == '1') {
    //       this.tableData[index].attr_list.push({
    //         attr_id: response.data.record.id,
    //         attr_name: row.specifications,
    //       })
    //     }
    //   })
    // },
    change6(index, i) {
      console.log(this.attrGroup_ID);
      const params = {
        dbName: 'mati',
        attrGroup: this.attrGroup_ID[i], // this.attrGroupID,
        // attrGroup: this.attrGroupID, // this.attrGroupID,
        attrName: this.tableData[index].attr_list[i].attr_name
      }
      QueryAttr(params).then(response => {
        if (response.data.code == '1') {
          this.tableData[index].attr_list[i].attr_id = response.data.record.id;
        }
      })
    },






  },
};
</script>

<style>
.el-form-item__content,
.el-form-item__label{
/* font-size:26px */

}

</style>
<style lang="scss" scoped>

.el-row {
  margin-bottom: 20px;
}

.el-row:last-child {
  margin-bottom: 0;
}

.el-tag+.el-tag {
  margin-left: 10px;
}



.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}

.input-new-tag {
  width: 300px;
  margin-left: 10px;
  vertical-align: bottom;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.el-button--medium {
  margin-top: 10px;
  margin-left: 600px;
  height: 45px;
  width: 100px;
}

.el-input_ss {
  width: 200px;
}

.el-input_ss1 {

  margin-top: 6px;
}

.btn_jia {
  position: relative;
  margin-left: 8px;
  margin-top: 20px;
}

.btn_jia:hover {
  font-weight: 600;
  text-decoration: underline;
}

.btn_jia::after {
  content: "";
  width: 2px;
  height: 20px;
  background: #000;
  position: absolute;
  top: 10px;
  right: 33px;
}

.status_shop {
  margin-right: 8px;
}

// 分页
.paging {
  width: 100%;
  height: 100%;
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

// 功能按钮
.function_btn_box {
  width: 100%;
  display: flex;
  margin-right: 10px;
  margin-bottom: 20px;
}

.input-with-select {
  margin-left: 50px;
}

.float_rigth {
  float: right;
}

.import-dialog {
  height: auto;
  max-height: 80vh;
  overflow-y: auto;
}
.dialog-footer{
  
}
// .el-input.inp {
//   width: auto !important;
// }
</style>
