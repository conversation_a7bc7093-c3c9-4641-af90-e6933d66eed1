<template>
  <div class="app-container">

    <!-- 添加按钮 -->
    <el-form :inline="true" class="user-search">
      <el-form-item>
        <el-button size="mini" @click="handleEdit1" type="primary">添加</el-button>

      </el-form-item>
   
      <el-form-item>
        <el-input size="mini" placeholder="请输入内容" v-model="input4" class="el-input_ss1">
          <el-button size="mini" slot="append" icon="el-icon-search" @click="change3"></el-button>
        </el-input>
      </el-form-item>
      <el-form-item>
        <!-- <el-button size="mini" type="primary" icon="el-icon-plus" @click="increase()">添加</el-button> -->

        <el-button size="mini" type="primary" @click="Reload" icon="el-icon-refresh-left">重新加载</el-button>
        <!-- <el-button @click="leading">批量导出</el-button> -->




      </el-form-item>
    </el-form>
    <!-- 表格 -->
    <el-table :data="data" border fit highlight-current-row>
      <el-table-column align="center" label="序号"  width="80">
        <template slot-scope="scope">{{ scope.$index + 1 }}</template>
      </el-table-column>
      <el-table-column align="center" label="标题" width="500">
        <template slot-scope="scope">{{ scope.row.title }}</template>

  
      </el-table-column>
     
      <el-table-column align="center" prop="name" label="封面图片">
        <template slot-scope="scope">
          <el-popover placement="bottom" trigger="hover" width="300">
            <img :src="scope.row.coverPic" width="100%" />
            <img slot="reference" :src="scope.row.coverPic" :alt="scope.row.coverPic"
              style="max-height: 40px;max-width:40px; padding: 5px" />
          </el-popover>
        </template>

      </el-table-column>
      <el-table-column align="center" label="观看量">
        <template slot-scope="scope">{{ scope.row.isDelete }}</template>

  
      </el-table-column>   <el-table-column align="center" label="收藏量">
        <template slot-scope="scope">{{ scope.row.collectCount }}</template>

  
      </el-table-column>

      <el-table-column align="center" prop="name" label="排序">
        <template slot-scope="scope">{{ scope.row.sort }}</template>

      </el-table-column>

      <el-table-column align="center" width="200" prop="name" label="创建时间">
        <template slot-scope="scope">{{ scope.row.createTime }}</template>

      </el-table-column>
      <el-table-column fixed="right" width="200" label="操作" align="center">
        <template slot-scope="scope">
          <el-button size="mini" @click="handleEdit(scope.$index, scope.row)">编辑</el-button>

          <el-button size="mini" @click="deleteUser(scope.$index, scope.row)" type="danger">删除</el-button>

        </template>
      </el-table-column>
      </el-table-column>
    </el-table>
    <el-row>
      <el-pagination :current-page.sync="currentPage" background style="margin-top:30px" :page-size="pageSize"
        layout="total,prev,pager,next,jumper" :total="total" @current-change="handleCurrentChange1" />

    </el-row>
    <!-- 编辑界面 -->
    <el-dialog :title="title" :visible.sync="editFormVisible" @close="closeDialog" width="50%">
      <el-form label-position="rigth" label-width="100px">
        
        <el-form-item label="标题名称:">
          <el-input style="width: 100%;" size="mini" v-model="special.title" auto-complete="off"
            placeholder="请输入标题"></el-input>
        </el-form-item>
        <el-form-item label="封面图:">
          <el-upload action="#" list-type="picture-card" :on-preview="handlePreview" :on-remove="handleRemove1"
            :auto-upload="false" :file-list="bookUrllist" multiple :on-change="handleSmallPicSuccess">
            <i class="el-icon-plus" />
          </el-upload>
          <el-dialog :visible.sync="dialogVisibleImg" top="0" center :modal="false">
            <img width="100%" :src="special.coverPic" alt>
          </el-dialog>
        </el-form-item>


      
        <el-form-item label="排序:">
          <el-input style="width: 100%;" size="mini" v-model="special.sort" auto-complete="off"
            placeholder="请输入排序"></el-input>
        </el-form-item>
        <el-form-item label="详情:">
          <wangEditor v-model="wangEditorDetail" :isClear="isClear" @change="wangEditorChange"></wangEditor>

            </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="closeDialog">取消</el-button>
        <el-button size="mini" type="primary" class="title" @click="submitForm()">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {  createWorkerStory,workerStorys,delStorys} from '@/api/GCS'
import { upload } from '@/api/upload'
import { Loading } from 'element-ui';
import wangEditor from "@/components/wangEditor/wangEditor.vue";

export default {
  components: {
    wangEditor
  },
  data() {
    return {
      isClear: false,//设置为true的时候，这个可以用this.wangEditorDetail=''来替代
      wangEditorDetail: "",
      editFormVisible: false,
      dialogVisibleImg: false,
      currentPage: 1,
      pageSize: 10,
      total: 1,
      data: [],
      title: '添加',
      special: {
        id: '',
        coverPic: "",
        status: 1,
        sortId: '',
        remark: '',
        integralNum: '',
        productStock: '',
        proInfo: '',
        peoNum: "",
        quota: '',
        productCate: '',
        cateId: '',
        content: '',
        sort: '',
      },
      modulelist: [

      ],
      SEDJJXCX_LHFW_UserInformation_HT: {},
      bookUrllist: [],
      bookUrllist1: [],
      input3: '',
      input4: '',
      baseUrl : process.env.VUE_APP_BASE_API + "/oe_createWorkerStory_.csp"

    }
  },
  created() {
    const SEDJJXCX_LHFW_UserInformation_HT = JSON.parse(decodeURIComponent(window.localStorage.getItem('SEDJJXCX_LHFW_UserInformation_HT')))
    this.SEDJJXCX_LHFW_UserInformation_HT = SEDJJXCX_LHFW_UserInformation_HT;
    this.fetchData()
  },
  methods: {
    wangEditorChange(val) {
      this.special.content=val;
    },
    Reload() {
      this.input3 = ''
      this.input4 = ''
      const params = {
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: 1
      }
      workerStorys(params).then(response => {
        this.total = response.data.totalNum

        if (response.data.code == '1') {
          this.data = response.data.list
        } else {
          this.data = response.data.list

        }
      }).catch((err) => {
        console.log();
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    change3() {
      const params = {
        keyword: this.input4,
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: 1
      }
      

      workerStorys(params).then(response => {
        this.total = response.data.totalNum

        if (response.data.code == '1') {
          this.data = response.data.list
        } else {
          this.data = response.data.list
          // this.input4 = ''
        }
      }).catch((err) => {
        console.log();
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    change2() {
      const params = {
        cateId: this.input3,
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: 1
      }
      if (this.input4) {
        params.keyword = this.input4;
      }

      workerStorys(params).then(response => {
        if (response.data.code == '1') {
          this.total = response.data.totalNum

          this.data = response.data.list
        } else {
          this.data = response.data.list
          this.input4 = ''
        }
      }).catch((err) => {
        console.log();
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
   
    handleCurrentChange1(val) {
      this.fetchData(val)
    },
    fetchData(val = 1) {
      const params = {
                            dbName: window.localStorage.getItem('JJHTDBnmame'),

        curPage: val
      }
      workerStorys(params).then(response => {
        this.total = response.data.totalNum
        if (response.data.code == '1') {
          this.data = response.data.list
        } else {
          this.data = response.data.list

        }
      })
    },
    handleEdit1() {
      this.title = '添加';
      this.editFormVisible = true;
      this.special.id = '';
    },
    handleEdit(index, row) {
      console.log(row);
      this.editFormVisible = true;
      this.title = '修改'
      this.special = row;
      this.wangEditorDetail = row.content
      if (row.coverPic) {
        this.bookUrllist = [
          {
            url: row.coverPic
          }
        ]
      }
    

      return
      if (row != undefined && row != 'undefined') {
        this.title = '修改'
        this.special.id = row.id
        this.special.name = row.name
      } else {
        this.title = '添加'
        this.special.id = ''
      }
    },
    // 关闭编辑、增加弹出框
    closeDialog() {

      this.editFormVisible = false;
      this.deptName = '';
      this.special = {};
      this.bookUrllist = []
      this.wangEditorDetail='';    },
    // 编辑、增加页面保存方法
    submitForm() {
    

      if (this.special.title == '') {
        this.$message({
          showClose: true,
          message: '请输入名称！',
          type: 'warning'
        });
        return
      }

      
      if (this.special.sort == '') {
        this.$message({
          showClose: true,
          message: '请输入排序！',
          type: 'warning'
        });
        return
      }

     
      if (this.special.coverPic == '') {
        this.$message({
          showClose: true,
          message: '请上传产品封面图！',
          type: 'warning'
        });
        return
      }

      if (this.special.content == '') {
        this.$message({
          showClose: true,
          message: '请上传专题内容！',
          type: 'warning'
        });
        return
      }
     
      var params = new URLSearchParams();
      params.append('sort', this.special.sort);
      params.append('title', this.special.title);
      params.append('coverPic', this.special.coverPic);
      params.append('content', this.special.content);
            params.append('dbName', window.localStorage.getItem('JJHTDBnmame'));

      params.append('id', this.special.id ? this.special.id : '');
      this.axios.post(this.baseUrl, params,).then(res => {
        if (res.data.code == '1') {
          this.editFormVisible = false
          this.special.title = '';
          this.special.sort = '';
          this.wangEditorDetail=''
          this.bookUrllist = []
          this.fetchData()
          this.$message({
            type: 'success',
            message: this.title == '修改' ? '修改成功！' : '创建成功！'
          })

        } else {
          this.$message({
            type: 'warning',
            message: this.dialogTitle == '修改' ? '修改失败' : '添加失败'

          })
        }
      },()=>{
        this.Product.id = res.data.id;
      }).catch(err => { 错误处理逻辑 })
   


    },

    deleteUser(index, row) {
      this.$confirm('此操作将永久删除, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const params = {
          id: row.id,
                              dbName: window.localStorage.getItem('JJHTDBnmame'),

        }; delStorys(params).then(response => {
          if (response.data.code == '1') {
            this.fetchData()
          }
        })
        this.$message({
          type: 'success',
          message: '删除成功!'
        });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });

    },
    handlePreview(file) {
      // 放大
      this.dialogVisibleImg = true
      this.special.coverPic = file.url
    },
    handleRemove1(file, fileList) {
      // 移除
      const { uid } = file
      const { powUrl } = this.form
      const newPowUrl = powUrl.filter(v => {
        return uid !== powUrl.uid
      })
    },
    handleSmallPicSuccess(res, file, fileList) {
      if (file && file.length == 2) {
        file.shift()
      }
      this.uploadImg(res, 1);

    },
    handleSmallPicSuccess1(res, file, fileList) {
      if (file && file.length == 2) {
        file.shift()
      }
      this.uploadImg(res, 2);

    }
    , uploadImg(file, type) {
      this.loading = Loading.service({
        lock: true,
        text: '图片上传中........',
        // background: 'rgba(0, 0, 0, 0.7)',
        target: document.querySelector('#loadingDiv')
      })
      let formData = new FormData()
      formData.append('file', file.raw)
      upload(formData).then(response => {
        setTimeout(() => {
          this.loading.close()

        }, 2000);
        if (type == 1) {
          this.special.coverPic = response.data.yunUrl;

        } else {
          this.special.content = response.data.yunUrl;

        }

      }).catch((err) => {
        console.log(err);

      });


    },
  }
}
</script>

<style lang="scss" scoped>
.el-row {
  margin-bottom: 20px;
}

.el-row:last-child {
  margin-bottom: 0;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.el-button--medium {
  margin-top: 10px;
  margin-left: 600px;
  height: 45px;
  width: 100px;
}

.el-input {
  width: 200px;
}

// .el-input.inp {
//   width: auto !important;
// }
.el-input_ss1 {

  margin-top: 6px;
}</style>
